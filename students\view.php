<?php
session_start();

if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit();
}

include '../config/database.php';

$student_id = (int)($_GET['id'] ?? 0);

if (!$student_id) {
    header('Location: list.php?error=invalid_id');
    exit();
}

// Get student with creator info
$sql = "SELECT s.*, u.full_name as created_by_name 
        FROM students s 
        LEFT JOIN users u ON s.created_by = u.id 
        WHERE s.id = ?";

// If not admin, only show own students
if ($_SESSION['user_role'] !== 'admin') {
    $sql .= " AND s.created_by = ?";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$student_id, $_SESSION['user_id']]);
} else {
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$student_id]);
}

$student = $stmt->fetch();

if (!$student) {
    header('Location: list.php?error=not_found');
    exit();
}

include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include '../includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">Student Details</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <a href="list.php" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> Back to List
                        </a>
                        <?php if ($_SESSION['user_role'] === 'admin' || $student['created_by'] == $_SESSION['user_id']): ?>
                        <a href="edit.php?id=<?php echo $student['id']; ?>" class="btn btn-sm btn-warning">
                            <i class="fas fa-edit"></i> Edit
                        </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-lg-8">
                    <div class="card shadow mb-4">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-user"></i> <?php echo htmlspecialchars($student['student_name']); ?>
                                <span class="badge bg-light text-dark ms-2">
                                    Class <?php echo $student['class']; ?>-<?php echo $student['section']; ?>
                                </span>
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label text-muted">
                                        <i class="fas fa-user"></i> Student Name
                                    </label>
                                    <div class="form-control-plaintext fw-bold">
                                        <?php echo htmlspecialchars($student['student_name']); ?>
                                    </div>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label class="form-label text-muted">
                                        <i class="fas fa-male"></i> Father's Name
                                    </label>
                                    <div class="form-control-plaintext">
                                        <?php echo htmlspecialchars($student['father_name']); ?>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label class="form-label text-muted">
                                        <i class="fas fa-graduation-cap"></i> Class
                                    </label>
                                    <div class="form-control-plaintext">
                                        <span class="badge bg-info fs-6">Class <?php echo $student['class']; ?></span>
                                    </div>
                                </div>
                                
                                <div class="col-md-4 mb-3">
                                    <label class="form-label text-muted">
                                        <i class="fas fa-layer-group"></i> Section
                                    </label>
                                    <div class="form-control-plaintext">
                                        <span class="badge bg-secondary fs-6">Section <?php echo $student['section']; ?></span>
                                    </div>
                                </div>
                                
                                <div class="col-md-4 mb-3">
                                    <label class="form-label text-muted">
                                        <i class="fas fa-venus-mars"></i> Gender
                                    </label>
                                    <div class="form-control-plaintext">
                                        <?php if ($student['gender']): ?>
                                            <i class="fas fa-<?php echo $student['gender'] == 'Male' ? 'mars text-primary' : ($student['gender'] == 'Female' ? 'venus text-danger' : 'genderless text-info'); ?>"></i>
                                            <?php echo $student['gender']; ?>
                                        <?php else: ?>
                                            <span class="text-muted">Not specified</span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label text-muted">
                                        <i class="fas fa-mobile-alt"></i> Mobile Number
                                    </label>
                                    <div class="form-control-plaintext">
                                        <?php if ($student['mobile_number']): ?>
                                            <a href="tel:<?php echo $student['mobile_number']; ?>" class="text-decoration-none">
                                                <i class="fas fa-phone text-success"></i> 
                                                <?php echo chunk_split($student['mobile_number'], 5, ' '); ?>
                                            </a>
                                        <?php else: ?>
                                            <span class="text-muted">Not provided</span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label class="form-label text-muted">
                                        <i class="fas fa-calendar-alt"></i> Admission Date
                                    </label>
                                    <div class="form-control-plaintext">
                                        <?php echo $student['admission_date'] ? date('F j, Y', strtotime($student['admission_date'])) : 'Not specified'; ?>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label text-muted">
                                        <i class="fas fa-flag"></i> Status
                                    </label>
                                    <div class="form-control-plaintext">
                                        <span class="badge bg-<?php echo $student['status'] == 'Active' ? 'success' : ($student['status'] == 'Left' ? 'danger' : 'warning'); ?> fs-6">
                                            <?php echo $student['status']; ?>
                                        </span>
                                    </div>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label class="form-label text-muted">
                                        <i class="fas fa-user-plus"></i> Created By
                                    </label>
                                    <div class="form-control-plaintext">
                                        <?php echo htmlspecialchars($student['created_by_name']); ?>
                                        <br><small class="text-muted">
                                            <?php echo date('M j, Y g:i A', strtotime($student['created_at'])); ?>
                                        </small>
                                    </div>
                                </div>
                            </div>

                            <?php if ($student['address']): ?>
                            <div class="mb-3">
                                <label class="form-label text-muted">
                                    <i class="fas fa-map-marker-alt"></i> Address
                                </label>
                                <div class="form-control-plaintext">
                                    <?php echo nl2br(htmlspecialchars($student['address'])); ?>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-4">
                    <?php if ($student['photo_path']): ?>
                    <div class="card shadow mb-4">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-camera"></i> Student Photo
                            </h6>
                        </div>
                        <div class="card-body text-center">
                            <img src="../uploads/students/<?php echo htmlspecialchars($student['photo_path']); ?>" 
                                 alt="Student Photo" 
                                 class="img-fluid rounded shadow"
                                 style="max-height: 300px;">
                        </div>
                    </div>
                    <?php endif; ?>
                    
                    <div class="card shadow">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-cog"></i> Actions
                            </h6>
                        </div>
                        <div class="card-body">
                            <?php if ($_SESSION['user_role'] === 'admin' || $student['created_by'] == $_SESSION['user_id']): ?>
                            <a href="edit.php?id=<?php echo $student['id']; ?>" class="btn btn-warning btn-block mb-2">
                                <i class="fas fa-edit"></i> Edit Student
                            </a>
                            <a href="delete.php?id=<?php echo $student['id']; ?>" 
                               class="btn btn-danger btn-block mb-2 delete-btn">
                                <i class="fas fa-trash"></i> Delete Student
                            </a>
                            <?php endif; ?>
                            
                            <a href="list.php" class="btn btn-secondary btn-block mb-2">
                                <i class="fas fa-list"></i> All Students
                            </a>
                            
                            <a href="../export/students.php?class=<?php echo $student['class']; ?>&section=<?php echo $student['section']; ?>" 
                               class="btn btn-success btn-block">
                                <i class="fas fa-download"></i> Export Class Data
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
