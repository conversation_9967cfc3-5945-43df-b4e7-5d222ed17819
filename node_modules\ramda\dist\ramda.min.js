!function(t,n){"object"==typeof exports&&"undefined"!=typeof module?n(exports):"function"==typeof define&&define.amd?define(["exports"],n):n((t=t||self).R={})}(this,function(t){"use strict";function s(t){return(s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function f(t){return null!=t&&"object"===s(t)&&!0===t["@@functional/placeholder"]}function o(r){return function t(n){return 0===arguments.length||f(n)?t:r.apply(this,arguments)}}function n(e){return function t(n,r){switch(arguments.length){case 0:return t;case 1:return f(n)?t:o(function(t){return e(n,t)});default:return f(n)&&f(r)?t:f(n)?o(function(t){return e(t,r)}):f(r)?o(function(t){return e(n,t)}):e(n,r)}}}var r=n(function(t,n){return+t+ +n});function c(t,n){var r,e=(t=t||[]).length,u=(n=n||[]).length,i=[];for(r=0;r<e;)i[i.length]=t[r],r+=1;for(r=0;r<u;)i[i.length]=n[r],r+=1;return i}function l(t,s){switch(t){case 0:return function(){return s.apply(this,arguments)};case 1:return function(t){return s.apply(this,arguments)};case 2:return function(t,n){return s.apply(this,arguments)};case 3:return function(t,n,r){return s.apply(this,arguments)};case 4:return function(t,n,r,e){return s.apply(this,arguments)};case 5:return function(t,n,r,e,u){return s.apply(this,arguments)};case 6:return function(t,n,r,e,u,i){return s.apply(this,arguments)};case 7:return function(t,n,r,e,u,i,o){return s.apply(this,arguments)};case 8:return function(t,n,r,e,u,i,o,c){return s.apply(this,arguments)};case 9:return function(t,n,r,e,u,i,o,c,a){return s.apply(this,arguments)};case 10:return function(t,n,r,e,u,i,o,c,a,f){return s.apply(this,arguments)};default:throw Error("First argument to _arity must be a non-negative integer no greater than ten")}}function p(o,c,a){return function(){for(var t=[],n=0,r=o,e=0,u=!1;e<c.length||n<arguments.length;){var i;c.length<=e||f(c[e])&&n<arguments.length?(i=arguments[n],n+=1):i=c[e],f(t[e]=i)?u=!0:r-=1,e+=1}return u||0<r?l(Math.max(0,r),p(o,t,a)):a.apply(this,t)}}var a=n(function(t,n){return 1===t?o(n):l(t,p(t,[],n))}),e=o(function(u){return a(u.length,function(){var n=0,r=arguments[0],e=arguments[arguments.length-1],t=Array.prototype.slice.call(arguments,0);return t[0]=function(){var t=r.apply(this,c(arguments,[n,e]));return n+=1,t},u.apply(this,t)})}),u=o(function(u){return a(u.length,function(){var n=arguments[0],r=arguments[arguments.length-1],e=r.length-1,t=Array.prototype.slice.call(arguments,0);return t[0]=function(){var t=n.apply(this,c(arguments,[e,r]));return e-=1,t},u.apply(this,t)})});function i(i){return function t(r,e,u){switch(arguments.length){case 0:return t;case 1:return f(r)?t:n(function(t,n){return i(r,t,n)});case 2:return f(r)&&f(e)?t:f(r)?n(function(t,n){return i(t,e,n)}):f(e)?n(function(t,n){return i(r,t,n)}):o(function(t){return i(r,e,t)});default:return f(r)&&f(e)&&f(u)?t:f(r)&&f(e)?n(function(t,n){return i(t,n,u)}):f(r)&&f(u)?n(function(t,n){return i(t,e,n)}):f(e)&&f(u)?n(function(t,n){return i(r,t,n)}):f(r)?o(function(t){return i(t,e,u)}):f(e)?o(function(t){return i(r,t,u)}):f(u)?o(function(t){return i(r,e,t)}):i(r,e,u)}}}var h=i(function(t,n,r){var e=r.length;if(e<=t||t<-e)return r;var u=(e+t)%e,i=c(r);return i[u]=n(r[u]),i}),y=Array.isArray||function(t){return null!=t&&0<=t.length&&"[object Array]"===Object.prototype.toString.call(t)};function d(t){return null!=t&&"function"==typeof t["@@transducer/step"]}function v(r,e,u){return function(){if(0===arguments.length)return u();var t=arguments[arguments.length-1];if(!y(t)){for(var n=0;n<r.length;){if("function"==typeof t[r[n]])return t[r[n]].apply(t,Array.prototype.slice.call(arguments,0,-1));n+=1}if(d(t))return e.apply(null,Array.prototype.slice.call(arguments,0,-1))(t)}return u.apply(this,arguments)}}function g(t){return t&&t["@@transducer/reduced"]?t:{"@@transducer/value":t,"@@transducer/reduced":!0}}var m=function(){return this.xf["@@transducer/init"]()},b=function(t){return this.xf["@@transducer/result"](t)};function x(t,n){this.xf=n,this.f=t,this.all=!0}x.prototype["@@transducer/init"]=m,x.prototype["@@transducer/result"]=function(t){return this.all&&(t=this.xf["@@transducer/step"](t,!0)),this.xf["@@transducer/result"](t)},x.prototype["@@transducer/step"]=function(t,n){return this.f(n)||(this.all=!1,t=g(this.xf["@@transducer/step"](t,!1))),t};var w=n(v(["all"],function(n){return function(t){return new x(n,t)}},function(t,n){for(var r=0;r<n.length;){if(!t(n[r]))return!1;r+=1}return!0}));function j(t){for(var n,r=[];!(n=t.next()).done;)r.push(n.value);return r}function A(t,n,r){for(var e=0,u=r.length;e<u;){if(t(n,r[e]))return!0;e+=1}return!1}function O(t,n){return Object.prototype.hasOwnProperty.call(n,t)}function S(t,n){for(var r=0;r<t.length;){if(t[r]===n)return!0;r+=1}return!1}var E="function"==typeof Object.is?Object.is:function(t,n){return t===n?0!==t||1/t==1/n:t!=t&&n!=n},_=Object.prototype.toString,I=function(){return"[object Arguments]"===_.call(arguments)?function(t){return"[object Arguments]"===_.call(t)}:function(t){return O("callee",t)}}(),N=!{toString:null}.propertyIsEnumerable("toString"),k=["constructor","valueOf","isPrototypeOf","toString","propertyIsEnumerable","hasOwnProperty","toLocaleString"],W=function(){return arguments.propertyIsEnumerable("length")}(),q=o("function"!=typeof Object.keys||W?function(t){if(Object(t)!==t)return[];var n,r,e=[],u=W&&I(t);for(n in t)!O(n,t)||u&&"length"===n||(e[e.length]=n);if(N)for(r=k.length-1;0<=r;)O(n=k[r],t)&&!S(e,n)&&(e[e.length]=n),r-=1;return e}:function(t){return Object(t)!==t?[]:Object.keys(t)}),U=o(function(t){return null===t?"Null":void 0===t?"Undefined":Object.prototype.toString.call(t).slice(8,-1)});function F(t,n,r,e){var u=j(t);function i(t,n){return P(t,n,r.slice(),e.slice())}return!A(function(t,n){return!A(i,n,t)},j(n),u)}function P(t,n,r,e){if(E(t,n))return!0;var u=U(t);if(u!==U(n))return!1;if("function"==typeof t["fantasy-land/equals"]||"function"==typeof n["fantasy-land/equals"])return"function"==typeof t["fantasy-land/equals"]&&t["fantasy-land/equals"](n)&&"function"==typeof n["fantasy-land/equals"]&&n["fantasy-land/equals"](t);if("function"==typeof t.equals||"function"==typeof n.equals)return"function"==typeof t.equals&&t.equals(n)&&"function"==typeof n.equals&&n.equals(t);switch(u){case"Arguments":case"Array":case"Object":if("function"==typeof t.constructor&&"Promise"===function(t){var n=(t+"").match(/^function (\w*)/);return null==n?"":n[1]}(t.constructor))return t===n;break;case"Boolean":case"Number":case"String":if(s(t)!==s(n)||!E(t.valueOf(),n.valueOf()))return!1;break;case"Date":if(!E(t.valueOf(),n.valueOf()))return!1;break;case"Error":return t.name===n.name&&t.message===n.message;case"RegExp":if(t.source!==n.source||t.global!==n.global||t.ignoreCase!==n.ignoreCase||t.multiline!==n.multiline||t.sticky!==n.sticky||t.unicode!==n.unicode)return!1}for(var i=r.length-1;0<=i;){if(r[i]===t)return e[i]===n;i-=1}switch(u){case"Map":return t.size===n.size&&F(t.entries(),n.entries(),r.concat([t]),e.concat([n]));case"Set":return t.size===n.size&&F(t.values(),n.values(),r.concat([t]),e.concat([n]));case"Arguments":case"Array":case"Object":case"Boolean":case"Number":case"String":case"Date":case"Error":case"RegExp":case"Int8Array":case"Uint8Array":case"Uint8ClampedArray":case"Int16Array":case"Uint16Array":case"Int32Array":case"Uint32Array":case"Float32Array":case"Float64Array":case"ArrayBuffer":break;default:return!1}var o=q(t);if(o.length!==q(n).length)return!1;var c=r.concat([t]),a=e.concat([n]);for(i=o.length-1;0<=i;){var f=o[i];if(!O(f,n)||!P(n[f],t[f],c,a))return!1;i-=1}return!0}var B=n(function(t,n){return P(t,n,[],[])});function C(t,n,r){var e,u;if("function"==typeof t.indexOf)switch(s(n)){case"number":if(0===n){for(e=1/n;r<t.length;){if(0===(u=t[r])&&1/u==e)return r;r+=1}return-1}if(n==n)return t.indexOf(n,r);for(;r<t.length;){if("number"==typeof(u=t[r])&&u!=u)return r;r+=1}return-1;case"string":case"boolean":case"function":case"undefined":return t.indexOf(n,r);case"object":if(null===n)return t.indexOf(n,r)}for(;r<t.length;){if(B(t[r],n))return r;r+=1}return-1}function T(t,n){return 0<=C(n,t,0)}function R(t,n){for(var r=0,e=n.length,u=Array(e);r<e;)u[r]=t(n[r]),r+=1;return u}function M(t){return'"'+t.replace(/\\/g,"\\\\").replace(/[\b]/g,"\\b").replace(/\f/g,"\\f").replace(/\n/g,"\\n").replace(/\r/g,"\\r").replace(/\t/g,"\\t").replace(/\v/g,"\\v").replace(/\0/g,"\\0").replace(/"/g,'\\"')+'"'}function D(t){return(t<10?"0":"")+t}var z="function"==typeof Date.prototype.toISOString?function(t){return t.toISOString()}:function(t){return t.getUTCFullYear()+"-"+D(1+t.getUTCMonth())+"-"+D(t.getUTCDate())+"T"+D(t.getUTCHours())+":"+D(t.getUTCMinutes())+":"+D(t.getUTCSeconds())+"."+(t.getUTCMilliseconds()/1e3).toFixed(3).slice(2,5)+"Z"};function L(t){return function(){return!t.apply(this,arguments)}}function V(t,n,r){for(var e=0,u=r.length;e<u;)n=t(n,r[e]),e+=1;return n}function G(t,n){for(var r=0,e=n.length,u=[];r<e;)t(n[r])&&(u[u.length]=n[r]),r+=1;return u}function K(t){return"[object Object]"===Object.prototype.toString.call(t)}function $(t,n){this.xf=n,this.f=t}$.prototype["@@transducer/init"]=m,$.prototype["@@transducer/result"]=b,$.prototype["@@transducer/step"]=function(t,n){return this.f(n)?this.xf["@@transducer/step"](t,n):t};var H=n(v(["fantasy-land/filter","filter"],function(n){return function(t){return new $(n,t)}},function(r,e){return K(e)?V(function(t,n){return r(e[n])&&(t[n]=e[n]),t},{},q(e)):G(r,e)})),J=n(function(t,n){return H(L(t),n)});function X(r,e){function u(t){var n=e.concat([r]);return T(t,n)?"<Circular>":X(t,n)}function t(n,t){return R(function(t){return M(t)+": "+u(n[t])},t.slice().sort())}switch(Object.prototype.toString.call(r)){case"[object Arguments]":return"(function() { return arguments; }("+R(u,r).join(", ")+"))";case"[object Array]":return"["+R(u,r).concat(t(r,J(function(t){return/^\d+$/.test(t)},q(r)))).join(", ")+"]";case"[object Boolean]":return"object"===s(r)?"new Boolean("+u(r.valueOf())+")":""+r;case"[object Date]":return"new Date("+(isNaN(r.valueOf())?u(NaN):M(z(r)))+")";case"[object Map]":return"new Map("+u(Array.from(r))+")";case"[object Null]":return"null";case"[object Number]":return"object"===s(r)?"new Number("+u(r.valueOf())+")":1/r==-1/0?"-0":r.toString(10);case"[object Set]":return"new Set("+u(Array.from(r).sort())+")";case"[object String]":return"object"===s(r)?"new String("+u(r.valueOf())+")":M(r);case"[object Undefined]":return"undefined";default:if("function"==typeof r.toString){var n=""+r;if("[object Object]"!=n)return n}return"{"+t(r,q(r)).join(", ")+"}"}}var Y=o(function(t){return X(t,[])}),Z=n(function(t,n){if(t===n)return n;function r(t,n){if(n<t!=t<n)return t<n?n:t}var e=r(t,n);if(void 0!==e)return e;var u=r(s(t),s(n));if(void 0!==u)return u===s(t)?t:n;var i=Y(t),o=r(i,Y(n));return void 0!==o&&o===i?t:n});function Q(t,n){this.xf=n,this.f=t}Q.prototype["@@transducer/init"]=m,Q.prototype["@@transducer/result"]=b,Q.prototype["@@transducer/step"]=function(t,n){return this.xf["@@transducer/step"](t,this.f(n))};function tt(n){return function(t){return new Q(n,t)}}var nt=n(v(["fantasy-land/map","map"],tt,function(r,e){switch(Object.prototype.toString.call(e)){case"[object Function]":return a(e.length,function(){return r.call(this,e.apply(this,arguments))});case"[object Object]":return V(function(t,n){return t[n]=r(e[n]),t},{},q(e));default:return R(r,e)}})),rt=Number.isInteger||function(t){return t<<0===t};function et(t){return"[object String]"===Object.prototype.toString.call(t)}var ut=n(function(t,n){var r=t<0?n.length+t:t;return et(n)?n[0|r]:n[r]}),it=n(function(t,n){if(null!=n)return rt(t)?ut(t,n):n[t]}),ot=n(function(t,n){return nt(it(t),n)}),ct=o(function(t){return!!y(t)||!!t&&("object"===s(t)&&(!et(t)&&(0===t.length||0<t.length&&(t.hasOwnProperty(0)&&t.hasOwnProperty(t.length-1)))))}),at="undefined"!=typeof Symbol?Symbol.iterator:"@@iterator";function ft(e,u,i){return function(t,n,r){if(ct(r))return e(t,n,r);if(null==r)return n;if("function"==typeof r["fantasy-land/reduce"])return u(t,n,r,"fantasy-land/reduce");if(null!=r[at])return i(t,n,r[at]());if("function"==typeof r.next)return i(t,n,r);if("function"==typeof r.reduce)return u(t,n,r,"reduce");throw new TypeError("reduce: list must be array or iterable")}}function st(t,n,r){for(var e=0,u=r.length;e<u;){if((n=t["@@transducer/step"](n,r[e]))&&n["@@transducer/reduced"]){n=n["@@transducer/value"];break}e+=1}return t["@@transducer/result"](n)}var lt=n(function(t,n){return l(t.length,function(){return t.apply(n,arguments)})});var pt=ft(st,function(t,n,r,e){return t["@@transducer/result"](r[e](lt(t["@@transducer/step"],t),n))},function(t,n,r){for(var e=r.next();!e.done;){if((n=t["@@transducer/step"](n,e.value))&&n["@@transducer/reduced"]){n=n["@@transducer/value"];break}e=r.next()}return t["@@transducer/result"](n)});function ht(t){this.f=t}function yt(t){return new ht(t)}ht.prototype["@@transducer/init"]=function(){throw Error("init not implemented on XWrap")},ht.prototype["@@transducer/result"]=function(t){return t},ht.prototype["@@transducer/step"]=function(t,n){return this.f(t,n)};var dt=i(function(t,n,r){return pt("function"==typeof t?yt(t):t,n,r)}),vt=o(function(r){return a(dt(Z,0,ot("length",r)),function(){for(var t=0,n=r.length;t<n;){if(!r[t].apply(this,arguments))return!1;t+=1}return!0})}),gt=o(function(t){return function(){return t}}),mt=n(function(t,n){return t&&n});function bt(t,n){this.xf=n,this.f=t,this.any=!1}bt.prototype["@@transducer/init"]=m,bt.prototype["@@transducer/result"]=function(t){return this.any||(t=this.xf["@@transducer/step"](t,!1)),this.xf["@@transducer/result"](t)},bt.prototype["@@transducer/step"]=function(t,n){return this.f(n)&&(this.any=!0,t=g(this.xf["@@transducer/step"](t,!0))),t};var xt=n(v(["any"],function(n){return function(t){return new bt(n,t)}},function(t,n){for(var r=0;r<n.length;){if(t(n[r]))return!0;r+=1}return!1})),wt=o(function(r){return a(dt(Z,0,ot("length",r)),function(){for(var t=0,n=r.length;t<n;){if(r[t].apply(this,arguments))return!0;t+=1}return!1})});var jt=ft(V,function(t,n,r,e){return r[e](t,n)},function(t,n,r){for(var e=r.next();!e.done;)n=t(n,e.value),e=r.next();return n}),At=n(function(n,r){return"function"==typeof r["fantasy-land/ap"]?r["fantasy-land/ap"](n):"function"==typeof n.ap?n.ap(r):"function"==typeof n?function(t){return n(t)(r(t))}:jt(function(t,n){return c(t,nt(n,r))},[],n)});function Ot(t,n){this.xf=n,this.pos=0,this.full=!1,this.acc=Array(t)}Ot.prototype["@@transducer/init"]=m,Ot.prototype["@@transducer/result"]=function(t){return this.acc=null,this.xf["@@transducer/result"](t)},Ot.prototype["@@transducer/step"]=function(t,n){return this.store(n),this.full?this.xf["@@transducer/step"](t,this.getCopy()):t},Ot.prototype.store=function(t){this.acc[this.pos]=t,this.pos+=1,this.pos===this.acc.length&&(this.pos=0,this.full=!0)},Ot.prototype.getCopy=function(){return c(Array.prototype.slice.call(this.acc,this.pos),Array.prototype.slice.call(this.acc,0,this.pos))};var St=n(v([],function(n){return function(t){return new Ot(n,t)}},function(t,n){for(var r=0,e=n.length-(t-1),u=Array(e<0?0:e);r<e;)u[r]=Array.prototype.slice.call(n,r,r+t),r+=1;return u})),Et=n(function(t,n){return c(n,[t])}),_t=n(function(t,n){return t.apply(this,n)}),It=o(function(t){for(var n=q(t),r=n.length,e=[],u=0;u<r;)e[u]=t[n[u]],u+=1;return e});function Nt(r,e){return y(e)?e.map(r):q(e).reduce(function(t,n){return t[n]=r(e[n]),t},{})}var kt=o(function n(t){return t=Nt(function(t){return"function"==typeof t?t:n(t)},t),a(dt(Z,0,ot("length",It(t))),function(){var n=arguments;return Nt(function(t){return _t(t,n)},t)})}),Wt=n(function(t,n){return n(t)}),qt=i(function(t,n,r){var e=t(n),u=t(r);return e<u?-1:u<e?1:0});function Ut(t,n,r){if(rt(t)&&y(r)){var e=[].concat(r);return e[t]=n,e}var u={};for(var i in r)u[i]=r[i];return u[t]=n,u}var Ft=o(function(t){return null==t}),Pt=i(function t(n,r,e){if(0===n.length)return r;var u=n[0];if(1<n.length){var i=!Ft(e)&&O(u,e)&&"object"===s(e[u])?e[u]:rt(n[1])?[]:{};r=t(Array.prototype.slice.call(n,1),r,i)}return Ut(u,r,e)}),Bt=i(function(t,n,r){return Pt([t],n,r)}),Ct=n(function(t,s){switch(t){case 0:return function(){return s.call(this)};case 1:return function(t){return s.call(this,t)};case 2:return function(t,n){return s.call(this,t,n)};case 3:return function(t,n,r){return s.call(this,t,n,r)};case 4:return function(t,n,r,e){return s.call(this,t,n,r,e)};case 5:return function(t,n,r,e,u){return s.call(this,t,n,r,e,u)};case 6:return function(t,n,r,e,u,i){return s.call(this,t,n,r,e,u,i)};case 7:return function(t,n,r,e,u,i,o){return s.call(this,t,n,r,e,u,i,o)};case 8:return function(t,n,r,e,u,i,o,c){return s.call(this,t,n,r,e,u,i,o,c)};case 9:return function(t,n,r,e,u,i,o,c,a){return s.call(this,t,n,r,e,u,i,o,c,a)};case 10:return function(t,n,r,e,u,i,o,c,a,f){return s.call(this,t,n,r,e,u,i,o,c,a,f)};default:throw Error("First argument to nAry must be a non-negative integer no greater than ten")}}),Tt=o(function(t){return Ct(2,t)});function Rt(t){var n=Object.prototype.toString.call(t);return"[object Function]"===n||"[object AsyncFunction]"===n||"[object GeneratorFunction]"===n||"[object AsyncGeneratorFunction]"===n}var Mt=n(function(t,n){var r=a(t,n);return a(t,function(){return V(At,nt(r,arguments[0]),Array.prototype.slice.call(arguments,1))})}),Dt=o(function(t){return Mt(t.length,t)}),zt=n(function(t,n){return Rt(t)?function(){return t.apply(this,arguments)&&n.apply(this,arguments)}:Dt(mt)(t,n)}),Lt=o(function(t){return t.apply(this,Array.prototype.slice.call(arguments,1))});function Vt(a){return function t(n){for(var r,e,u,i=[],o=0,c=n.length;o<c;){if(ct(n[o]))for(u=0,e=(r=a?t(n[o]):n[o]).length;u<e;)i[i.length]=r[u],u+=1;else i[i.length]=n[o];o+=1}return i}}var Gt="@@transducer/init",Kt="@@transducer/step",$t="@@transducer/result";function Ht(t){this.xf=t}function Jt(t){this.xf=new Ht(t)}Ht.prototype[Gt]=m,Ht.prototype[$t]=b,Ht.prototype[Kt]=function(t,n){var r=this.xf[Kt](t,n);return r["@@transducer/reduced"]?function(t){return{"@@transducer/value":t,"@@transducer/reduced":!0}}(r):r},Jt.prototype[Gt]=m,Jt.prototype[$t]=b,Jt.prototype[Kt]=function(t,n){return ct(n)?pt(this.xf,t,n):st(this.xf,t,[n])};var Xt=n(v(["fantasy-land/chain","chain"],function(n){return function(t){return tt(n)(function(t){return new Jt(t)}(t))}},function(n,r){return"function"==typeof r?function(t){return n(r(t))(t)}:Vt(!1)(nt(n,r))})),Yt=i(function(t,n,r){if(n<t)throw Error("min must not be greater than max in clamp(min, max, value)");return r<t?t:n<r?n:r});function Zt(t){return RegExp(t.source,t.flags?t.flags:(t.global?"g":"")+(t.ignoreCase?"i":"")+(t.multiline?"m":"")+(t.sticky?"y":"")+(t.unicode?"u":"")+(t.dotAll?"s":""))}function Qt(e,u,i){if(i||(i=new tn),function(t){var n=s(t);return null==t||"object"!=n&&"function"!=n}(e))return e;function t(t){var n=i.get(e);if(n)return n;for(var r in i.set(e,t),e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=u?Qt(e[r],!0,i):e[r]);return t}switch(U(e)){case"Object":return t(Object.create(Object.getPrototypeOf(e)));case"Array":return t([]);case"Date":return new Date(e.valueOf());case"RegExp":return Zt(e);case"Int8Array":case"Uint8Array":case"Uint8ClampedArray":case"Int16Array":case"Uint16Array":case"Int32Array":case"Uint32Array":case"Float32Array":case"Float64Array":case"BigInt64Array":case"BigUint64Array":return e.slice();default:return e}}function tn(){this.map={},this.length=0}tn.prototype.set=function(t,n){var r=this.hash(t),e=this.map[r];e||(this.map[r]=e=[]),e.push([t,n]),this.length+=1},tn.prototype.hash=function(t){var n=[];for(var r in t)n.push(Object.prototype.toString.call(t[r]));return n.join()},tn.prototype.get=function(t){if(180<this.length){var n=this.hash(t),r=this.map[n];if(r)for(var e=0;e<r.length;e+=1){var u=r[e];if(u[0]===t)return u[1]}}else for(var i in this.map)for(var o=this.map[i],c=0;c<o.length;c+=1){var a=o[c];if(a[0]===t)return a[1]}};var nn=o(function(t){return null!=t&&"function"==typeof t.clone?t.clone():Qt(t,!0)}),rn=n(function(e,t){var n=jt(function(t,n){var r=e(n);return void 0===t[r]&&(t[r]=[]),t[r].push(n),t},{},t),r=[];for(var u in n)r.push(n[u]);return r}),en=o(function(r){return function(t,n){return r(t,n)?-1:r(n,t)?1:0}}),un=o(function(t){return!t}),on=Dt(un);function cn(t,n){return function(){return n.call(this,t.apply(this,arguments))}}function an(r,e){return function(){var t=arguments.length;if(0===t)return e();var n=arguments[t-1];return y(n)||"function"!=typeof n[r]?e.apply(this,arguments):n[r].apply(n,Array.prototype.slice.call(arguments,0,t-1))}}var fn=i(an("slice",function(t,n,r){return Array.prototype.slice.call(r,t,n)})),sn=o(an("tail",fn(1,1/0)));function ln(){if(0===arguments.length)throw Error("pipe requires at least one argument");return l(arguments[0].length,dt(cn,arguments[0],sn(arguments)))}var pn=o(function(t){return et(t)?t.split("").reverse().join(""):Array.prototype.slice.call(t,0).reverse()});function hn(){if(0===arguments.length)throw Error("compose requires at least one argument");return ln.apply(this,pn(arguments))}var yn=ut(0);function dn(t){return t}var vn=o(dn),gn=n(function(r,t){if(t.length<=0)return vn;var n=yn(t),e=sn(t);return l(n.length,function(){return jt(function(t,n){return r.call(this,n,t)},n.apply(this,arguments),e)})}),mn=n(function(t,n){return gn.call(this,t,pn(n))}),bn=n(function(t,n){if(y(t)){if(y(n))return t.concat(n);throw new TypeError(Y(n)+" is not an array")}if(et(t)){if(et(n))return t+n;throw new TypeError(Y(n)+" is not a string")}if(null!=t&&Rt(t["fantasy-land/concat"]))return t["fantasy-land/concat"](n);if(null!=t&&Rt(t.concat))return t.concat(n);throw new TypeError(Y(t)+' does not have a method named "concat" or "fantasy-land/concat"')}),xn=o(function(n){return l(dt(Z,0,nt(function(t){return t[0].length},n)),function(){for(var t=0;t<n.length;){if(n[t][0].apply(this,arguments))return n[t][1].apply(this,arguments);t+=1}})}),wn=o(function(t){return a(t.length,t)}),jn=n(function(s,l){if(10<s)throw Error("Constructor with greater than ten arguments");return 0===s?function(){return new l}:wn(Ct(s,function(t,n,r,e,u,i,o,c,a,f){switch(s){case 1:return new l(t);case 2:return new l(t,n);case 3:return new l(t,n,r);case 4:return new l(t,n,r,e);case 5:return new l(t,n,r,e,u);case 6:return new l(t,n,r,e,u,i);case 7:return new l(t,n,r,e,u,i,o);case 8:return new l(t,n,r,e,u,i,o,c);case 9:return new l(t,n,r,e,u,i,o,c,a);case 10:return new l(t,n,r,e,u,i,o,c,a,f)}}))}),An=o(function(t){return jn(t.length,t)}),On=n(function(t,e){return a(dt(Z,0,ot("length",e)),function(){var n=arguments,r=this;return t.apply(r,R(function(t){return t.apply(r,n)},e))})}),Sn=wn(function(r,t){return jt(function(t,n){return r(n)?t+1:t},0,t)});function En(t,n,r,e){this.valueFn=t,this.valueAcc=n,this.keyFn=r,this.xf=e,this.inputs={}}En.prototype["@@transducer/init"]=m,En.prototype["@@transducer/result"]=function(t){var n;for(n in this.inputs)if(O(n,this.inputs)&&(t=this.xf["@@transducer/step"](t,this.inputs[n]))["@@transducer/reduced"]){t=t["@@transducer/value"];break}return this.inputs=null,this.xf["@@transducer/result"](t)},En.prototype["@@transducer/step"]=function(t,n){var r=this.keyFn(n);return this.inputs[r]=this.inputs[r]||[r,Qt(this.valueAcc,!1)],this.inputs[r][1]=this.valueFn(this.inputs[r][1],n),t};var _n=p(4,[],v([],function(n,r,e){return function(t){return new En(n,r,e,t)}},function(u,i,o,t){var n=yt(function(t,n){var r=o(n),e=u(O(r,t)?t[r]:Qt(i,!1),n);return e&&e["@@transducer/reduced"]?g(t):(t[r]=e,t)});return pt(n,{},t)})),In=_n(function(t,n){return t+1},0),Nn=r(-1),kn=n(function(t,n){return null==n||n!=n?t:n}),Wn=i(function(t,n,r){var e=t(n),u=t(r);return u<e?-1:e<u?1:0});function qn(){this._nativeSet="function"==typeof Set?new Set:null,this._items={}}function Un(t,n,r){var e,u=s(t);switch(u){case"string":case"number":return 0===t&&1/t==-1/0?!!r._items["-0"]||(n&&(r._items["-0"]=!0),!1):null!==r._nativeSet?n?(e=r._nativeSet.size,r._nativeSet.add(t),r._nativeSet.size===e):r._nativeSet.has(t):u in r._items?t in r._items[u]||(n&&(r._items[u][t]=!0),!1):(n&&(r._items[u]={},r._items[u][t]=!0),!1);case"boolean":if(u in r._items){var i=t?1:0;return!!r._items[u][i]||(n&&(r._items[u][i]=!0),!1)}return n&&(r._items[u]=t?[!1,!0]:[!0,!1]),!1;case"function":return null!==r._nativeSet?n?(e=r._nativeSet.size,r._nativeSet.add(t),r._nativeSet.size===e):r._nativeSet.has(t):u in r._items?!!T(t,r._items[u])||(n&&r._items[u].push(t),!1):(n&&(r._items[u]=[t]),!1);case"undefined":return!!r._items[u]||(n&&(r._items[u]=!0),!1);case"object":if(null===t)return!!r._items.null||(n&&(r._items.null=!0),!1);default:return(u=Object.prototype.toString.call(t))in r._items?!!T(t,r._items[u])||(n&&r._items[u].push(t),!1):(n&&(r._items[u]=[t]),!1)}}qn.prototype.add=function(t){return!Un(t,!0,this)},qn.prototype.has=function(t){return Un(t,!1,this)};var Fn=n(function(t,n){for(var r=[],e=0,u=t.length,i=n.length,o=new qn,c=0;c<i;c+=1)o.add(n[c]);for(;e<u;)o.add(t[e])&&(r[r.length]=t[e]),e+=1;return r}),Pn=i(function(t,n,r){for(var e=[],u=0,i=n.length;u<i;)A(t,n[u],r)||A(t,n[u],e)||e.push(n[u]),u+=1;return e}),Bn=i(function(t,n,r){var e=Array.prototype.slice.call(r,0);return e.splice(t,n),e});var Cn=n(function t(n,r){if(null==r)return r;switch(n.length){case 0:return r;case 1:return function(t,n){if(null==n)return n;if(rt(t)&&y(n))return Bn(t,1,n);var r={};for(var e in n)r[e]=n[e];return delete r[t],r}(n[0],r);default:var e=n[0],u=Array.prototype.slice.call(n,1);return null==r[e]?function(t,n){if(rt(t)&&y(n))return[].concat(n);var r={};for(var e in n)r[e]=n[e];return r}(e,r):Bt(e,t(u,r[e]),r)}}),Tn=n(function(t,n){return Cn([t],n)}),Rn=n(function(t,n){return t/n});function Mn(t,n){this.xf=n,this.n=t}Mn.prototype["@@transducer/init"]=m,Mn.prototype["@@transducer/result"]=b,Mn.prototype["@@transducer/step"]=function(t,n){return 0<this.n?(this.n-=1,t):this.xf["@@transducer/step"](t,n)};var Dn=n(v(["drop"],function(n){return function(t){return new Mn(n,t)}},function(t,n){return fn(Math.max(0,t),1/0,n)}));function zn(t,n){this.xf=n,this.n=t,this.i=0}zn.prototype["@@transducer/init"]=m,zn.prototype["@@transducer/result"]=b,zn.prototype["@@transducer/step"]=function(t,n){this.i+=1;var r=0===this.n?t:this.xf["@@transducer/step"](t,n);return this.n<0||this.i<this.n?r:g(r)};var Ln=n(v(["take"],function(n){return function(t){return new zn(n,t)}},function(t,n){return fn(0,t<0?1/0:t,n)}));function Vn(t,n){if(t<=0)return n;this.xf=n,this.pos=0,this.full=!1,this.acc=Array(t)}Vn.prototype["@@transducer/init"]=m,Vn.prototype["@@transducer/result"]=function(t){return this.acc=null,this.xf["@@transducer/result"](t)},Vn.prototype["@@transducer/step"]=function(t,n){return this.full&&(t=this.xf["@@transducer/step"](t,this.acc[this.pos])),this.store(n),t},Vn.prototype.store=function(t){this.acc[this.pos]=t,this.pos+=1,this.pos===this.acc.length&&(this.pos=0,this.full=!0)};var Gn=n(v([],function(n){return function(t){return new Vn(n,t)}},function(t,n){return Ln(t<n.length?n.length-t:0,n)}));function Kn(t,n){this.f=t,this.retained=[],this.xf=n}Kn.prototype["@@transducer/init"]=m,Kn.prototype["@@transducer/result"]=function(t){return this.retained=null,this.xf["@@transducer/result"](t)},Kn.prototype["@@transducer/step"]=function(t,n){return this.f(n)?this.retain(t,n):this.flush(t,n)},Kn.prototype.flush=function(t,n){return t=pt(this.xf,t,this.retained),this.retained=[],this.xf["@@transducer/step"](t,n)},Kn.prototype.retain=function(t,n){return this.retained.push(n),t};var $n=n(v([],function(n){return function(t){return new Kn(n,t)}},function(t,n){for(var r=n.length-1;0<=r&&t(n[r]);)r-=1;return fn(0,r+1,n)}));function Hn(t,n){this.xf=n,this.pred=t,this.lastValue=void 0,this.seenFirstValue=!1}function Jn(n){return function(t){return new Hn(n,t)}}Hn.prototype["@@transducer/init"]=m,Hn.prototype["@@transducer/result"]=b,Hn.prototype["@@transducer/step"]=function(t,n){var r=!1;return this.seenFirstValue?this.pred(this.lastValue,n)&&(r=!0):this.seenFirstValue=!0,this.lastValue=n,r?t:this.xf["@@transducer/step"](t,n)};var Xn=ut(-1),Yn=n(v([],Jn,function(t,n){var r=[],e=1,u=n.length;if(0!==u)for(r[0]=n[0];e<u;)t(Xn(r),n[e])||(r[r.length]=n[e]),e+=1;return r})),Zn=o(v([],function(){return Jn(B)},Yn(B))),Qn=i(function(t,n,r){return B(t(n),t(r))}),tr=n(function(t,n){return v([],function(){return Jn(Qn(t))},Yn(Qn(t)))(n)});function nr(t,n){this.xf=n,this.f=t}nr.prototype["@@transducer/init"]=m,nr.prototype["@@transducer/result"]=b,nr.prototype["@@transducer/step"]=function(t,n){if(this.f){if(this.f(n))return t;this.f=null}return this.xf["@@transducer/step"](t,n)};var rr=n(v(["dropWhile"],function(n){return function(t){return new nr(n,t)}},function(t,n){for(var r=0,e=n.length;r<e&&t(n[r]);)r+=1;return fn(r,1/0,n)})),er=n(function(t,n){return t||n}),ur=n(function(t,n){return Rt(t)?function(){return t.apply(this,arguments)||n.apply(this,arguments)}:Dt(er)(t,n)});var ir=o(function(t){return null!=t&&"function"==typeof t["fantasy-land/empty"]?t["fantasy-land/empty"]():null!=t&&null!=t.constructor&&"function"==typeof t.constructor["fantasy-land/empty"]?t.constructor["fantasy-land/empty"]():null!=t&&"function"==typeof t.empty?t.empty():null!=t&&null!=t.constructor&&"function"==typeof t.constructor.empty?t.constructor.empty():y(t)?[]:et(t)?"":K(t)?{}:I(t)?function(){return arguments}():function(t){var n=Object.prototype.toString.call(t);return"[object Uint8ClampedArray]"===n||"[object Int8Array]"===n||"[object Uint8Array]"===n||"[object Int16Array]"===n||"[object Uint16Array]"===n||"[object Int32Array]"===n||"[object Uint32Array]"===n||"[object Float32Array]"===n||"[object Float64Array]"===n||"[object BigInt64Array]"===n||"[object BigUint64Array]"===n}(t)?t.constructor.from(""):void 0}),or=n(function(t,n){return Dn(t<0?0:n.length-t,n)}),cr=n(function(t,n){return B(or(t.length,n),t)}),ar=i(function(t,n,r){return B(n[t],r[t])}),fr=n(function t(n,r){if(!K(r)&&!y(r))return r;var e,u,i,o=r instanceof Array?[]:{};for(u in r)i=s(e=n[u]),o[u]="function"===i?e(r[u]):e&&"object"===i?t(e,r[u]):r[u];return o});function sr(t,n){this.xf=n,this.f=t,this.found=!1}sr.prototype["@@transducer/init"]=m,sr.prototype["@@transducer/result"]=function(t){return this.found||(t=this.xf["@@transducer/step"](t,void 0)),this.xf["@@transducer/result"](t)},sr.prototype["@@transducer/step"]=function(t,n){return this.f(n)&&(this.found=!0,t=g(this.xf["@@transducer/step"](t,n))),t};var lr=n(v(["find"],function(n){return function(t){return new sr(n,t)}},function(t,n){for(var r=0,e=n.length;r<e;){if(t(n[r]))return n[r];r+=1}}));function pr(t,n){this.xf=n,this.f=t,this.idx=-1,this.found=!1}pr.prototype["@@transducer/init"]=m,pr.prototype["@@transducer/result"]=function(t){return this.found||(t=this.xf["@@transducer/step"](t,-1)),this.xf["@@transducer/result"](t)},pr.prototype["@@transducer/step"]=function(t,n){return this.idx+=1,this.f(n)&&(this.found=!0,t=g(this.xf["@@transducer/step"](t,this.idx))),t};var hr=n(v([],function(n){return function(t){return new pr(n,t)}},function(t,n){for(var r=0,e=n.length;r<e;){if(t(n[r]))return r;r+=1}return-1}));function yr(t,n){this.xf=n,this.f=t}yr.prototype["@@transducer/init"]=m,yr.prototype["@@transducer/result"]=function(t){return this.xf["@@transducer/result"](this.xf["@@transducer/step"](t,this.last))},yr.prototype["@@transducer/step"]=function(t,n){return this.f(n)&&(this.last=n),t};var dr=n(v([],function(n){return function(t){return new yr(n,t)}},function(t,n){for(var r=n.length-1;0<=r;){if(t(n[r]))return n[r];r-=1}}));function vr(t,n){this.xf=n,this.f=t,this.idx=-1,this.lastIdx=-1}vr.prototype["@@transducer/init"]=m,vr.prototype["@@transducer/result"]=function(t){return this.xf["@@transducer/result"](this.xf["@@transducer/step"](t,this.lastIdx))},vr.prototype["@@transducer/step"]=function(t,n){return this.idx+=1,this.f(n)&&(this.lastIdx=this.idx),t};var gr=n(v([],function(n){return function(t){return new vr(n,t)}},function(t,n){for(var r=n.length-1;0<=r;){if(t(n[r]))return r;r-=1}return-1})),mr=o(Vt(!0)),br=o(function(e){return a(e.length,function(t,n){var r=Array.prototype.slice.call(arguments,0);return r[0]=n,r[1]=t,e.apply(this,r)})}),xr=n(an("forEach",function(t,n){for(var r=n.length,e=0;e<r;)t(n[e]),e+=1;return n})),wr=n(function(t,n){for(var r=q(n),e=0;e<r.length;){var u=r[e];t(n[u],u,n),e+=1}return n}),jr=o(function(t){for(var n={},r=0;r<t.length;)n[t[r][0]]=t[r][1],r+=1;return n}),Ar=n(an("groupBy",_n(function(t,n){return t.push(n),t},[]))),Or=n(function(t,n){for(var r=[],e=0,u=n.length;e<u;){for(var i=e+1;i<u&&t(n[i-1],n[i]);)i+=1;r.push(n.slice(e,i)),e=i}return r}),Sr=n(function(t,n){return n<t}),Er=n(function(t,n){return n<=t}),_r=n(function(t,n){if(0===t.length||Ft(n))return!1;for(var r=n,e=0;e<t.length;){if(Ft(r)||!O(t[e],r))return!1;r=r[t[e]],e+=1}return!0}),Ir=n(function(t,n){return _r([t],n)}),Nr=n(function(t,n){return!Ft(n)&&t in n}),kr=i(function(t,n,r){return a(Math.max(t.length,n.length,r.length),function(){return t.apply(this,arguments)?n.apply(this,arguments):r.apply(this,arguments)})}),Wr=r(1),qr=n(T),Ur=_n(function(t,n){return n},null),Fr=n(function(t,n){return"function"!=typeof n.indexOf||y(n)?C(n,t,0):n.indexOf(t)}),Pr=fn(0,-1),Br=i(function(n,t,r){return G(function(t){return A(n,t,r)},t)}),Cr=i(function(t,n,r){t=t<r.length&&0<=t?t:r.length;var e=Array.prototype.slice.call(r,0);return e.splice(t,0,n),e}),Tr=i(function(t,n,r){return[].concat(Array.prototype.slice.call(r,0,t=t<r.length&&0<=t?t:r.length),n,Array.prototype.slice.call(r,t))});function Rr(t,n){this.xf=n,this.f=t,this.set=new qn}Rr.prototype["@@transducer/init"]=m,Rr.prototype["@@transducer/result"]=b,Rr.prototype["@@transducer/step"]=function(t,n){return this.set.add(this.f(n))?this.xf["@@transducer/step"](t,n):t};var Mr=n(v([],function(n){return function(t){return new Rr(n,t)}},function(t,n){for(var r,e,u=new qn,i=[],o=0;o<n.length;)r=t(e=n[o]),u.add(r)&&i.push(e),o+=1;return i})),Dr=Mr(vn),zr=n(function(t,n){for(var r=new qn,e=0;e<t.length;e+=1)r.add(t[e]);return Dr(G(r.has.bind(r),n))}),Lr=n(an("intersperse",function(t,n){for(var r=[],e=0,u=n.length;e<u;)e===u-1?r.push(n[e]):r.push(n[e],t),e+=1;return r}));var Vr="function"==typeof Object.assign?Object.assign:function(t){if(null==t)throw new TypeError("Cannot convert undefined or null to object");for(var n=Object(t),r=1,e=arguments.length;r<e;){var u=arguments[r];if(null!=u)for(var i in u)O(i,u)&&(n[i]=u[i]);r+=1}return n},Gr=n(function(t,n){var r={};return r[t]=n,r}),Kr={"@@transducer/init":Array,"@@transducer/step":function(t,n){return t.push(n),t},"@@transducer/result":dn},$r={"@@transducer/init":String,"@@transducer/step":function(t,n){return t+n},"@@transducer/result":dn},Hr={"@@transducer/init":Object,"@@transducer/step":function(t,n){return Vr(t,ct(n)?Gr(n[0],n[1]):n)},"@@transducer/result":dn};var Jr=i(function(t,n,r){var e=n(d(t)?t:function(t){if(d(t))return t;if(ct(t))return Kr;if("string"==typeof t)return $r;if("object"===s(t))return Hr;throw Error("Cannot create transformer for "+t)}(t));return pt(e,e["@@transducer/init"](),r)}),Xr=o(function(t){for(var n=q(t),r=n.length,e=0,u={};e<r;){var i=n[e],o=t[i],c=O(o,u)?u[o]:u[o]=[];c[c.length]=i,e+=1}return u}),Yr=o(function(t){for(var n=q(t),r=n.length,e=0,u={};e<r;){var i=n[e];u[t[i]]=i,e+=1}return u}),Zr=n(function(n,r){return a(n+1,function(){var t=arguments[n];if(null!=t&&Rt(t[r]))return t[r].apply(t,Array.prototype.slice.call(arguments,0,n));throw new TypeError(Y(t)+' does not have a method named "'+r+'"')})}),Qr=n(function(t,n){return n instanceof t||null!=n&&(n.constructor===t||"Object"===t.name&&"object"===s(n))}),te=o(function(t){return null!=t&&B(t,ir(t))}),ne=o(function(t){return!Ft(t)}),re=Zr(1,"join"),ee=o(function(t){return On(function(){return Array.prototype.slice.call(arguments,0)},t)}),ue=o(function(t){var n,r=[];for(n in t)r[r.length]=n;return r}),ie=n(function(t,n){if("function"!=typeof n.lastIndexOf||y(n)){for(var r=n.length-1;0<=r;){if(B(n[r],t))return r;r-=1}return-1}return n.lastIndexOf(t)});function oe(t){return"[object Number]"===Object.prototype.toString.call(t)}var ce=o(function(t){return null!=t&&oe(t.length)?t.length:NaN}),ae=n(function(r,e){return function(t){return function(n){return nt(function(t){return e(t,n)},t(r(n)))}}}),fe=i(function(t,n,r){return h(t,gt(n),r)}),se=o(function(t){return ae(ut(t),fe(t))}),le=n(function(t,u){return t.map(function(t){for(var n,r=u,e=0;e<t.length;){if(null==r)return;r=rt(n=t[e])?ut(n,r):r[n],e+=1}return r})}),pe=n(function(t,n){return le([t],n)[0]}),he=o(function(t){return ae(pe(t),Pt(t))}),ye=o(function(t){return ae(it(t),Bt(t))}),de=n(function(t,n){return t<n}),ve=n(function(t,n){return t<=n}),ge=i(function(t,n,r){for(var e=0,u=r.length,i=[],o=[n];e<u;)o=t(o[0],r[e]),i[e]=o[1],e+=1;return[o[0],i]}),me=i(function(t,n,r){for(var e=r.length-1,u=[],i=[n];0<=e;)i=t(i[0],r[e]),u[e]=i[1],e-=1;return[i[0],u]}),be=n(function(r,e){return V(function(t,n){return t[n]=r(e[n],n,e),t},{},q(e))}),xe=n(function(t,n){return n.match(t)||[]}),we=n(function(t,n){return rt(t)?!rt(n)||n<1?NaN:(t%n+n)%n:NaN}),je=i(function(t,n,r){var e=t(r);return Z(t(n),e)===e?r:n}),Ae=dt(r,0),Oe=o(function(t){return Ae(t)/t.length}),Se=o(function(t){var n=t.length;if(0===n)return NaN;var r=2-n%2,e=(n-r)/2;return Oe(Array.prototype.slice.call(t,0).sort(function(t,n){return t<n?-1:n<t?1:0}).slice(e,e+r))}),Ee=n(function(n,r){var e={};return l(r.length,function(){var t=n.apply(this,arguments);return O(t,e)||(e[t]=r.apply(this,arguments)),e[t]})}),_e=o(function(t){return Vr.apply(null,[{}].concat(t))}),Ie=i(function(t,n,r){var e,u={};for(e in r=r||{},n=n||{})O(e,n)&&(u[e]=O(e,r)?t(e,n[e],r[e]):n[e]);for(e in r)O(e,r)&&!O(e,u)&&(u[e]=r[e]);return u}),Ne=i(function e(u,t,n){return Ie(function(t,n,r){return K(n)&&K(r)?e(u,n,r):u(t,n,r)},t,n)}),ke=n(function(t,n){return Ne(function(t,n,r){return n},t,n)}),We=n(function(t,n){return Ne(function(t,n,r){return r},t,n)}),qe=i(function(e,t,n){return Ne(function(t,n,r){return e(n,r)},t,n)}),Ue=n(function(t,n){return Vr({},n,t)}),Fe=n(function(t,n){return Vr({},t,n)}),Pe=i(function(e,t,n){return Ie(function(t,n,r){return e(n,r)},t,n)}),Be=n(function(t,n){if(t===n)return t;function r(t,n){if(t<n!=n<t)return n<t?n:t}var e=r(t,n);if(void 0!==e)return e;var u=r(s(t),s(n));if(void 0!==u)return u===s(t)?t:n;var i=Y(t),o=r(i,Y(n));return void 0!==o?o===i?t:n:t}),Ce=i(function(t,n,r){var e=t(r);return Be(t(n),e)===e?r:n});var Te=i(function t(n,r,e){if(!K(e)&&!y(e))return e;if(0===n.length)return r(e);var u=n[0];if(!O(u,e))return e;if(1===n.length)return function(t,n,r){if(rt(t)&&y(r)){var e=[].concat(r);return e[t]=n(e[t]),e}var u={};for(var i in r)u[i]=r[i];return u[t]=n(u[t]),u}(u,r,e);var i=t(Array.prototype.slice.call(n,1),r,e[u]);return i===e[u]?e:Ut(u,i,e)}),Re=i(function(t,n,r){return Te([t],n,r)}),Me=n(function(t,n){return t%n}),De=i(function(t,n,r){var e=r.length,u=r.slice(),i=t<0?e+t:t,o=n<0?e+n:n,c=u.splice(i,1);return i<0||r.length<=i||o<0||r.length<=o?r:[].concat(u.slice(0,o)).concat(c).concat(u.slice(o,r.length))}),ze=n(function(t,n){return t*n}),Le=n(function(n,r){return function(t){return n.call(void 0,We(r,t))}}),Ve=o(function(t){return-t}),Ge=n(function(t,n){return w(L(t),n)}),Ke=o(function(t){return a(t<0?1:t+1,function(){return ut(t,arguments)})}),$e=i(function(t,n,r){return t(n(r))}),He=n(function(t,n){return"function"==typeof t["fantasy-land/of"]?t["fantasy-land/of"](n):"function"==typeof t.of?t.of(n):[n]}),Je=n(function(t,n){for(var r={},e={},u=0,i=t.length;u<i;)u+=e[t[u]]=1;for(var o in n)e.hasOwnProperty(o)||(r[o]=n[o]);return r}),Xe=p(4,[],function(t,n,r,e){return t(n(r),n(e))}),Ye=o(function(t){var n,r=!1;return l(t.length,function(){return r?n:(r=!0,n=t.apply(this,arguments))})});function Ze(t,n){if(null==n||!Rt(n.then))throw new TypeError("`"+t+"` expected a Promise, received "+X(n,[]))}var Qe=n(function(t,n){return Ze("otherwise",n),n.then(null,t)}),tu=i(function(t,n,r){return t(function(t){return function n(r){return{value:r,map:function(t){return n(t(r))}}}(n(t))})(r).value}),nu=n(function(t,n){return[t,n]});function ru(r){return n(function(t,n){return l(Math.max(0,t.length-n.length),function(){return t.apply(this,r(n,arguments))})})}var eu=ru(c),uu=ru(br(c)),iu=ee([H,J]),ou=i(function(t,n,r){return B(pe(n,r),t)}),cu=i(function(t,n,r){return kn(t,pe(n,r))}),au=i(function(t,n,r){return t(pe(n,r))}),fu=n(function(t,n){for(var r={},e=0;e<t.length;)t[e]in n&&(r[t[e]]=n[t[e]]),e+=1;return r}),su=n(function(t,n){for(var r={},e=0,u=t.length;e<u;){var i=t[e];r[i]=n[i],e+=1}return r}),lu=n(function(t,n){var r={};for(var e in n)t(n[e],e,n)&&(r[e]=n[e]);return r}),pu=n(function(t,n){return c([t],n)}),hu=dt(ze,1),yu=n(function(r,e){return a(e.length,function(){for(var t=[],n=0;n<e.length;)t.push(e[n].call(this,arguments[n])),n+=1;return r.apply(this,t.concat(Array.prototype.slice.call(arguments,e.length)))})}),du=yu(R,[su,vn]);function vu(n,r,e){return function(t){return r(e(n(t)))}}function gu(t,n,r){this.xf=r,this.f=t,this.g=n}gu.prototype["@@transducer/init"]=m,gu.prototype["@@transducer/result"]=b,gu.prototype["@@transducer/step"]=function(t,n){return this.xf["@@transducer/step"](t,vu(this.f,this.g,n))};var mu=i(v(["fantasy-land/promap","promap"],function(n,r){return function(t){return new gu(n,r,t)}},vu)),bu=i(function(t,n,r){return B(t,it(n,r))}),xu=i(function(t,n,r){return Qr(t,it(n,r))}),wu=i(function(t,n,r){return kn(t,it(n,r))}),ju=i(function(t,n,r){return t(it(n,r))}),Au=n(function(t,n){return t.map(function(t){return pe([t],n)})}),Ou=n(function(t,n){if(!oe(t)||!oe(n))throw new TypeError("Both arguments to range must be numbers");for(var r=[],e=t;e<n;)r.push(e),e+=1;return r}),Su=i(function(t,n,r){for(var e=r.length-1;0<=e;){if((n=t(r[e],n))&&n["@@transducer/reduced"]){n=n["@@transducer/value"];break}e-=1}return n}),Eu=p(4,[],function(r,e,t,n){var u=yt(function(t,n){return r(t,n)?e(t,n):g(t)});return pt(u,t,n)}),_u=o(g),Iu=n(function(t,n){var r,e=+n,u=0;if(e<0||isNaN(e))throw new RangeError("n must be a non-negative number");for(r=[];u<e;)r.push(t(u)),u+=1;return r}),Nu=n(function(t,n){return Iu(gt(t),n)}),ku=i(function(t,n,r){return r.replace(t,n)}),Wu="@@transducer/init",qu="@@transducer/step";function Uu(t,n,r){this.xf=r,this.f=t,this.acc=n}Uu.prototype[Wu]=function(){return this.xf[qu](this.xf[Wu](),this.acc)},Uu.prototype["@@transducer/result"]=b,Uu.prototype[qu]=function(t,n){return t["@@transducer/reduced"]?t:(this.acc=this.f(this.acc,n),this.xf[qu](t,this.acc))};function Fu(t,n,r){var e=r.length,u=r.slice(),i=t<0?e+t:t,o=n<0?e+n:n,c=Math.min(i,o),a=Math.max(i,o);return i<0||e<i?u:o<0||e<o?u:i===o?u:u=[].concat(u.slice(0,c)).concat([u[a]]).concat(u.slice(1+c,a)).concat([u[c]]).concat(u.slice(1+a,e))}var Pu=i(v([],i(function(t,n,r){return new Uu(t,n,r)}),function(t,n,r){for(var e=0,u=r.length,i=[n];e<u;)n=t(n,r[e]),i[e+1]=n,e+=1;return i})),Bu=n(function(t,n){var r="function"==typeof t["fantasy-land/of"]?t["fantasy-land/of"]:"function"==typeof t.of?t.of:t,e={"fantasy-land/of":r};return"function"==typeof n["fantasy-land/traverse"]?n["fantasy-land/traverse"](e,dn):"function"==typeof n.traverse?n.traverse(e,dn):Su(function(t,n){return At(nt(pu,t),n)},r([]),n)}),Cu=i(function(t,n,r){return tu(t,gt(n),r)}),Tu=n(function(t,n){return Array.prototype.slice.call(n,0).sort(t)}),Ru=n(function(u,t){return Array.prototype.slice.call(t,0).sort(function(t,n){var r=u(t),e=u(n);return r<e?-1:e<r?1:0})}),Mu=n(function(u,t){return Array.prototype.slice.call(t,0).sort(function(t,n){for(var r=0,e=0;0===r&&e<u.length;)r=u[e](t,n),e+=1;return r})}),Du=Zr(1,"split"),zu=n(function(t,n){return[fn(0,t,n),fn(t,ce(n),n)]}),Lu=n(function(t,n){if(t<=0)throw Error("First argument to splitEvery must be a positive integer");for(var r=[],e=0;e<n.length;)r.push(fn(e,e+=t,n));return r}),Vu=n(function(t,n){for(var r=0,e=n.length,u=[];r<e&&!t(n[r]);)u.push(n[r]),r+=1;return[u,Array.prototype.slice.call(n,r)]}),Gu=p(2,[],function(t,n){for(var r=[],e=[],u=0;u<n.length;u+=1)t(n[u])||e.push(n[u]),(u<n.length-1&&t(n[u+1])||u===n.length-1)&&0<e.length&&(r.push(e),e=[]);return r}),Ku=n(function(t,n){return B(Ln(t.length,n),t)}),$u=n(function(t,n){return+t-+n}),Hu=i(function(t,n,r){return y(r)?Fu(t,n,r):et(r)?function(t,n,r){var e=Fu(t,n,r);return y(e)?e.join(""):e}(t,n,r):function(t,n,r){var e=nn(r),u=Object.getOwnPropertyNames(e);if(u.includes(t)&&u.includes(n)){var i=e[t];e[t]=e[n],e[n]=i}return e}(t,n,r)}),Ju=n(function(t,n){return bn(Fn(t,n),Fn(n,t))}),Xu=i(function(t,n,r){return bn(Pn(t,n,r),Pn(t,r,n))}),Yu=n(function(t,n){for(var r=n.length-1;0<=r&&t(n[r]);)r-=1;return fn(r+1,1/0,n)});function Zu(t,n){this.xf=n,this.f=t}Zu.prototype["@@transducer/init"]=m,Zu.prototype["@@transducer/result"]=b,Zu.prototype["@@transducer/step"]=function(t,n){return this.f(n)?this.xf["@@transducer/step"](t,n):g(t)};var Qu=n(v(["takeWhile"],function(n){return function(t){return new Zu(n,t)}},function(t,n){for(var r=0,e=n.length;r<e&&t(n[r]);)r+=1;return fn(0,r,n)}));function ti(t,n){this.xf=n,this.f=t}ti.prototype["@@transducer/init"]=m,ti.prototype["@@transducer/result"]=b,ti.prototype["@@transducer/step"]=function(t,n){return this.f(n),this.xf["@@transducer/step"](t,n)};var ni=n(v([],function(n){return function(t){return new ti(n,t)}},function(t,n){return t(n),n}));var ri=n(function(t,n){if(!function(t){return"[object RegExp]"===Object.prototype.toString.call(t)}(t))throw new TypeError("‘test’ requires a value of type RegExp as its first argument; received "+Y(t));return Zt(t).test(n)}),ei=n(function(t,n){return Ze("andThen",n),n.then(t)}),ui=Zr(0,"toLowerCase"),ii=o(function(t){var n=[];for(var r in t)O(r,t)&&(n[n.length]=[r,t[r]]);return n}),oi=o(function(t){var n=[];for(var r in t)n[n.length]=[r,t[r]];return n}),ci=Zr(0,"toUpperCase"),ai=a(4,function(t,n,r,e){return pt(t("function"==typeof n?yt(n):n),r,e)}),fi=o(function(t){for(var n=0,r=[];n<t.length;){for(var e=t[n],u=0;u<e.length;)void 0===r[u]&&(r[u]=[]),r[u].push(e[u]),u+=1;n+=1}return r}),si=i(function(t,n,r){var e={"fantasy-land/of":"function"==typeof t["fantasy-land/of"]?t["fantasy-land/of"]:"function"==typeof t.of?t.of:t};return"function"==typeof r["fantasy-land/traverse"]?r["fantasy-land/traverse"](e,n):"function"==typeof r.traverse?r.traverse(e,n):Bu(e,nt(n,r))}),li="\t\n\v\f\r                　\u2028\u2029\ufeff",pi=!("function"==typeof String.prototype.trim)||li.trim()?o(function(t){var n=RegExp("^["+li+"]["+li+"]*"),r=RegExp("["+li+"]["+li+"]*$");return t.replace(n,"").replace(r,"")}):o(function(t){return t.trim()}),hi=n(function(t,n){return l(t.length,function(){try{return t.apply(this,arguments)}catch(t){return n.apply(this,c([t],arguments))}})}),yi=o(function(t){return function(){return t(Array.prototype.slice.call(arguments,0))}}),di=o(function(t){return Ct(1,t)}),vi=n(function(u,i){return a(u,function(){for(var t,n=1,r=i,e=0;n<=u&&"function"==typeof r;)r=r.apply(this,Array.prototype.slice.call(arguments,e,t=n===u?arguments.length:e+r.length)),n+=1,e=t;return r})}),gi=n(function(t,n){for(var r=t(n),e=[];r&&r.length;)e[e.length]=r[0],r=t(r[1]);return e}),mi=n(hn(Dr,c));function bi(t,n){this.xf=n,this.pred=t,this.items=[]}bi.prototype["@@transducer/init"]=m,bi.prototype["@@transducer/result"]=b,bi.prototype["@@transducer/step"]=function(t,n){return A(this.pred,n,this.items)?t:(this.items.push(n),this.xf["@@transducer/step"](t,n))};function xi(t){return{value:t,"fantasy-land/map":function(){return this}}}var wi=n(v([],function(n){return function(t){return new bi(n,t)}},function(t,n){for(var r,e=0,u=n.length,i=[];e<u;)A(t,r=n[e],i)||(i[i.length]=r),e+=1;return i})),ji=i(function(t,n,r){return wi(t,c(n,r))}),Ai=i(function(t,n,r){return t(r)?r:n(r)}),Oi=Xt(dn),Si=i(function(t,n,r){for(var e=r;!t(e);)e=n(e);return e}),Ei=n(function(n,r){return n in r&&y(r[n])?R(function(t){return Ut(n,t,r)},r[n]):[r]}),_i=o(function(t){var n,r=[];for(n in t)r[r.length]=t[n];return r}),Ii=n(function(t,n){return t(xi)(n).value}),Ni=i(function(t,n,r){return t(r)?n(r):r}),ki=n(function(t,n){for(var r in t)if(O(r,t)&&!t[r](n[r]))return!1;return!0}),Wi=n(function(t,n){for(var r in t)if(O(r,t)&&t[r](n[r]))return!0;return!1}),qi=n(function(t,n){return ki(nt(B,t),n)}),Ui=n(function(t,n){for(var r=new qn,e=0;e<t.length;e+=1)r.add(t[e]);return J(r.has.bind(r),n)}),Fi=n(function(t,n){return!!(!t^!n)}),Pi=n(function(t,n){for(var r,e=0,u=t.length,i=n.length,o=[];e<u;){for(r=0;r<i;)o[o.length]=[t[e],n[r]],r+=1;e+=1}return o}),Bi=n(function(t,n){for(var r=[],e=0,u=Math.min(t.length,n.length);e<u;)r[e]=[t[e],n[e]],e+=1;return r}),Ci=n(function(t,n){for(var r=0,e=Math.min(t.length,n.length),u={};r<e;)u[t[r]]=n[r],r+=1;return u}),Ti=i(function(t,n,r){for(var e=[],u=0,i=Math.min(n.length,r.length);u<i;)e[u]=t(n[u],r[u]),u+=1;return e}),Ri=o(function(n){return a(n.length,function(){var t=arguments;return function(){return n.apply(this,t)}})});t.F=function(){return!1},t.T=function(){return!0},t.__={"@@functional/placeholder":!0},t.add=r,t.addIndex=e,t.addIndexRight=u,t.adjust=h,t.all=w,t.allPass=vt,t.always=gt,t.and=mt,t.andThen=ei,t.any=xt,t.anyPass=wt,t.ap=At,t.aperture=St,t.append=Et,t.apply=_t,t.applySpec=kt,t.applyTo=Wt,t.ascend=qt,t.assoc=Bt,t.assocPath=Pt,t.binary=Tt,t.bind=lt,t.both=zt,t.call=Lt,t.chain=Xt,t.clamp=Yt,t.clone=nn,t.collectBy=rn,t.comparator=en,t.complement=on,t.compose=hn,t.composeWith=mn,t.concat=bn,t.cond=xn,t.construct=An,t.constructN=jn,t.converge=On,t.count=Sn,t.countBy=In,t.curry=wn,t.curryN=a,t.dec=Nn,t.defaultTo=kn,t.descend=Wn,t.difference=Fn,t.differenceWith=Pn,t.dissoc=Tn,t.dissocPath=Cn,t.divide=Rn,t.drop=Dn,t.dropLast=Gn,t.dropLastWhile=$n,t.dropRepeats=Zn,t.dropRepeatsBy=tr,t.dropRepeatsWith=Yn,t.dropWhile=rr,t.either=ur,t.empty=ir,t.endsWith=cr,t.eqBy=Qn,t.eqProps=ar,t.equals=B,t.evolve=fr,t.filter=H,t.find=lr,t.findIndex=hr,t.findLast=dr,t.findLastIndex=gr,t.flatten=mr,t.flip=br,t.forEach=xr,t.forEachObjIndexed=wr,t.fromPairs=jr,t.groupBy=Ar,t.groupWith=Or,t.gt=Sr,t.gte=Er,t.has=Ir,t.hasIn=Nr,t.hasPath=_r,t.head=yn,t.identical=function t(r,n){switch(arguments.length){case 0:return t;case 1:return function t(n){switch(arguments.length){case 0:return t;default:return E(r,n)}};default:return E(r,n)}},t.identity=vn,t.ifElse=kr,t.inc=Wr,t.includes=qr,t.indexBy=Ur,t.indexOf=Fr,t.init=Pr,t.innerJoin=Br,t.insert=Cr,t.insertAll=Tr,t.intersection=zr,t.intersperse=Lr,t.into=Jr,t.invert=Xr,t.invertObj=Yr,t.invoker=Zr,t.is=Qr,t.isEmpty=te,t.isNil=Ft,t.isNotNil=ne,t.join=re,t.juxt=ee,t.keys=q,t.keysIn=ue,t.last=Xn,t.lastIndexOf=ie,t.length=ce,t.lens=ae,t.lensIndex=se,t.lensPath=he,t.lensProp=ye,t.lift=Dt,t.liftN=Mt,t.lt=de,t.lte=ve,t.map=nt,t.mapAccum=ge,t.mapAccumRight=me,t.mapObjIndexed=be,t.match=xe,t.mathMod=we,t.max=Z,t.maxBy=je,t.mean=Oe,t.median=Se,t.memoizeWith=Ee,t.mergeAll=_e,t.mergeDeepLeft=ke,t.mergeDeepRight=We,t.mergeDeepWith=qe,t.mergeDeepWithKey=Ne,t.mergeLeft=Ue,t.mergeRight=Fe,t.mergeWith=Pe,t.mergeWithKey=Ie,t.min=Be,t.minBy=Ce,t.modify=Re,t.modifyPath=Te,t.modulo=Me,t.move=De,t.multiply=ze,t.nAry=Ct,t.negate=Ve,t.none=Ge,t.not=un,t.nth=ut,t.nthArg=Ke,t.o=$e,t.objOf=Gr,t.of=He,t.omit=Je,t.on=Xe,t.once=Ye,t.or=er,t.otherwise=Qe,t.over=tu,t.pair=nu,t.partial=eu,t.partialObject=Le,t.partialRight=uu,t.partition=iu,t.path=pe,t.pathEq=ou,t.pathOr=cu,t.pathSatisfies=au,t.paths=le,t.pick=fu,t.pickAll=su,t.pickBy=lu,t.pipe=ln,t.pipeWith=gn,t.pluck=ot,t.prepend=pu,t.product=hu,t.project=du,t.promap=mu,t.prop=it,t.propEq=bu,t.propIs=xu,t.propOr=wu,t.propSatisfies=ju,t.props=Au,t.range=Ou,t.reduce=dt,t.reduceBy=_n,t.reduceRight=Su,t.reduceWhile=Eu,t.reduced=_u,t.reject=J,t.remove=Bn,t.repeat=Nu,t.replace=ku,t.reverse=pn,t.scan=Pu,t.sequence=Bu,t.set=Cu,t.slice=fn,t.sort=Tu,t.sortBy=Ru,t.sortWith=Mu,t.split=Du,t.splitAt=zu,t.splitEvery=Lu,t.splitWhen=Vu,t.splitWhenever=Gu,t.startsWith=Ku,t.subtract=$u,t.sum=Ae,t.swap=Hu,t.symmetricDifference=Ju,t.symmetricDifferenceWith=Xu,t.tail=sn,t.take=Ln,t.takeLast=or,t.takeLastWhile=Yu,t.takeWhile=Qu,t.tap=ni,t.test=ri,t.thunkify=Ri,t.times=Iu,t.toLower=ui,t.toPairs=ii,t.toPairsIn=oi,t.toString=Y,t.toUpper=ci,t.transduce=ai,t.transpose=fi,t.traverse=si,t.trim=pi,t.tryCatch=hi,t.type=U,t.unapply=yi,t.unary=di,t.uncurryN=vi,t.unfold=gi,t.union=mi,t.unionWith=ji,t.uniq=Dr,t.uniqBy=Mr,t.uniqWith=wi,t.unless=Ai,t.unnest=Oi,t.until=Si,t.unwind=Ei,t.update=fe,t.useWith=yu,t.values=It,t.valuesIn=_i,t.view=Ii,t.when=Ni,t.where=ki,t.whereAny=Wi,t.whereEq=qi,t.without=Ui,t.xor=Fi,t.xprod=Pi,t.zip=Bi,t.zipObj=Ci,t.zipWith=Ti,Object.defineProperty(t,"__esModule",{value:!0})});
