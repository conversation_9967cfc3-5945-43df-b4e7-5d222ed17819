# Student Management System - Security Configuration

# Prevent access to sensitive files
<Files "*.sql">
    Order allow,deny
    <PERSON><PERSON> from all
</Files>

<Files "config.php">
    Order allow,deny
    <PERSON><PERSON> from all
</Files>

# Enable error reporting for development (remove in production)
php_flag display_errors On
php_flag display_startup_errors On

# Set upload limits
php_value upload_max_filesize 5M
php_value post_max_size 5M
php_value max_execution_time 300

# Security headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
</IfModule>

# Prevent directory browsing
Options -Indexes

# Custom error pages (optional)
ErrorDocument 404 /404.php
ErrorDocument 403 /403.php
