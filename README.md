# Student Management System

A comprehensive web application for managing student data with role-based access control, built with Next.js and Supabase.

## Features

### Admin Features
- **User Management**: Create, edit, and delete users (teachers/data entry staff)
- **Student Data Overview**: View all student records across all users
- **Data Export**: Export student data class-wise in Excel format
- **Activity Logs**: Track user activities (who added/edited what and when)
- **Analytics**: View per-user entry statistics and system overview

### User Features
- **Authentication**: Secure login system with Supabase Auth
- **Student Management**: Add, edit, and delete their own student records
- **Data Viewing**: View only their own entered records
- **Search & Filter**: Filter by class, section, and status

### Student Data Fields
- Student Name (required)
- Father's Name (required)
- Class (dropdown: 1-12)
- Section (dropdown: A, B, C)
- Address (textarea)
- Mobile Number (with validation)
- Gender (optional: Male/Female/Other)
- Photo (file upload via Supabase Storage)
- Admission Date (auto/manual)
- Status (Active/Left/Transferred)

## Technology Stack

### Frontend
- **Next.js 14** with App Router
- **TypeScript** for type safety
- **Tailwind CSS** for styling
- **React Hook Form** for form handling
- **Lucide React** for icons
- **React Hot Toast** for notifications

### Backend & Database
- **Supabase** for authentication, database, and storage
- **PostgreSQL** (via Supabase)
- **Row Level Security** for data protection

## Quick Start

### Prerequisites
- Node.js (v18 or higher)
- npm or yarn
- Supabase account

### 1. Clone and Install

```bash
git clone <repository-url>
cd students
npm install
```

### 2. Supabase Setup

1. **Create a new Supabase project** at [supabase.com](https://supabase.com)

2. **Run the database setup script**:
   - Go to your Supabase dashboard
   - Navigate to SQL Editor
   - Copy and paste the contents of `supabase-setup.sql`
   - Run the script to create tables and policies

3. **Configure environment variables**:
   ```bash
   cp .env.local.example .env.local
   ```
   
   Update `.env.local` with your Supabase credentials:
   ```env
   NEXT_PUBLIC_SUPABASE_URL=your-supabase-url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key
   ```

### 3. Create Admin User

1. **Sign up the first user** through the application
2. **Set admin role** in Supabase dashboard:
   - Go to Table Editor → users
   - Find your user and change role to 'admin'

### 4. Start Development Server

```bash
npm run dev
```

Visit [http://localhost:3000](http://localhost:3000)

## Database Schema

### Users Table
- `id` (UUID, Primary Key, references auth.users)
- `email` (Text, Unique)
- `full_name` (Text)
- `role` ('admin' | 'user')
- `is_active` (Boolean)
- `created_at`, `updated_at` (Timestamps)

### Students Table
- `id` (UUID, Primary Key)
- `student_name`, `father_name` (Text, Required)
- `class` (Integer, 1-12)
- `section` ('A' | 'B' | 'C')
- `address`, `mobile_number` (Text, Optional)
- `gender` ('Male' | 'Female' | 'Other', Optional)
- `photo_url` (Text, Optional)
- `admission_date` (Date)
- `status` ('Active' | 'Left' | 'Transferred')
- `created_by` (UUID, Foreign Key to users)
- `created_at`, `updated_at` (Timestamps)

### Activity Logs Table
- `id` (UUID, Primary Key)
- `user_id` (UUID, Foreign Key to users)
- `action`, `entity_type` (Text)
- `entity_id` (UUID, Optional)
- `details`, `ip_address`, `user_agent` (Text, Optional)
- `created_at` (Timestamp)

## Security Features

- **Row Level Security (RLS)** on all tables
- **Role-based access control**
- **Secure authentication** with Supabase Auth
- **Data isolation** (users can only see their own data)
- **Admin oversight** (admins can see all data)

## Deployment

### Deploy to Vercel

1. **Push to GitHub**
2. **Connect to Vercel**
3. **Set environment variables** in Vercel dashboard
4. **Deploy**

### Environment Variables for Production

```env
NEXT_PUBLIC_SUPABASE_URL=your-production-supabase-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-production-supabase-anon-key
```

## Development

### Available Scripts

```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run start        # Start production server
npm run lint         # Run ESLint
```

### Project Structure

```
src/
├── app/                 # Next.js App Router
│   ├── globals.css     # Global styles
│   ├── layout.tsx      # Root layout
│   └── page.tsx        # Home page
├── components/         # React components
│   ├── auth/          # Authentication components
│   ├── dashboard/     # Dashboard components
│   ├── providers/     # Context providers
│   └── ui/            # UI components
└── lib/               # Utilities
    ├── supabase.ts    # Supabase client
    └── supabase-server.ts # Server-side Supabase
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Support

For support or questions, please create an issue in the repository.
