{"author": "<PERSON> <<EMAIL>> (scott.sauyet.com)", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "web": "http://buzzdecafe.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "web": "http://fr.umio.us"}, {"name": "<PERSON>", "email": "<EMAIL>", "web": "http://davidchambers.me"}, {"name": "<PERSON>", "email": "<EMAIL>", "web": "https://github.com/megawac"}], "name": "ramda", "description": "A practical functional library for JavaScript programmers.", "keywords": ["ramda", "functional", "utils", "utilities", "toolkit", "fp", "tacit", "point-free", "curried", "pure", "fantasy-land"], "sideEffects": false, "version": "0.29.1", "homepage": "https://ramdajs.com/", "license": "MIT", "repository": {"type": "git", "url": "git://github.com/ramda/ramda.git"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/ramda"}, "main": "./src/index.js", "exports": {".": {"require": "./src/index.js", "import": "./es/index.js", "default": "./src/index.js"}, "./es/*": "./es/*.js", "./src/*": "./src/*.js", "./dist/*": "./dist/*.js", "./es/": "./es/", "./src/": "./src/", "./dist/": "./dist/"}, "module": "./es/index.js", "unpkg": "dist/ramda.min.js", "jsdelivr": "dist/ramda.min.js", "files": ["es", "src", "dist"], "scripts": {"prebench": "npm run --silent build:cjs", "bench": "node scripts/benchRunner", "bookmarklet": "node scripts/bookmarklet", "build:es": "cross-env BABEL_ENV=es babel source --out-dir es && node ./scripts/addModulePackageScope.js", "build:cjs": "cross-env BABEL_ENV=cjs babel source --out-dir src", "build:umd": "cross-env NODE_ENV=development rollup -c -o dist/ramda.js", "build:umd:min": "cross-env NODE_ENV=production rollup -c -o dist/ramda.min.js", "build": "npm-run-all --parallel build:**", "partial-build": "node ./scripts/partialBuild", "clean": "rimraf es/* src/* dist/* coverage/*", "prepare": "npm run clean && npm run build", "coverage": "BABEL_ENV=cjs nyc --reporter=lcov mocha -- --require @babel/register", "lint": "eslint scripts/bookmarklet scripts/*.js source/*.js source/internal/*.js test/*.js test/**/*.js lib/sauce/*.js lib/bench/*.js", "browser_test": "testem ci", "spec": "cross-env BABEL_ENV=cjs mocha --require @babel/register --reporter spec", "test": "npm-run-all --parallel spec lint"}, "dependencies": {}, "devDependencies": {"@babel/cli": "^7.4.4", "@babel/core": "^7.4.5", "@babel/preset-env": "^7.7.4", "@babel/register": "^7.4.4", "@babel/types": "^7.4.4", "@rollup/plugin-babel": "^5.0.4", "babel-plugin-annotate-pure-calls": "^0.4.0", "babel-plugin-import-export-rename": "^1.0.1", "babelify": "^10.0.0", "benchmark": "~1.0.0", "browserify": "https://api.github.com/repos/browserify/browserify/tarball/9ff7c55cc67a7ddbc64f8e7270bcd75fcc72ce2f", "cli-table": "0.3.x", "cross-env": "^5.2.0", "dox": "latest", "envvar": "^2.0.0", "eslint": "^5.16.0", "eslint-plugin-import": "^2.25.4", "fast-check": "^2.12.0", "handlebars": ">=4.1.2", "js-yaml": "^3.13.1", "mocha": "^6.1.4", "npm-run-all": "^4.1.5", "nyc": "^15.0.1", "rimraf": "^2.6.3", "rollup": "^1.32.1", "rollup-plugin-uglify": "^6.0.4", "sanctuary": "0.7.x", "sanctuary-identity": "^2.1.0", "sanctuary3": "npm:sanctuary@^3.1.0", "sinon": "^7.3.2", "testem": "^2.16.0", "xyz": "^3.0.0"}}