<?php
session_start();

if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit();
}

// Check admin access
if ($_SESSION['user_role'] !== 'admin') {
    header('Location: ../index.php?error=access_denied');
    exit();
}

include '../config/database.php';

// Get all users with their student counts
$sql = "SELECT u.*, 
               COUNT(s.id) as student_count,
               COUNT(CASE WHEN s.status = 'Active' THEN 1 END) as active_students
        FROM users u 
        LEFT JOIN students s ON u.id = s.created_by 
        GROUP BY u.id 
        ORDER BY u.created_at DESC";

$stmt = $pdo->query($sql);
$users = $stmt->fetchAll();

include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include '../includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">User Management</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <a href="add.php" class="btn btn-sm btn-primary">
                        <i class="fas fa-plus"></i> Add User
                    </a>
                </div>
            </div>

            <?php if (isset($_GET['success'])): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle"></i> 
                    <?php 
                    switch($_GET['success']) {
                        case 'added': echo 'User added successfully!'; break;
                        case 'updated': echo 'User updated successfully!'; break;
                        case 'deleted': echo 'User deleted successfully!'; break;
                        default: echo 'Operation completed successfully!';
                    }
                    ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <div class="card shadow">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-users"></i> System Users 
                        <span class="badge bg-primary"><?php echo count($users); ?> users</span>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover data-table">
                            <thead class="table-dark">
                                <tr>
                                    <th>ID</th>
                                    <th>User Details</th>
                                    <th>Role</th>
                                    <th>Students</th>
                                    <th>Status</th>
                                    <th>Created</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($users as $user): ?>
                                <tr>
                                    <td><?php echo $user['id']; ?></td>
                                    <td>
                                        <div>
                                            <strong><?php echo htmlspecialchars($user['full_name']); ?></strong>
                                            <br><small class="text-muted">
                                                <i class="fas fa-at"></i> <?php echo htmlspecialchars($user['username']); ?>
                                            </small>
                                            <br><small class="text-muted">
                                                <i class="fas fa-envelope"></i> <?php echo htmlspecialchars($user['email']); ?>
                                            </small>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?php echo $user['role'] == 'admin' ? 'danger' : 'info'; ?> fs-6">
                                            <i class="fas fa-<?php echo $user['role'] == 'admin' ? 'shield-alt' : 'user'; ?>"></i>
                                            <?php echo ucfirst($user['role']); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <div class="text-center">
                                            <span class="badge bg-primary"><?php echo $user['student_count']; ?></span>
                                            <small class="text-muted d-block">Total</small>
                                            <?php if ($user['active_students'] > 0): ?>
                                                <span class="badge bg-success"><?php echo $user['active_students']; ?></span>
                                                <small class="text-muted d-block">Active</small>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?php echo $user['is_active'] ? 'success' : 'danger'; ?>">
                                            <i class="fas fa-<?php echo $user['is_active'] ? 'check' : 'times'; ?>"></i>
                                            <?php echo $user['is_active'] ? 'Active' : 'Inactive'; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <small class="text-muted">
                                            <?php echo date('M j, Y', strtotime($user['created_at'])); ?>
                                            <br><?php echo date('g:i A', strtotime($user['created_at'])); ?>
                                        </small>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm" role="group">
                                            <a href="view.php?id=<?php echo $user['id']; ?>" 
                                               class="btn btn-outline-info" title="View">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            
                                            <?php if ($user['id'] != $_SESSION['user_id']): ?>
                                            <a href="edit.php?id=<?php echo $user['id']; ?>" 
                                               class="btn btn-outline-warning" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            
                                            <?php if ($user['role'] !== 'admin' || $user['id'] != 1): ?>
                                            <a href="delete.php?id=<?php echo $user['id']; ?>" 
                                               class="btn btn-outline-danger delete-btn" title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                            <?php endif; ?>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
