# Installation Guide - Student Management System

## 🌐 Live Website: https://voicebirdtv.in/Student/

## 📋 Quick Setup Instructions

### 1. Database Setup

**For Hosting Service (Current Setup):**
- Database: `u579681058_SMS` (already configured)
- Import the file: `setup/database.sql` using phpMyAdmin
- The database credentials are already configured in `config/database.php`

**Import Tables using phpMyAdmin:**
1. Login to your hosting control panel
2. Open phpMyAdmin
3. Select database `u579681058_SMS`
4. Click "Import" tab
5. Choose `setup/database.sql` file
6. Click "Go"

**For Local Development:**
```sql
CREATE DATABASE student_management;
```

**Command Line Method (if available):**
```bash
mysql -u u579681058_SMS -p u579681058_SMS < setup/database.sql
```

### 2. Database Configuration

The database is already configured in `config/database.php`:
```php
$host = 'localhost';
$dbname = 'u579681058_SMS';
$username = 'u579681058_SMS';
$password = 'Aj@14021994';
```

### 3. Set File Permissions

```bash
chmod 755 uploads/
chmod 755 uploads/students/
```

### 4. Access Application

Open in browser: `http://localhost/your-folder-name`

## 🔐 Default Login Credentials

**Admin:**
- Username: `admin`
- Password: `admin123`

**User:**
- Username: `user1`
- Password: `user123`

## ✅ Verification

After setup, you should be able to:
- ✅ Login with default credentials
- ✅ Add new students with mobile number validation
- ✅ View responsive design on mobile devices
- ✅ Export student data
- ✅ Manage users (admin only)

## 🔧 Troubleshooting

**Database Connection Error:**
1. Check MySQL is running
2. Verify credentials in `config/database.php`
3. Ensure database exists
4. Confirm tables are imported

**File Upload Issues:**
1. Check folder permissions
2. Verify PHP upload settings
3. Ensure sufficient disk space

**Mobile Features Not Working:**
1. Enable JavaScript in browser
2. Test on actual mobile device
3. Check browser console for errors

## 📱 Mobile Testing

1. Open on mobile device
2. Test mobile number input (should show numeric keypad)
3. Try form validation
4. Test touch interactions
5. Rotate device to test responsiveness

---

**Need Help?** Check the main README.md for detailed documentation.
