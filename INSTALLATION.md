# Installation Guide - Student Management System

## 📋 Quick Setup Instructions

### 1. Database Setup

**Create Database:**
```sql
CREATE DATABASE student_management;
```

**Import Tables:**
- Use phpMyAdmin or MySQL command line
- Import the file: `setup/database.sql`

**Command Line Method:**
```bash
mysql -u root -p student_management < setup/database.sql
```

### 2. Configure Database Connection

Edit `config/database.php`:
```php
$host = 'localhost';        // Your MySQL host
$dbname = 'student_management';  // Database name
$username = 'root';         // Your MySQL username
$password = '';             // Your MySQL password
```

### 3. Set File Permissions

```bash
chmod 755 uploads/
chmod 755 uploads/students/
```

### 4. Access Application

Open in browser: `http://localhost/your-folder-name`

## 🔐 Default Login Credentials

**Admin:**
- Username: `admin`
- Password: `admin123`

**User:**
- Username: `user1`
- Password: `user123`

## ✅ Verification

After setup, you should be able to:
- ✅ Login with default credentials
- ✅ Add new students with mobile number validation
- ✅ View responsive design on mobile devices
- ✅ Export student data
- ✅ Manage users (admin only)

## 🔧 Troubleshooting

**Database Connection Error:**
1. Check MySQL is running
2. Verify credentials in `config/database.php`
3. Ensure database exists
4. Confirm tables are imported

**File Upload Issues:**
1. Check folder permissions
2. Verify PHP upload settings
3. Ensure sufficient disk space

**Mobile Features Not Working:**
1. Enable JavaScript in browser
2. Test on actual mobile device
3. Check browser console for errors

## 📱 Mobile Testing

1. Open on mobile device
2. Test mobile number input (should show numeric keypad)
3. Try form validation
4. Test touch interactions
5. Rotate device to test responsiveness

---

**Need Help?** Check the main README.md for detailed documentation.
