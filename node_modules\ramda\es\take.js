import _curry2 from "./internal/_curry2.js";
import _dispatchable from "./internal/_dispatchable.js";
import _xtake from "./internal/_xtake.js";
import slice from "./slice.js";
/**
 * Returns the first `n` elements of the given list, string, or
 * transducer/transformer (or object with a `take` method).
 *
 * Dispatches to the `take` method of the second argument, if present.
 *
 * @func
 * @memberOf R
 * @since v0.1.0
 * @category List
 * @sig Number -> [a] -> [a]
 * @sig Number -> String -> String
 * @param {Number} n
 * @param {*} list
 * @return {*}
 * @see R.drop
 * @example
 *
 *      R.take(1, ['foo', 'bar', 'baz']); //=> ['foo']
 *      R.take(2, ['foo', 'bar', 'baz']); //=> ['foo', 'bar']
 *      R.take(3, ['foo', 'bar', 'baz']); //=> ['foo', 'bar', 'baz']
 *      <PERSON>.take(4, ['foo', 'bar', 'baz']); //=> ['foo', 'bar', 'baz']
 *      <PERSON>.take(3, 'ramda');               //=> 'ram'
 *
 *      const personnel = [
 *        '<PERSON>ru<PERSON>',
 *        '<PERSON> <PERSON>',
 *        '<PERSON>',
 *        '<PERSON> <PERSON>',
 *        '<PERSON> <PERSON>lli<PERSON>',
 *        '<PERSON> <PERSON>',
 *        '<PERSON> <PERSON>',
 *        '<PERSON> Crotty'
 *      ];
 *
 *      const take<PERSON>ive = R.take(5);
 *      takeFive(personnel);
 *      //=> ['<PERSON> Bru<PERSON>', '<PERSON> <PERSON>', '<PERSON> <PERSON>', '<PERSON> <PERSON>', '<PERSON> Mulligan']
 * @symb R.take(-1, [a, b]) = [a, b]
 * @symb R.take(0, [a, b]) = []
 * @symb R.take(1, [a, b]) = [a]
 * @symb R.take(2, [a, b]) = [a, b]
 */

var take =
/*#__PURE__*/
_curry2(
/*#__PURE__*/
_dispatchable(['take'], _xtake, function take(n, xs) {
  return slice(0, n < 0 ? Infinity : n, xs);
}));

export default take;