'use client'

import { useState, useEffect } from 'react'
import { createClient } from '@/lib/supabase'
import { CheckCircle, XCircle, Loader2, Database, Key, Globe } from 'lucide-react'

export function ConnectionTest() {
  const [connectionStatus, setConnectionStatus] = useState<{
    supabase: 'loading' | 'success' | 'error'
    auth: 'loading' | 'success' | 'error'
    database: 'loading' | 'success' | 'error'
    error?: string
  }>({
    supabase: 'loading',
    auth: 'loading',
    database: 'loading'
  })

  const supabase = createClient()

  useEffect(() => {
    testConnection()
  }, [])

  const testConnection = async () => {
    try {
      // Test 1: Basic Supabase client initialization
      console.log('Testing Supabase client...')
      if (!supabase) {
        throw new Error('Supabase client not initialized')
      }
      setConnectionStatus(prev => ({ ...prev, supabase: 'success' }))

      // Test 2: Auth connection
      console.log('Testing Auth connection...')
      const { data: authData, error: authError } = await supabase.auth.getSession()
      if (authError) {
        console.error('Auth error:', authError)
        setConnectionStatus(prev => ({ ...prev, auth: 'error', error: authError.message }))
      } else {
        console.log('Auth connection successful:', authData)
        setConnectionStatus(prev => ({ ...prev, auth: 'success' }))
      }

      // Test 3: Database connection (try to query a simple table or create one)
      console.log('Testing Database connection...')
      
      // First, let's try to create the users table if it doesn't exist
      const { data: tableData, error: tableError } = await supabase
        .from('users')
        .select('count', { count: 'exact', head: true })

      if (tableError) {
        console.log('Users table might not exist, trying to create it...')
        
        // Try to create the table using RPC or direct SQL
        const { error: createError } = await supabase.rpc('create_users_table_if_not_exists')
        
        if (createError) {
          console.log('RPC failed, table might already exist or need manual creation')
          setConnectionStatus(prev => ({ 
            ...prev, 
            database: 'error', 
            error: 'Database connected but tables need setup. Please run the SQL script manually.' 
          }))
        } else {
          setConnectionStatus(prev => ({ ...prev, database: 'success' }))
        }
      } else {
        console.log('Database connection successful, users table exists')
        setConnectionStatus(prev => ({ ...prev, database: 'success' }))
      }

    } catch (error: any) {
      console.error('Connection test failed:', error)
      setConnectionStatus(prev => ({ 
        ...prev, 
        supabase: 'error', 
        auth: 'error', 
        database: 'error',
        error: error.message 
      }))
    }
  }

  const getStatusIcon = (status: 'loading' | 'success' | 'error') => {
    switch (status) {
      case 'loading':
        return <Loader2 className="h-5 w-5 animate-spin text-blue-500" />
      case 'success':
        return <CheckCircle className="h-5 w-5 text-green-500" />
      case 'error':
        return <XCircle className="h-5 w-5 text-red-500" />
    }
  }

  const getStatusText = (status: 'loading' | 'success' | 'error') => {
    switch (status) {
      case 'loading':
        return 'Testing...'
      case 'success':
        return 'Connected'
      case 'error':
        return 'Failed'
    }
  }

  return (
    <div className="max-w-2xl mx-auto p-6">
      <div className="card p-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
          <Database className="h-6 w-6 mr-2" />
          Supabase Connection Test
        </h2>

        <div className="space-y-4">
          {/* Environment Variables Check */}
          <div className="border rounded-lg p-4">
            <h3 className="font-semibold text-gray-800 mb-3">Environment Variables</h3>
            <div className="space-y-2 text-sm">
              <div className="flex items-center justify-between">
                <span>NEXT_PUBLIC_SUPABASE_URL:</span>
                <span className={`font-mono ${process.env.NEXT_PUBLIC_SUPABASE_URL ? 'text-green-600' : 'text-red-600'}`}>
                  {process.env.NEXT_PUBLIC_SUPABASE_URL ? '✓ Set' : '✗ Missing'}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span>NEXT_PUBLIC_SUPABASE_ANON_KEY:</span>
                <span className={`font-mono ${process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? 'text-green-600' : 'text-red-600'}`}>
                  {process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? '✓ Set' : '✗ Missing'}
                </span>
              </div>
              {process.env.NEXT_PUBLIC_SUPABASE_URL && (
                <div className="text-xs text-gray-500 mt-2">
                  URL: {process.env.NEXT_PUBLIC_SUPABASE_URL}
                </div>
              )}
            </div>
          </div>

          {/* Connection Tests */}
          <div className="border rounded-lg p-4">
            <h3 className="font-semibold text-gray-800 mb-3">Connection Tests</h3>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <Globe className="h-4 w-4 mr-2 text-gray-500" />
                  <span>Supabase Client</span>
                </div>
                <div className="flex items-center space-x-2">
                  {getStatusIcon(connectionStatus.supabase)}
                  <span className="text-sm">{getStatusText(connectionStatus.supabase)}</span>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <Key className="h-4 w-4 mr-2 text-gray-500" />
                  <span>Authentication</span>
                </div>
                <div className="flex items-center space-x-2">
                  {getStatusIcon(connectionStatus.auth)}
                  <span className="text-sm">{getStatusText(connectionStatus.auth)}</span>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <Database className="h-4 w-4 mr-2 text-gray-500" />
                  <span>Database</span>
                </div>
                <div className="flex items-center space-x-2">
                  {getStatusIcon(connectionStatus.database)}
                  <span className="text-sm">{getStatusText(connectionStatus.database)}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Error Display */}
          {connectionStatus.error && (
            <div className="border border-red-200 rounded-lg p-4 bg-red-50">
              <h3 className="font-semibold text-red-800 mb-2">Error Details</h3>
              <p className="text-sm text-red-700">{connectionStatus.error}</p>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex space-x-3">
            <button
              onClick={testConnection}
              className="btn-primary"
            >
              Retry Connection Test
            </button>
            
            {connectionStatus.database === 'error' && (
              <button
                onClick={() => window.open('https://supabase.com/dashboard/project/' + process.env.NEXT_PUBLIC_SUPABASE_URL?.split('//')[1]?.split('.')[0], '_blank')}
                className="btn-secondary"
              >
                Open Supabase Dashboard
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
