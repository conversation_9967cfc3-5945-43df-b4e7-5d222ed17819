<?php
session_start();

if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit();
}

include '../config/database.php';

$student_id = (int)($_GET['id'] ?? 0);

if (!$student_id) {
    header('Location: list.php?error=invalid_id');
    exit();
}

// Get student data
$sql = "SELECT * FROM students WHERE id = ?";
if ($_SESSION['user_role'] !== 'admin') {
    $sql .= " AND created_by = ?";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$student_id, $_SESSION['user_id']]);
} else {
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$student_id]);
}

$student = $stmt->fetch();

if (!$student) {
    header('Location: list.php?error=not_found');
    exit();
}

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['confirm_delete'])) {
    try {
        // Delete photo file if exists
        if ($student['photo_path'] && file_exists('../uploads/students/' . $student['photo_path'])) {
            unlink('../uploads/students/' . $student['photo_path']);
        }
        
        // Delete student record
        $stmt = $pdo->prepare("DELETE FROM students WHERE id = ?");
        $stmt->execute([$student_id]);
        
        // Log activity
        logActivity($pdo, $_SESSION['user_id'], 'STUDENT_DELETED', 'STUDENT', $student_id, 
                   "Deleted student: " . $student['student_name']);
        
        header('Location: list.php?success=deleted');
        exit();
        
    } catch (PDOException $e) {
        $error = 'Failed to delete student: ' . $e->getMessage();
    }
}

include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include '../includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2 text-danger">Delete Student</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <a href="list.php" class="btn btn-sm btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> Back to List
                    </a>
                </div>
            </div>

            <?php if (isset($error)): ?>
                <div class="alert alert-danger" role="alert">
                    <i class="fas fa-exclamation-triangle"></i> <?php echo $error; ?>
                </div>
            <?php endif; ?>

            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="card shadow border-danger">
                        <div class="card-header bg-danger text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-exclamation-triangle"></i> Confirm Deletion
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-warning" role="alert">
                                <h6><i class="fas fa-warning"></i> Warning!</h6>
                                <p class="mb-0">You are about to permanently delete this student record. This action cannot be undone.</p>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-8">
                                    <h6>Student Details:</h6>
                                    <table class="table table-sm">
                                        <tr>
                                            <td><strong>Name:</strong></td>
                                            <td><?php echo htmlspecialchars($student['student_name']); ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Father's Name:</strong></td>
                                            <td><?php echo htmlspecialchars($student['father_name']); ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Class & Section:</strong></td>
                                            <td>Class <?php echo $student['class']; ?> - Section <?php echo $student['section']; ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Mobile:</strong></td>
                                            <td><?php echo $student['mobile_number'] ?: 'Not provided'; ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Status:</strong></td>
                                            <td>
                                                <span class="badge bg-<?php echo $student['status'] == 'Active' ? 'success' : ($student['status'] == 'Left' ? 'danger' : 'warning'); ?>">
                                                    <?php echo $student['status']; ?>
                                                </span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>Created:</strong></td>
                                            <td><?php echo date('M j, Y g:i A', strtotime($student['created_at'])); ?></td>
                                        </tr>
                                    </table>
                                </div>
                                
                                <?php if ($student['photo_path']): ?>
                                <div class="col-md-4 text-center">
                                    <img src="../uploads/students/<?php echo htmlspecialchars($student['photo_path']); ?>" 
                                         alt="Student Photo" 
                                         class="img-fluid rounded shadow"
                                         style="max-height: 150px;">
                                    <p class="text-muted mt-2 mb-0 small">Photo will also be deleted</p>
                                </div>
                                <?php endif; ?>
                            </div>
                            
                            <hr>
                            
                            <form method="POST" id="deleteForm">
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="confirmCheck" required>
                                    <label class="form-check-label" for="confirmCheck">
                                        I understand that this action is permanent and cannot be undone
                                    </label>
                                </div>
                                
                                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                    <a href="view.php?id=<?php echo $student['id']; ?>" class="btn btn-secondary me-md-2">
                                        <i class="fas fa-times"></i> Cancel
                                    </a>
                                    <button type="submit" name="confirm_delete" class="btn btn-danger" id="deleteBtn" disabled>
                                        <i class="fas fa-trash"></i> Delete Permanently
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const confirmCheck = document.getElementById('confirmCheck');
    const deleteBtn = document.getElementById('deleteBtn');
    const deleteForm = document.getElementById('deleteForm');
    
    confirmCheck.addEventListener('change', function() {
        deleteBtn.disabled = !this.checked;
    });
    
    deleteForm.addEventListener('submit', function(e) {
        if (!confirmCheck.checked) {
            e.preventDefault();
            alert('Please confirm that you understand this action is permanent.');
            return false;
        }
        
        if (!confirm('Are you absolutely sure you want to delete this student? This cannot be undone!')) {
            e.preventDefault();
            return false;
        }
    });
});
</script>

<?php include '../includes/footer.php'; ?>
