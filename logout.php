<?php
session_start();

// Include database connection for logging
include 'config/database.php';

// Log the logout activity if user is logged in
if (isset($_SESSION['user_id'])) {
    logActivity($pdo, $_SESSION['user_id'], 'USER_LOGOUT', 'AUTH', null, 'User logged out');
}

// Destroy all session data
session_destroy();

// Redirect to login page with success message
header('Location: ./login.php?message=logged_out');
exit();
?>
