{"name": "@supabase/ssr", "version": "0.0.10", "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "publishConfig": {"access": "public"}, "files": ["dist"], "repository": {"type": "git", "url": "git+https://github.com/supabase/auth-helpers.git"}, "keywords": ["Supabase", "<PERSON><PERSON>", "Next.js", "Svelte Kit", "Remix", "Express"], "author": "Supabase", "license": "MIT", "bugs": {"url": "https://github.com/supabase/auth-helpers/issues"}, "homepage": "https://github.com/supabase/auth-helpers#readme", "dependencies": {"cookie": "^0.5.0", "ramda": "^0.29.0"}, "devDependencies": {"@supabase/supabase-js": "2.33.1", "@types/cookie": "^0.5.1", "@types/ramda": "^0.29.3", "tsup": "^6.7.0", "vitest": "^0.34.6", "tsconfig": "0.1.1"}, "peerDependencies": {"@supabase/supabase-js": "^2.33.1"}, "scripts": {"lint": "tsc", "build": "tsup", "test": "vitest run", "test:watch": "vitest"}}