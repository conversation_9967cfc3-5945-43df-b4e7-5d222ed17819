var _curry1 =
/*#__PURE__*/
require("./internal/_curry1.js");

var _dispatchable =
/*#__PURE__*/
require("./internal/_dispatchable.js");

var _xdropRepeatsWith =
/*#__PURE__*/
require("./internal/_xdropRepeatsWith.js");

var dropRepeatsWith =
/*#__PURE__*/
require("./dropRepeatsWith.js");

var equals =
/*#__PURE__*/
require("./equals.js");
/**
 * Returns a new list without any consecutively repeating elements.
 * [`R.equals`](#equals) is used to determine equality.
 *
 * Acts as a transducer if a transformer is given in list position.
 *
 * @func
 * @memberOf R
 * @since v0.14.0
 * @category List
 * @sig [a] -> [a]
 * @param {Array} list The array to consider.
 * @return {Array} `list` without repeating elements.
 * @see R.transduce
 * @example
 *
 *     R.dropRepeats([1, 1, 1, 2, 3, 4, 4, 2, 2]); //=> [1, 2, 3, 4, 2]
 */


var dropRepeats =
/*#__PURE__*/
_curry1(
/*#__PURE__*/
_dispatchable([], function () {
  return _xdropRepeatsWith(equals);
},
/*#__PURE__*/
dropRepeatsWith(equals)));

module.exports = dropRepeats;