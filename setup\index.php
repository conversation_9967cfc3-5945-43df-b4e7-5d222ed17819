<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Setup - Student Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px 0;
        }
        .setup-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        }
        .setup-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="setup-card">
                    <div class="setup-header text-center py-4">
                        <i class="fas fa-database fa-3x mb-3"></i>
                        <h2>Database Setup</h2>
                        <p class="mb-0">Student Management System</p>
                    </div>
                    
                    <div class="card-body p-4">
                        <?php
                        $message = '';
                        $error = '';
                        
                        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
                            $host = $_POST['host'] ?? 'localhost';
                            $username = $_POST['username'] ?? 'root';
                            $password = $_POST['password'] ?? '';
                            $dbname = $_POST['dbname'] ?? 'student_management';
                            
                            try {
                                // First, connect without database to create it
                                $pdo = new PDO("mysql:host=$host;charset=utf8mb4", $username, $password);
                                $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                                
                                // Create database
                                $pdo->exec("CREATE DATABASE IF NOT EXISTS `$dbname`");
                                $pdo->exec("USE `$dbname`");
                                
                                // Read and execute SQL file
                                $sql = file_get_contents('database.sql');
                                $pdo->exec($sql);
                                
                                // Update config file
                                $config_content = "<?php
// Database configuration
\$host = '$host';
\$dbname = '$dbname';
\$username = '$username';
\$password = '$password';

try {
    \$pdo = new PDO(\"mysql:host=\$host;dbname=\$dbname;charset=utf8mb4\", \$username, \$password);
    \$pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    \$pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
} catch(PDOException \$e) {
    die(\"Connection failed: \" . \$e->getMessage());
}

// Function to log activities
function logActivity(\$pdo, \$user_id, \$action, \$entity_type, \$entity_id = null, \$details = null) {
    \$stmt = \$pdo->prepare(\"
        INSERT INTO activity_logs (user_id, action, entity_type, entity_id, details, ip_address, user_agent, created_at) 
        VALUES (?, ?, ?, ?, ?, ?, ?, NOW())
    \");
    
    \$ip_address = \$_SERVER['REMOTE_ADDR'] ?? null;
    \$user_agent = \$_SERVER['HTTP_USER_AGENT'] ?? null;
    
    \$stmt->execute([\$user_id, \$action, \$entity_type, \$entity_id, \$details, \$ip_address, \$user_agent]);
}

// Function to check if user is admin
function isAdmin() {
    return isset(\$_SESSION['user_role']) && \$_SESSION['user_role'] === 'admin';
}

// Function to require admin access
function requireAdmin() {
    if (!isAdmin()) {
        header('Location: ../index.php?error=access_denied');
        exit();
    }
}

// Function to sanitize input
function sanitizeInput(\$input) {
    return htmlspecialchars(strip_tags(trim(\$input)));
}

// Function to validate email
function validateEmail(\$email) {
    return filter_var(\$email, FILTER_VALIDATE_EMAIL);
}

// Function to hash password
function hashPassword(\$password) {
    return password_hash(\$password, PASSWORD_DEFAULT);
}

// Function to verify password
function verifyPassword(\$password, \$hash) {
    return password_verify(\$password, \$hash);
}
?>";
                                
                                file_put_contents('../config/database.php', $config_content);
                                
                                $message = 'Database setup completed successfully! You can now login to the system.';
                                
                            } catch (PDOException $e) {
                                $error = 'Database setup failed: ' . $e->getMessage();
                            }
                        }
                        ?>
                        
                        <?php if ($message): ?>
                            <div class="alert alert-success" role="alert">
                                <i class="fas fa-check-circle"></i> <?php echo $message; ?>
                                <hr>
                                <div class="text-center">
                                    <a href="../login.php" class="btn btn-success">
                                        <i class="fas fa-sign-in-alt"></i> Go to Login
                                    </a>
                                </div>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($error): ?>
                            <div class="alert alert-danger" role="alert">
                                <i class="fas fa-exclamation-triangle"></i> <?php echo $error; ?>
                            </div>
                        <?php endif; ?>
                        
                        <?php if (!$message): ?>
                        <form method="POST">
                            <div class="mb-3">
                                <label for="host" class="form-label">
                                    <i class="fas fa-server"></i> Database Host
                                </label>
                                <input type="text" class="form-control" id="host" name="host" 
                                       value="localhost" required>
                                <div class="form-text">Usually 'localhost' for local development</div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="dbname" class="form-label">
                                    <i class="fas fa-database"></i> Database Name
                                </label>
                                <input type="text" class="form-control" id="dbname" name="dbname" 
                                       value="student_management" required>
                                <div class="form-text">The database will be created if it doesn't exist</div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="username" class="form-label">
                                    <i class="fas fa-user"></i> Database Username
                                </label>
                                <input type="text" class="form-control" id="username" name="username" 
                                       value="root" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="password" class="form-label">
                                    <i class="fas fa-lock"></i> Database Password
                                </label>
                                <input type="password" class="form-control" id="password" name="password">
                                <div class="form-text">Leave empty if no password is set</div>
                            </div>
                            
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-cog"></i> Setup Database
                                </button>
                            </div>
                        </form>
                        
                        <hr class="my-4">
                        
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle"></i> What this setup will do:</h6>
                            <ul class="mb-0">
                                <li>Create the database (if it doesn't exist)</li>
                                <li>Create all required tables (users, students, activity_logs)</li>
                                <li>Insert default admin and user accounts</li>
                                <li>Add sample student data</li>
                                <li>Configure the database connection</li>
                            </ul>
                        </div>
                        
                        <div class="alert alert-warning">
                            <h6><i class="fas fa-key"></i> Default Login Credentials:</h6>
                            <div class="row">
                                <div class="col-6">
                                    <strong>Admin:</strong><br>
                                    Username: admin<br>
                                    Password: admin123
                                </div>
                                <div class="col-6">
                                    <strong>User:</strong><br>
                                    Username: user1<br>
                                    Password: user123
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
