<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>Mobile Input Test - Student Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px 0;
        }
        
        .test-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        }
        
        .test-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0;
        }
        
        /* Mobile-specific input styles */
        input[type="tel"] {
            font-size: 18px;
            letter-spacing: 1px;
            text-align: center;
            font-weight: 500;
        }
        
        .form-control {
            min-height: 50px;
            font-size: 16px;
        }
        
        .btn {
            min-height: 50px;
            font-size: 16px;
        }
        
        .mobile-demo {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        .validation-status {
            margin-top: 0.5rem;
            padding: 0.5rem;
            border-radius: 5px;
            font-weight: 500;
        }
        
        .status-valid {
            background: #d1edff;
            color: #0c63e4;
            border: 1px solid #b6d7ff;
        }
        
        .status-invalid {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status-neutral {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="test-card">
                    <div class="test-header text-center py-4">
                        <i class="fas fa-mobile-alt fa-3x mb-3"></i>
                        <h2>Mobile Input Test</h2>
                        <p class="mb-0">Test mobile number input with numeric keypad</p>
                    </div>
                    
                    <div class="card-body p-4">
                        <form id="mobileTestForm">
                            <div class="mb-4">
                                <label for="mobile_number" class="form-label">
                                    <i class="fas fa-phone"></i> Mobile Number
                                </label>
                                <input type="tel" 
                                       class="form-control" 
                                       id="mobile_number" 
                                       name="mobile_number" 
                                       pattern="[0-9]{10}" 
                                       maxlength="10" 
                                       placeholder="Enter 10-digit mobile number"
                                       autocomplete="tel"
                                       inputmode="numeric">
                                <div class="form-text">
                                    <i class="fas fa-info-circle"></i> This should open numeric keypad on mobile devices
                                </div>
                                <div id="validationStatus" class="validation-status status-neutral">
                                    <i class="fas fa-clock"></i> Enter mobile number to see validation
                                </div>
                            </div>
                            
                            <div class="mb-4">
                                <label for="student_name" class="form-label">
                                    <i class="fas fa-user"></i> Student Name
                                </label>
                                <input type="text" 
                                       class="form-control" 
                                       id="student_name" 
                                       name="student_name" 
                                       placeholder="Enter student name"
                                       autocomplete="name">
                                <div class="form-text">
                                    <i class="fas fa-info-circle"></i> This should open regular keyboard
                                </div>
                            </div>
                            
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-check"></i> Test Submit
                                </button>
                            </div>
                        </form>
                        
                        <div class="mobile-demo mt-4">
                            <h6><i class="fas fa-lightbulb"></i> Mobile Features:</h6>
                            <ul class="mb-0">
                                <li><strong>Numeric Keypad:</strong> Mobile number field opens number pad</li>
                                <li><strong>Real-time Validation:</strong> Checks for exactly 10 digits</li>
                                <li><strong>Auto-formatting:</strong> Adds space for readability</li>
                                <li><strong>Input Restriction:</strong> Only allows numbers</li>
                                <li><strong>Visual Feedback:</strong> Green for valid, red for invalid</li>
                            </ul>
                        </div>
                        
                        <div class="alert alert-info mt-3">
                            <h6><i class="fas fa-mobile-alt"></i> Testing Instructions:</h6>
                            <p class="mb-2">1. <strong>On Mobile:</strong> Tap the mobile number field - numeric keypad should appear</p>
                            <p class="mb-2">2. <strong>Try entering:</strong> Numbers only (letters will be filtered out)</p>
                            <p class="mb-2">3. <strong>Watch validation:</strong> Status changes as you type</p>
                            <p class="mb-0">4. <strong>Test limit:</strong> Try entering more than 10 digits</p>
                        </div>
                        
                        <div class="text-center mt-4">
                            <a href="login.php" class="btn btn-outline-primary">
                                <i class="fas fa-arrow-left"></i> Back to Login
                            </a>
                            <a href="students/add.php" class="btn btn-outline-success ms-2">
                                <i class="fas fa-user-plus"></i> Add Student Form
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        $(document).ready(function() {
            const mobileInput = $('#mobile_number');
            const statusDiv = $('#validationStatus');
            
            // Mobile number validation and formatting
            mobileInput.on('input', function() {
                let value = $(this).val();
                
                // Remove any non-digit characters
                value = value.replace(/\D/g, '');
                $(this).val(value);
                
                // Update validation status
                if (value.length === 0) {
                    statusDiv.removeClass('status-valid status-invalid').addClass('status-neutral');
                    statusDiv.html('<i class="fas fa-clock"></i> Enter mobile number to see validation');
                    $(this).removeClass('is-valid is-invalid');
                } else if (value.length < 10) {
                    statusDiv.removeClass('status-valid status-neutral').addClass('status-invalid');
                    statusDiv.html(`<i class="fas fa-times-circle"></i> Need ${10 - value.length} more digits (${value.length}/10)`);
                    $(this).removeClass('is-valid').addClass('is-invalid');
                } else if (value.length === 10) {
                    statusDiv.removeClass('status-invalid status-neutral').addClass('status-valid');
                    statusDiv.html('<i class="fas fa-check-circle"></i> Valid mobile number! ✓');
                    $(this).removeClass('is-invalid').addClass('is-valid');
                } else {
                    // Limit to 10 digits
                    $(this).val(value.substring(0, 10));
                    statusDiv.removeClass('status-invalid status-neutral').addClass('status-valid');
                    statusDiv.html('<i class="fas fa-check-circle"></i> Valid mobile number! ✓');
                    $(this).removeClass('is-invalid').addClass('is-valid');
                }
            });
            
            // Form submission test
            $('#mobileTestForm').on('submit', function(e) {
                e.preventDefault();
                
                const mobile = mobileInput.val();
                const name = $('#student_name').val();
                
                if (mobile.length !== 10) {
                    alert('Please enter a valid 10-digit mobile number');
                    return;
                }
                
                alert(`Form would submit with:\nMobile: ${mobile}\nName: ${name}`);
            });
            
            // Add some demo functionality
            $('.btn-outline-primary, .btn-outline-success').on('click', function(e) {
                if ($(this).attr('href').includes('students/add.php') || $(this).attr('href').includes('login.php')) {
                    const mobile = mobileInput.val();
                    if (mobile && mobile.length === 10) {
                        // Store the mobile number for the next form (demo purposes)
                        sessionStorage.setItem('demo_mobile', mobile);
                    }
                }
            });
        });
    </script>
</body>
</html>
