import { ConnectionTest } from '@/components/test/ConnectionTest'

export default function TestConnectionPage() {
  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-4xl mx-auto px-4">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Supabase Connection Test
          </h1>
          <p className="text-gray-600">
            Let's verify that your Supabase connection is working properly
          </p>
        </div>
        
        <ConnectionTest />
        
        <div className="mt-8 text-center">
          <a 
            href="/"
            className="text-primary-600 hover:text-primary-700 font-medium"
          >
            ← Back to Main App
          </a>
        </div>
      </div>
    </div>
  )
}
