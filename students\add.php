<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit();
}

include '../config/database.php';

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $student_name = sanitizeInput($_POST['student_name']);
    $father_name = sanitizeInput($_POST['father_name']);
    $class = (int)$_POST['class'];
    $section = sanitizeInput($_POST['section']);
    $address = sanitizeInput($_POST['address']);
    $mobile_number = sanitizeInput($_POST['mobile_number']);
    $gender = sanitizeInput($_POST['gender']);
    $admission_date = $_POST['admission_date'];
    $status = sanitizeInput($_POST['status']);
    
    // Validation
    if (empty($student_name) || empty($father_name) || empty($class) || empty($section)) {
        $error = 'Please fill in all required fields.';
    } elseif ($class < 1 || $class > 12) {
        $error = 'Class must be between 1 and 12.';
    } elseif (!in_array($section, ['A', 'B', 'C'])) {
        $error = 'Section must be A, B, or C.';
    } elseif (!empty($mobile_number) && !preg_match('/^[0-9]{10}$/', $mobile_number)) {
        $error = 'Mobile number must be exactly 10 digits.';
    } else {
        try {
            // Handle photo upload
            $photo_path = null;
            if (isset($_FILES['photo']) && $_FILES['photo']['error'] === UPLOAD_ERR_OK) {
                $allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
                $file_type = $_FILES['photo']['type'];
                $file_size = $_FILES['photo']['size'];
                
                if (!in_array($file_type, $allowed_types)) {
                    $error = 'Only JPG, PNG, and GIF images are allowed.';
                } elseif ($file_size > 5 * 1024 * 1024) { // 5MB limit
                    $error = 'Image size must be less than 5MB.';
                } else {
                    $file_extension = pathinfo($_FILES['photo']['name'], PATHINFO_EXTENSION);
                    $photo_path = 'student_' . time() . '_' . rand(1000, 9999) . '.' . $file_extension;
                    $upload_path = '../uploads/students/' . $photo_path;
                    
                    if (!move_uploaded_file($_FILES['photo']['tmp_name'], $upload_path)) {
                        $error = 'Failed to upload photo.';
                    }
                }
            }
            
            if (!$error) {
                // Insert student
                $stmt = $pdo->prepare("
                    INSERT INTO students (student_name, father_name, class, section, address, mobile_number, 
                                        gender, photo_path, admission_date, status, created_by) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ");
                
                $stmt->execute([
                    $student_name, $father_name, $class, $section, $address, 
                    $mobile_number ?: null, $gender ?: null, $photo_path, 
                    $admission_date ?: null, $status, $_SESSION['user_id']
                ]);
                
                $student_id = $pdo->lastInsertId();
                
                // Log activity
                logActivity($pdo, $_SESSION['user_id'], 'STUDENT_CREATED', 'STUDENT', $student_id, 
                           "Created student: $student_name");
                
                $success = 'Student added successfully!';
                
                // Clear form data
                $_POST = [];
            }
        } catch (PDOException $e) {
            $error = 'Database error: ' . $e->getMessage();
        }
    }
}

include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <?php include '../includes/sidebar.php'; ?>
        
        <!-- Main Content -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">Add New Student</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <a href="list.php" class="btn btn-sm btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> Back to List
                    </a>
                </div>
            </div>

            <?php if ($error): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-triangle"></i> <?php echo $error; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle"></i> <?php echo $success; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <div class="row">
                <div class="col-lg-8">
                    <div class="card shadow">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-user-plus"></i> Student Information
                            </h5>
                        </div>
                        <div class="card-body">
                            <form method="POST" enctype="multipart/form-data" id="studentForm">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="student_name" class="form-label">
                                            <i class="fas fa-user"></i> Student Name <span class="text-danger">*</span>
                                        </label>
                                        <input type="text" class="form-control" id="student_name" name="student_name" 
                                               value="<?php echo htmlspecialchars($_POST['student_name'] ?? ''); ?>" 
                                               required autocomplete="name">
                                        <div class="invalid-feedback">Please enter student name.</div>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="father_name" class="form-label">
                                            <i class="fas fa-male"></i> Father's Name <span class="text-danger">*</span>
                                        </label>
                                        <input type="text" class="form-control" id="father_name" name="father_name" 
                                               value="<?php echo htmlspecialchars($_POST['father_name'] ?? ''); ?>" 
                                               required autocomplete="name">
                                        <div class="invalid-feedback">Please enter father's name.</div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-4 mb-3">
                                        <label for="class" class="form-label">
                                            <i class="fas fa-graduation-cap"></i> Class <span class="text-danger">*</span>
                                        </label>
                                        <select class="form-select" id="class" name="class" required>
                                            <option value="">Select Class</option>
                                            <?php for ($i = 1; $i <= 12; $i++): ?>
                                                <option value="<?php echo $i; ?>" 
                                                        <?php echo (($_POST['class'] ?? '') == $i) ? 'selected' : ''; ?>>
                                                    Class <?php echo $i; ?>
                                                </option>
                                            <?php endfor; ?>
                                        </select>
                                        <div class="invalid-feedback">Please select a class.</div>
                                    </div>
                                    
                                    <div class="col-md-4 mb-3">
                                        <label for="section" class="form-label">
                                            <i class="fas fa-layer-group"></i> Section <span class="text-danger">*</span>
                                        </label>
                                        <select class="form-select" id="section" name="section" required>
                                            <option value="">Select Section</option>
                                            <option value="A" <?php echo (($_POST['section'] ?? '') == 'A') ? 'selected' : ''; ?>>Section A</option>
                                            <option value="B" <?php echo (($_POST['section'] ?? '') == 'B') ? 'selected' : ''; ?>>Section B</option>
                                            <option value="C" <?php echo (($_POST['section'] ?? '') == 'C') ? 'selected' : ''; ?>>Section C</option>
                                        </select>
                                        <div class="invalid-feedback">Please select a section.</div>
                                    </div>
                                    
                                    <div class="col-md-4 mb-3">
                                        <label for="gender" class="form-label">
                                            <i class="fas fa-venus-mars"></i> Gender
                                        </label>
                                        <select class="form-select" id="gender" name="gender">
                                            <option value="">Select Gender</option>
                                            <option value="Male" <?php echo (($_POST['gender'] ?? '') == 'Male') ? 'selected' : ''; ?>>Male</option>
                                            <option value="Female" <?php echo (($_POST['gender'] ?? '') == 'Female') ? 'selected' : ''; ?>>Female</option>
                                            <option value="Other" <?php echo (($_POST['gender'] ?? '') == 'Other') ? 'selected' : ''; ?>>Other</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="mobile_number" class="form-label">
                                            <i class="fas fa-mobile-alt"></i> Mobile Number
                                        </label>
                                        <input type="tel" class="form-control" id="mobile_number" name="mobile_number" 
                                               value="<?php echo htmlspecialchars($_POST['mobile_number'] ?? ''); ?>" 
                                               pattern="[0-9]{10}" maxlength="10" 
                                               placeholder="Enter 10-digit mobile number"
                                               autocomplete="tel">
                                        <div class="form-text">
                                            <i class="fas fa-info-circle"></i> Enter exactly 10 digits (e.g., 9876543210)
                                        </div>
                                        <div class="invalid-feedback">Mobile number must be exactly 10 digits.</div>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="admission_date" class="form-label">
                                            <i class="fas fa-calendar-alt"></i> Admission Date
                                        </label>
                                        <input type="date" class="form-control" id="admission_date" name="admission_date" 
                                               value="<?php echo $_POST['admission_date'] ?? date('Y-m-d'); ?>">
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="status" class="form-label">
                                            <i class="fas fa-flag"></i> Status
                                        </label>
                                        <select class="form-select" id="status" name="status">
                                            <option value="Active" <?php echo (($_POST['status'] ?? 'Active') == 'Active') ? 'selected' : ''; ?>>Active</option>
                                            <option value="Left" <?php echo (($_POST['status'] ?? '') == 'Left') ? 'selected' : ''; ?>>Left</option>
                                            <option value="Transferred" <?php echo (($_POST['status'] ?? '') == 'Transferred') ? 'selected' : ''; ?>>Transferred</option>
                                        </select>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="photo" class="form-label">
                                            <i class="fas fa-camera"></i> Student Photo
                                        </label>
                                        <input type="file" class="form-control" id="photo" name="photo" 
                                               accept="image/jpeg,image/jpg,image/png,image/gif">
                                        <div class="form-text">
                                            <i class="fas fa-info-circle"></i> Max size: 5MB. Formats: JPG, PNG, GIF
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="address" class="form-label">
                                        <i class="fas fa-map-marker-alt"></i> Address
                                    </label>
                                    <textarea class="form-control" id="address" name="address" rows="3" 
                                              placeholder="Enter complete address"><?php echo htmlspecialchars($_POST['address'] ?? ''); ?></textarea>
                                </div>

                                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                    <a href="list.php" class="btn btn-secondary me-md-2">
                                        <i class="fas fa-times"></i> Cancel
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save"></i> Add Student
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-4">
                    <div class="card shadow">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-info-circle"></i> Instructions
                            </h6>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled">
                                <li class="mb-2">
                                    <i class="fas fa-check text-success"></i> 
                                    Fields marked with <span class="text-danger">*</span> are required
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-mobile-alt text-info"></i> 
                                    Mobile number must be exactly 10 digits
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-camera text-warning"></i> 
                                    Photo is optional but recommended
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-shield-alt text-primary"></i> 
                                    All data is securely stored
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
