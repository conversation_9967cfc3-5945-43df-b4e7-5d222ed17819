<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>Student Management System</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <!-- DataTables CSS -->
    <link href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <style>
        body {
            font-size: 0.875rem;
            background-color: #f8f9fa;
        }
        
        .sidebar {
            position: fixed;
            top: 0;
            bottom: 0;
            left: 0;
            z-index: 100;
            padding: 48px 0 0;
            box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .sidebar-sticky {
            position: relative;
            top: 0;
            height: calc(100vh - 48px);
            padding-top: .5rem;
            overflow-x: hidden;
            overflow-y: auto;
        }
        
        .sidebar .nav-link {
            font-weight: 500;
            color: rgba(255, 255, 255, 0.8);
            transition: all 0.3s ease;
        }
        
        .sidebar .nav-link:hover {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 5px;
        }
        
        .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255, 255, 255, 0.2);
            border-radius: 5px;
        }
        
        .sidebar-heading {
            font-size: .75rem;
            text-transform: uppercase;
            color: rgba(255, 255, 255, 0.6);
        }
        
        .navbar-brand {
            padding-top: .75rem;
            padding-bottom: .75rem;
            font-size: 1rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white !important;
            box-shadow: inset 0 -1px 0 rgba(0, 0, 0, .25);
        }
        
        .border-left-primary {
            border-left: 0.25rem solid #4e73df !important;
        }
        
        .border-left-success {
            border-left: 0.25rem solid #1cc88a !important;
        }
        
        .border-left-info {
            border-left: 0.25rem solid #36b9cc !important;
        }
        
        .border-left-warning {
            border-left: 0.25rem solid #f6c23e !important;
        }
        
        .card {
            box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15) !important;
            border: none;
        }
        
        .text-xs {
            font-size: .7rem;
        }
        
        .font-weight-bold {
            font-weight: 700 !important;
        }
        
        .text-gray-800 {
            color: #5a5c69 !important;
        }
        
        .text-gray-300 {
            color: #dddfeb !important;
        }
        
        .btn-block {
            display: block;
            width: 100%;
        }
        
        .main-content {
            margin-left: 240px;
            padding: 20px;
        }
        
        /* Mobile-specific styles */
        @media (max-width: 768px) {
            .sidebar {
                position: relative;
                height: auto;
            }

            .main-content {
                margin-left: 0;
            }

            /* Larger touch targets for mobile */
            .btn {
                min-height: 44px;
                padding: 12px 16px;
            }

            .form-control, .form-select {
                min-height: 44px;
                font-size: 16px; /* Prevents zoom on iOS */
            }

            /* Mobile number input specific styles */
            input[type="tel"] {
                font-size: 18px;
                letter-spacing: 1px;
                text-align: center;
            }

            /* Better spacing for mobile forms */
            .card-body {
                padding: 1rem;
            }

            .mb-3 {
                margin-bottom: 1.5rem !important;
            }

            /* Responsive table */
            .table-responsive {
                font-size: 0.875rem;
            }

            /* Mobile-friendly alerts */
            .alert {
                margin-bottom: 1rem;
                padding: 0.75rem;
            }
        }

        /* Extra small devices */
        @media (max-width: 576px) {
            .container-fluid {
                padding-left: 0.5rem;
                padding-right: 0.5rem;
            }

            .card {
                margin-bottom: 1rem;
            }

            .btn-group .btn {
                padding: 8px 12px;
                font-size: 0.875rem;
            }

            /* Stack form elements on very small screens */
            .row .col-md-6,
            .row .col-md-4 {
                margin-bottom: 1rem;
            }
        }

        /* Input validation styles for mobile */
        .form-control.is-valid,
        .form-select.is-valid {
            border-color: #198754;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%23198754' d='m2.3 6.73.94-.94 1.44 1.44L7.4 4.5l.94.94L4.66 9.2z'/%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right calc(0.375em + 0.1875rem) center;
            background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
        }

        .form-control.is-invalid,
        .form-select.is-invalid {
            border-color: #dc3545;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath d='m5.8 4.6 1.4 1.4M7.2 4.6l-1.4 1.4'/%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right calc(0.375em + 0.1875rem) center;
            background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
        }
    </style>
</head>
<body>
    <!-- Top Navigation -->
    <nav class="navbar navbar-dark sticky-top flex-md-nowrap p-0 shadow">
        <a class="navbar-brand col-md-3 col-lg-2 me-0 px-3" href="<?php
            $current_path = $_SERVER['PHP_SELF'];
            if (strpos($current_path, '/Student/') !== false) {
                $depth = substr_count(str_replace('/Student/', '', $current_path), '/');
                if ($depth == 0) {
                    echo './';
                } else {
                    echo str_repeat('../', $depth);
                }
            } else {
                echo './';
            }
        ?>index.php">
            <i class="fas fa-graduation-cap"></i> Student Management
        </a>
        
        <button class="navbar-toggler position-absolute d-md-none collapsed" type="button" 
                data-bs-toggle="collapse" data-bs-target="#sidebarMenu">
            <span class="navbar-toggler-icon"></span>
        </button>
        
        <div class="navbar-nav">
            <div class="nav-item text-nowrap">
                <a class="nav-link px-3 text-white" href="<?php
                    $current_path = $_SERVER['PHP_SELF'];
                    if (strpos($current_path, '/Student/') !== false) {
                        $depth = substr_count(str_replace('/Student/', '', $current_path), '/');
                        if ($depth == 0) {
                            echo './';
                        } else {
                            echo str_repeat('../', $depth);
                        }
                    } else {
                        echo './';
                    }
                ?>logout.php">
                    <i class="fas fa-sign-out-alt"></i> Sign out
                </a>
            </div>
        </div>
    </nav>
