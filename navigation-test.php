<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Navigation Test - Student Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; padding: 20px; }
        .test-card { background: rgba(255, 255, 255, 0.95); border-radius: 15px; box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1); }
        .test-header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 15px 15px 0 0; }
        .nav-item { padding: 8px; margin: 4px 0; border: 1px solid #ddd; border-radius: 5px; }
        .nav-item:hover { background: #f8f9fa; }
        .working { border-color: #28a745; background: #d4edda; }
        .broken { border-color: #dc3545; background: #f8d7da; }
    </style>
</head>
<body>
    <div class="container">
        <div class="test-card">
            <div class="test-header text-center py-4">
                <i class="fas fa-route fa-3x mb-3"></i>
                <h2>Navigation Test</h2>
                <p class="mb-1">🌐 Current URL: <?php echo $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI']; ?></p>
                <p class="mb-0">Testing all navigation links for /Student/ folder structure</p>
            </div>
            
            <div class="p-4">
                <div class="row">
                    <div class="col-md-6">
                        <h5><i class="fas fa-link"></i> Main Navigation Links</h5>
                        
                        <div class="nav-item">
                            <strong>Dashboard:</strong>
                            <a href="./index.php" class="btn btn-sm btn-primary ms-2">./index.php</a>
                        </div>
                        
                        <div class="nav-item">
                            <strong>Login:</strong>
                            <a href="./login.php" class="btn btn-sm btn-success ms-2">./login.php</a>
                        </div>
                        
                        <div class="nav-item">
                            <strong>Profile:</strong>
                            <a href="./profile.php" class="btn btn-sm btn-info ms-2">./profile.php</a>
                        </div>
                        
                        <div class="nav-item">
                            <strong>Logout:</strong>
                            <a href="./logout.php" class="btn btn-sm btn-secondary ms-2">./logout.php</a>
                        </div>
                        
                        <h5 class="mt-4"><i class="fas fa-graduation-cap"></i> Student Module</h5>
                        
                        <div class="nav-item">
                            <strong>Student List:</strong>
                            <a href="./students/list.php" class="btn btn-sm btn-primary ms-2">./students/list.php</a>
                        </div>
                        
                        <div class="nav-item">
                            <strong>Add Student:</strong>
                            <a href="./students/add.php" class="btn btn-sm btn-success ms-2">./students/add.php</a>
                        </div>
                        
                        <div class="nav-item">
                            <strong>Export Students:</strong>
                            <a href="./export/students.php" class="btn btn-sm btn-warning ms-2">./export/students.php</a>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <h5><i class="fas fa-users"></i> Admin Module</h5>
                        
                        <div class="nav-item">
                            <strong>User List:</strong>
                            <a href="./users/list.php" class="btn btn-sm btn-primary ms-2">./users/list.php</a>
                        </div>
                        
                        <div class="nav-item">
                            <strong>Add User:</strong>
                            <a href="./users/add.php" class="btn btn-sm btn-success ms-2">./users/add.php</a>
                        </div>
                        
                        <div class="nav-item">
                            <strong>Activity Logs:</strong>
                            <a href="./activity/logs.php" class="btn btn-sm btn-info ms-2">./activity/logs.php</a>
                        </div>
                        
                        <h5 class="mt-4"><i class="fas fa-mobile-alt"></i> Mobile Features</h5>
                        
                        <div class="nav-item">
                            <strong>Mobile Demo:</strong>
                            <a href="./mobile-demo.php" class="btn btn-sm btn-primary ms-2">./mobile-demo.php</a>
                        </div>
                        
                        <div class="nav-item">
                            <strong>Mobile Test:</strong>
                            <a href="./mobile-test.php" class="btn btn-sm btn-success ms-2">./mobile-test.php</a>
                        </div>
                        
                        <h5 class="mt-4"><i class="fas fa-tools"></i> Utilities</h5>
                        
                        <div class="nav-item">
                            <strong>Fix Passwords:</strong>
                            <a href="./fix-passwords.php" class="btn btn-sm btn-warning ms-2">./fix-passwords.php</a>
                        </div>
                        
                        <div class="nav-item">
                            <strong>File List:</strong>
                            <a href="./file-list.php" class="btn btn-sm btn-info ms-2">./file-list.php</a>
                        </div>
                    </div>
                </div>
                
                <div class="alert alert-info mt-4">
                    <h6><i class="fas fa-info-circle"></i> Navigation Path Information:</h6>
                    <p><strong>Current Script:</strong> <?php echo $_SERVER['PHP_SELF']; ?></p>
                    <p><strong>Document Root:</strong> <?php echo $_SERVER['DOCUMENT_ROOT']; ?></p>
                    <p><strong>Request URI:</strong> <?php echo $_SERVER['REQUEST_URI']; ?></p>
                    <p class="mb-0"><strong>Base Path Logic:</strong> All links use <code>./</code> prefix for same directory navigation</p>
                </div>
                
                <div class="alert alert-success mt-3">
                    <h6><i class="fas fa-check-circle"></i> Navigation Strategy:</h6>
                    <ul class="mb-0">
                        <li><strong>Root Level:</strong> <code>./filename.php</code> (same directory)</li>
                        <li><strong>Subdirectories:</strong> <code>./folder/file.php</code> (relative to root)</li>
                        <li><strong>From Subdirectories:</strong> <code>../filename.php</code> (back to root)</li>
                        <li><strong>Sidebar Helper:</strong> Dynamic path calculation based on current location</li>
                    </ul>
                </div>
                
                <div class="text-center mt-4">
                    <a href="./login.php" class="btn btn-primary me-2">
                        <i class="fas fa-sign-in-alt"></i> Go to Login
                    </a>
                    <a href="./mobile-demo.php" class="btn btn-success me-2">
                        <i class="fas fa-mobile-alt"></i> Mobile Demo
                    </a>
                    <a href="./file-list.php" class="btn btn-info">
                        <i class="fas fa-list"></i> File List
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // Test all links and mark them as working or broken
        document.addEventListener('DOMContentLoaded', function() {
            const links = document.querySelectorAll('.nav-item a');
            
            links.forEach(link => {
                link.addEventListener('click', function(e) {
                    // Don't prevent default, let the link work
                    const navItem = this.closest('.nav-item');
                    navItem.classList.add('working');
                    navItem.classList.remove('broken');
                });
            });
        });
    </script>
</body>
</html>
