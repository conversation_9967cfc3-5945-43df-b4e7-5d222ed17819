import { createBrowserClient } from '@supabase/ssr'

export const createClient = () => {
  return createBrowserClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  )
}

export type Database = {
  public: {
    Tables: {
      users: {
        Row: {
          id: string
          email: string
          full_name: string
          role: 'admin' | 'user'
          is_active: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          email: string
          full_name: string
          role?: 'admin' | 'user'
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          full_name?: string
          role?: 'admin' | 'user'
          is_active?: boolean
          updated_at?: string
        }
      }
      students: {
        Row: {
          id: string
          student_name: string
          father_name: string
          class: number
          section: 'A' | 'B' | 'C'
          address: string | null
          mobile_number: string | null
          gender: 'Male' | 'Female' | 'Other' | null
          photo_url: string | null
          admission_date: string | null
          status: 'Active' | 'Left' | 'Transferred'
          created_by: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          student_name: string
          father_name: string
          class: number
          section: 'A' | 'B' | 'C'
          address?: string | null
          mobile_number?: string | null
          gender?: 'Male' | 'Female' | 'Other' | null
          photo_url?: string | null
          admission_date?: string | null
          status?: 'Active' | 'Left' | 'Transferred'
          created_by: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          student_name?: string
          father_name?: string
          class?: number
          section?: 'A' | 'B' | 'C'
          address?: string | null
          mobile_number?: string | null
          gender?: 'Male' | 'Female' | 'Other' | null
          photo_url?: string | null
          admission_date?: string | null
          status?: 'Active' | 'Left' | 'Transferred'
          updated_at?: string
        }
      }
      activity_logs: {
        Row: {
          id: string
          user_id: string
          action: string
          entity_type: string
          entity_id: string | null
          details: string | null
          ip_address: string | null
          user_agent: string | null
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          action: string
          entity_type: string
          entity_id?: string | null
          details?: string | null
          ip_address?: string | null
          user_agent?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          action?: string
          entity_type?: string
          entity_id?: string | null
          details?: string | null
          ip_address?: string | null
          user_agent?: string | null
        }
      }
    }
  }
}
