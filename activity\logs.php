<?php
session_start();

if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit();
}

// Check admin access
if ($_SESSION['user_role'] !== 'admin') {
    header('Location: ../index.php?error=access_denied');
    exit();
}

include '../config/database.php';

// Get filter parameters
$user_filter = $_GET['user'] ?? '';
$action_filter = $_GET['action'] ?? '';
$date_from = $_GET['date_from'] ?? '';
$date_to = $_GET['date_to'] ?? '';

// Build query
$where_conditions = [];
$params = [];

if ($user_filter) {
    $where_conditions[] = "al.user_id = ?";
    $params[] = $user_filter;
}

if ($action_filter) {
    $where_conditions[] = "al.action LIKE ?";
    $params[] = "%$action_filter%";
}

if ($date_from) {
    $where_conditions[] = "DATE(al.created_at) >= ?";
    $params[] = $date_from;
}

if ($date_to) {
    $where_conditions[] = "DATE(al.created_at) <= ?";
    $params[] = $date_to;
}

$where_clause = $where_conditions ? "WHERE " . implode(" AND ", $where_conditions) : "";

// Get activity logs with user info
$sql = "SELECT al.*, u.full_name as user_name, u.username 
        FROM activity_logs al 
        LEFT JOIN users u ON al.user_id = u.id 
        $where_clause 
        ORDER BY al.created_at DESC 
        LIMIT 500";

$stmt = $pdo->prepare($sql);
$stmt->execute($params);
$activities = $stmt->fetchAll();

// Get users for filter dropdown
$users_stmt = $pdo->query("SELECT id, full_name, username FROM users ORDER BY full_name");
$users = $users_stmt->fetchAll();

include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include '../includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">Activity Logs</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="window.print()">
                            <i class="fas fa-print"></i> Print
                        </button>
                    </div>
                </div>
            </div>

            <!-- Filters -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-3">
                            <label for="user" class="form-label">User</label>
                            <select name="user" id="user" class="form-select">
                                <option value="">All Users</option>
                                <?php foreach ($users as $user): ?>
                                    <option value="<?php echo $user['id']; ?>" <?php echo $user_filter == $user['id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($user['full_name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="col-md-3">
                            <label for="action" class="form-label">Action</label>
                            <select name="action" id="action" class="form-select">
                                <option value="">All Actions</option>
                                <option value="LOGIN" <?php echo $action_filter == 'LOGIN' ? 'selected' : ''; ?>>Login/Logout</option>
                                <option value="STUDENT" <?php echo $action_filter == 'STUDENT' ? 'selected' : ''; ?>>Student Actions</option>
                                <option value="USER" <?php echo $action_filter == 'USER' ? 'selected' : ''; ?>>User Actions</option>
                                <option value="EXPORT" <?php echo $action_filter == 'EXPORT' ? 'selected' : ''; ?>>Export Actions</option>
                            </select>
                        </div>
                        
                        <div class="col-md-2">
                            <label for="date_from" class="form-label">From Date</label>
                            <input type="date" name="date_from" id="date_from" class="form-control" 
                                   value="<?php echo htmlspecialchars($date_from); ?>">
                        </div>
                        
                        <div class="col-md-2">
                            <label for="date_to" class="form-label">To Date</label>
                            <input type="date" name="date_to" id="date_to" class="form-control" 
                                   value="<?php echo htmlspecialchars($date_to); ?>">
                        </div>
                        
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i> Filter
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Activity Logs -->
            <div class="card shadow">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-clipboard-list"></i> System Activity 
                        <span class="badge bg-primary"><?php echo count($activities); ?> records</span>
                    </h5>
                </div>
                <div class="card-body">
                    <?php if ($activities): ?>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover data-table">
                                <thead class="table-dark">
                                    <tr>
                                        <th>Date & Time</th>
                                        <th>User</th>
                                        <th>Action</th>
                                        <th>Entity</th>
                                        <th>Details</th>
                                        <th>IP Address</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($activities as $activity): ?>
                                    <tr>
                                        <td>
                                            <small>
                                                <?php echo date('M j, Y', strtotime($activity['created_at'])); ?>
                                                <br><?php echo date('g:i:s A', strtotime($activity['created_at'])); ?>
                                            </small>
                                        </td>
                                        <td>
                                            <strong><?php echo htmlspecialchars($activity['user_name'] ?: 'Unknown'); ?></strong>
                                            <br><small class="text-muted">
                                                @<?php echo htmlspecialchars($activity['username'] ?: 'unknown'); ?>
                                            </small>
                                        </td>
                                        <td>
                                            <span class="badge bg-<?php 
                                                echo match(true) {
                                                    str_contains($activity['action'], 'LOGIN') => 'success',
                                                    str_contains($activity['action'], 'LOGOUT') => 'secondary',
                                                    str_contains($activity['action'], 'CREATED') => 'primary',
                                                    str_contains($activity['action'], 'UPDATED') => 'warning',
                                                    str_contains($activity['action'], 'DELETED') => 'danger',
                                                    str_contains($activity['action'], 'EXPORT') => 'info',
                                                    default => 'dark'
                                                };
                                            ?>">
                                                <?php echo htmlspecialchars($activity['action']); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge bg-light text-dark">
                                                <?php echo htmlspecialchars($activity['entity_type']); ?>
                                            </span>
                                            <?php if ($activity['entity_id']): ?>
                                                <br><small class="text-muted">ID: <?php echo $activity['entity_id']; ?></small>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <small><?php echo htmlspecialchars($activity['details'] ?: '-'); ?></small>
                                        </td>
                                        <td>
                                            <small class="text-muted">
                                                <?php echo htmlspecialchars($activity['ip_address'] ?: '-'); ?>
                                            </small>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-5">
                            <i class="fas fa-clipboard-list fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No activity logs found</h5>
                            <p class="text-muted">Try adjusting your filter criteria.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </main>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
