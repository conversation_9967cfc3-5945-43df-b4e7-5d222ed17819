    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
    
    <!-- Custom JS -->
    <script>
        // Initialize DataTables
        $(document).ready(function() {
            $('.data-table').DataTable({
                "pageLength": 25,
                "responsive": true,
                "order": [[ 0, "desc" ]],
                "language": {
                    "search": "Search:",
                    "lengthMenu": "Show _MENU_ entries",
                    "info": "Showing _START_ to _END_ of _TOTAL_ entries",
                    "paginate": {
                        "first": "First",
                        "last": "Last",
                        "next": "Next",
                        "previous": "Previous"
                    }
                }
            });
            
            // Auto-hide alerts after 5 seconds
            setTimeout(function() {
                $('.alert').fadeOut('slow');
            }, 5000);
            
            // Confirm delete actions
            $('.delete-btn').click(function(e) {
                if (!confirm('Are you sure you want to delete this item? This action cannot be undone.')) {
                    e.preventDefault();
                }
            });
            
            // Form validation
            $('form').submit(function() {
                var isValid = true;
                $(this).find('input[required], select[required], textarea[required]').each(function() {
                    if ($(this).val() === '') {
                        isValid = false;
                        $(this).addClass('is-invalid');
                    } else {
                        $(this).removeClass('is-invalid');
                    }
                });
                
                if (!isValid) {
                    alert('Please fill in all required fields.');
                    return false;
                }
            });
            
            // Mobile number validation - exactly 10 digits for mobile devices
            $('input[name="mobile_number"], input[type="tel"]').on('input', function() {
                var value = $(this).val();
                // Remove any non-digit characters
                value = value.replace(/\D/g, '');
                $(this).val(value);

                if (value && value.length !== 10) {
                    $(this).addClass('is-invalid');
                    if (value.length < 10) {
                        $(this).siblings('.invalid-feedback').text('Mobile number must be exactly 10 digits. Currently ' + value.length + ' digits.');
                    } else {
                        $(this).siblings('.invalid-feedback').text('Mobile number cannot be more than 10 digits.');
                        $(this).val(value.substring(0, 10)); // Limit to 10 digits
                    }
                } else if (value.length === 10) {
                    $(this).removeClass('is-invalid');
                    $(this).addClass('is-valid');
                } else {
                    $(this).removeClass('is-invalid is-valid');
                }
            });

            // Format mobile number as user types (optional - adds spaces for readability)
            $('input[name="mobile_number"], input[type="tel"]').on('keyup', function() {
                var value = $(this).val().replace(/\D/g, '');
                if (value.length >= 6) {
                    // Format as XXXXX XXXXX for better readability while typing
                    var formatted = value.substring(0, 5) + ' ' + value.substring(5, 10);
                    if ($(this).val() !== value) { // Only update if different to avoid cursor jumping
                        $(this).val(formatted.trim());
                    }
                }
            });

            // Remove formatting on form submit to ensure clean data
            $('form').on('submit', function() {
                $('input[name="mobile_number"], input[type="tel"]').each(function() {
                    var cleanValue = $(this).val().replace(/\D/g, '');
                    $(this).val(cleanValue);
                });
            });
            
            // Email validation
            $('input[type="email"]').on('input', function() {
                var value = $(this).val();
                var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (value && !emailRegex.test(value)) {
                    $(this).addClass('is-invalid');
                    $(this).next('.invalid-feedback').text('Please enter a valid email address.');
                } else {
                    $(this).removeClass('is-invalid');
                }
            });
        });
        
        // Export functions
        function exportToExcel(url) {
            window.location.href = url;
        }
        
        // Print function
        function printPage() {
            window.print();
        }
        
        // Show loading spinner
        function showLoading() {
            $('#loading').show();
        }
        
        // Hide loading spinner
        function hideLoading() {
            $('#loading').hide();
        }
    </script>
    
    <!-- Loading Spinner -->
    <div id="loading" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 9999;">
        <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); color: white; text-align: center;">
            <i class="fas fa-spinner fa-spin fa-3x"></i>
            <p class="mt-3">Loading...</p>
        </div>
    </div>
</body>
</html>
