<?php
// Database configuration - Updated for hosting service
$host = 'localhost';
$dbname = 'u579681058_SMS';
$username = 'u579681058_SMS';
$password = 'Aj@14021994';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
} catch(PDOException $e) {
    // Show user-friendly error message
    die("
    <div style='font-family: Arial, sans-serif; max-width: 600px; margin: 50px auto; padding: 20px; border: 1px solid #ddd; border-radius: 10px; background: #f9f9f9;'>
        <h2 style='color: #d32f2f; margin-bottom: 20px;'>
            <i style='font-size: 24px;'>⚠️</i> Database Connection Error
        </h2>
        <p style='margin-bottom: 15px;'><strong>Could not connect to the database.</strong></p>
        <p style='margin-bottom: 15px;'>Please check:</p>
        <ul style='margin-bottom: 20px;'>
            <li>Database server is running</li>
            <li>Database credentials in <code>config/database.php</code> are correct</li>
            <li>Database <strong>student_management</strong> exists</li>
            <li>Required tables are imported from <code>setup/database.sql</code></li>
        </ul>
        <p style='color: #666; font-size: 14px;'><strong>Error:</strong> " . $e->getMessage() . "</p>
    </div>
    ");
}

// Function to log activities
function logActivity($pdo, $user_id, $action, $entity_type, $entity_id = null, $details = null) {
    $stmt = $pdo->prepare("
        INSERT INTO activity_logs (user_id, action, entity_type, entity_id, details, ip_address, user_agent, created_at) 
        VALUES (?, ?, ?, ?, ?, ?, ?, NOW())
    ");
    
    $ip_address = $_SERVER['REMOTE_ADDR'] ?? null;
    $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? null;
    
    $stmt->execute([$user_id, $action, $entity_type, $entity_id, $details, $ip_address, $user_agent]);
}

// Function to check if user is admin
function isAdmin() {
    return isset($_SESSION['user_role']) && $_SESSION['user_role'] === 'admin';
}

// Function to require admin access
function requireAdmin() {
    if (!isAdmin()) {
        header('Location: ../index.php?error=access_denied');
        exit();
    }
}

// Function to sanitize input
function sanitizeInput($input) {
    return htmlspecialchars(strip_tags(trim($input)));
}

// Function to validate email
function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL);
}

// Function to hash password
function hashPassword($password) {
    return password_hash($password, PASSWORD_DEFAULT);
}

// Function to verify password
function verifyPassword($password, $hash) {
    return password_verify($password, $hash);
}
?>
