"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/ws";
exports.ids = ["vendor-chunks/ws"];
exports.modules = {

/***/ "(ssr)/./node_modules/ws/index.js":
/*!**********************************!*\
  !*** ./node_modules/ws/index.js ***!
  \**********************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst WebSocket = __webpack_require__(/*! ./lib/websocket */ \"(ssr)/./node_modules/ws/lib/websocket.js\");\nWebSocket.createWebSocketStream = __webpack_require__(/*! ./lib/stream */ \"(ssr)/./node_modules/ws/lib/stream.js\");\nWebSocket.Server = __webpack_require__(/*! ./lib/websocket-server */ \"(ssr)/./node_modules/ws/lib/websocket-server.js\");\nWebSocket.Receiver = __webpack_require__(/*! ./lib/receiver */ \"(ssr)/./node_modules/ws/lib/receiver.js\");\nWebSocket.Sender = __webpack_require__(/*! ./lib/sender */ \"(ssr)/./node_modules/ws/lib/sender.js\");\nWebSocket.WebSocket = WebSocket;\nWebSocket.WebSocketServer = WebSocket.Server;\nmodule.exports = WebSocket;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvd3MvaW5kZXguanMiLCJtYXBwaW5ncyI6IkFBQUE7QUFFQSxNQUFNQSxZQUFZQyxtQkFBT0EsQ0FBQztBQUUxQkQsVUFBVUUscUJBQXFCLEdBQUdELG1CQUFPQSxDQUFDO0FBQzFDRCxVQUFVRyxNQUFNLEdBQUdGLG1CQUFPQSxDQUFDO0FBQzNCRCxVQUFVSSxRQUFRLEdBQUdILG1CQUFPQSxDQUFDO0FBQzdCRCxVQUFVSyxNQUFNLEdBQUdKLG1CQUFPQSxDQUFDO0FBRTNCRCxVQUFVQSxTQUFTLEdBQUdBO0FBQ3RCQSxVQUFVTSxlQUFlLEdBQUdOLFVBQVVHLE1BQU07QUFFNUNJLE9BQU9DLE9BQU8sR0FBR1IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdHVkZW50LW1hbmFnZW1lbnQtc3lzdGVtLy4vbm9kZV9tb2R1bGVzL3dzL2luZGV4LmpzPzE1NGUiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG5jb25zdCBXZWJTb2NrZXQgPSByZXF1aXJlKCcuL2xpYi93ZWJzb2NrZXQnKTtcblxuV2ViU29ja2V0LmNyZWF0ZVdlYlNvY2tldFN0cmVhbSA9IHJlcXVpcmUoJy4vbGliL3N0cmVhbScpO1xuV2ViU29ja2V0LlNlcnZlciA9IHJlcXVpcmUoJy4vbGliL3dlYnNvY2tldC1zZXJ2ZXInKTtcbldlYlNvY2tldC5SZWNlaXZlciA9IHJlcXVpcmUoJy4vbGliL3JlY2VpdmVyJyk7XG5XZWJTb2NrZXQuU2VuZGVyID0gcmVxdWlyZSgnLi9saWIvc2VuZGVyJyk7XG5cbldlYlNvY2tldC5XZWJTb2NrZXQgPSBXZWJTb2NrZXQ7XG5XZWJTb2NrZXQuV2ViU29ja2V0U2VydmVyID0gV2ViU29ja2V0LlNlcnZlcjtcblxubW9kdWxlLmV4cG9ydHMgPSBXZWJTb2NrZXQ7XG4iXSwibmFtZXMiOlsiV2ViU29ja2V0IiwicmVxdWlyZSIsImNyZWF0ZVdlYlNvY2tldFN0cmVhbSIsIlNlcnZlciIsIlJlY2VpdmVyIiwiU2VuZGVyIiwiV2ViU29ja2V0U2VydmVyIiwibW9kdWxlIiwiZXhwb3J0cyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ws/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ws/lib/buffer-util.js":
/*!********************************************!*\
  !*** ./node_modules/ws/lib/buffer-util.js ***!
  \********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst { EMPTY_BUFFER } = __webpack_require__(/*! ./constants */ \"(ssr)/./node_modules/ws/lib/constants.js\");\nconst FastBuffer = Buffer[Symbol.species];\n/**\n * Merges an array of buffers into a new buffer.\n *\n * @param {Buffer[]} list The array of buffers to concat\n * @param {Number} totalLength The total length of buffers in the list\n * @return {Buffer} The resulting buffer\n * @public\n */ function concat(list, totalLength) {\n    if (list.length === 0) return EMPTY_BUFFER;\n    if (list.length === 1) return list[0];\n    const target = Buffer.allocUnsafe(totalLength);\n    let offset = 0;\n    for(let i = 0; i < list.length; i++){\n        const buf = list[i];\n        target.set(buf, offset);\n        offset += buf.length;\n    }\n    if (offset < totalLength) {\n        return new FastBuffer(target.buffer, target.byteOffset, offset);\n    }\n    return target;\n}\n/**\n * Masks a buffer using the given mask.\n *\n * @param {Buffer} source The buffer to mask\n * @param {Buffer} mask The mask to use\n * @param {Buffer} output The buffer where to store the result\n * @param {Number} offset The offset at which to start writing\n * @param {Number} length The number of bytes to mask.\n * @public\n */ function _mask(source, mask, output, offset, length) {\n    for(let i = 0; i < length; i++){\n        output[offset + i] = source[i] ^ mask[i & 3];\n    }\n}\n/**\n * Unmasks a buffer using the given mask.\n *\n * @param {Buffer} buffer The buffer to unmask\n * @param {Buffer} mask The mask to use\n * @public\n */ function _unmask(buffer, mask) {\n    for(let i = 0; i < buffer.length; i++){\n        buffer[i] ^= mask[i & 3];\n    }\n}\n/**\n * Converts a buffer to an `ArrayBuffer`.\n *\n * @param {Buffer} buf The buffer to convert\n * @return {ArrayBuffer} Converted buffer\n * @public\n */ function toArrayBuffer(buf) {\n    if (buf.length === buf.buffer.byteLength) {\n        return buf.buffer;\n    }\n    return buf.buffer.slice(buf.byteOffset, buf.byteOffset + buf.length);\n}\n/**\n * Converts `data` to a `Buffer`.\n *\n * @param {*} data The data to convert\n * @return {Buffer} The buffer\n * @throws {TypeError}\n * @public\n */ function toBuffer(data) {\n    toBuffer.readOnly = true;\n    if (Buffer.isBuffer(data)) return data;\n    let buf;\n    if (data instanceof ArrayBuffer) {\n        buf = new FastBuffer(data);\n    } else if (ArrayBuffer.isView(data)) {\n        buf = new FastBuffer(data.buffer, data.byteOffset, data.byteLength);\n    } else {\n        buf = Buffer.from(data);\n        toBuffer.readOnly = false;\n    }\n    return buf;\n}\nmodule.exports = {\n    concat,\n    mask: _mask,\n    toArrayBuffer,\n    toBuffer,\n    unmask: _unmask\n};\n/* istanbul ignore else  */ if (!process.env.WS_NO_BUFFER_UTIL) {\n    try {\n        const bufferUtil = __webpack_require__(/*! bufferutil */ \"?32c4\");\n        module.exports.mask = function(source, mask, output, offset, length) {\n            if (length < 48) _mask(source, mask, output, offset, length);\n            else bufferUtil.mask(source, mask, output, offset, length);\n        };\n        module.exports.unmask = function(buffer, mask) {\n            if (buffer.length < 32) _unmask(buffer, mask);\n            else bufferUtil.unmask(buffer, mask);\n        };\n    } catch (e) {\n    // Continue regardless of the error.\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ws/lib/buffer-util.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ws/lib/constants.js":
/*!******************************************!*\
  !*** ./node_modules/ws/lib/constants.js ***!
  \******************************************/
/***/ ((module) => {

eval("\nconst BINARY_TYPES = [\n    \"nodebuffer\",\n    \"arraybuffer\",\n    \"fragments\"\n];\nconst hasBlob = typeof Blob !== \"undefined\";\nif (hasBlob) BINARY_TYPES.push(\"blob\");\nmodule.exports = {\n    BINARY_TYPES,\n    EMPTY_BUFFER: Buffer.alloc(0),\n    GUID: \"258EAFA5-E914-47DA-95CA-C5AB0DC85B11\",\n    hasBlob,\n    kForOnEventAttribute: Symbol(\"kIsForOnEventAttribute\"),\n    kListener: Symbol(\"kListener\"),\n    kStatusCode: Symbol(\"status-code\"),\n    kWebSocket: Symbol(\"websocket\"),\n    NOOP: ()=>{}\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvd3MvbGliL2NvbnN0YW50cy5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUVBLE1BQU1BLGVBQWU7SUFBQztJQUFjO0lBQWU7Q0FBWTtBQUMvRCxNQUFNQyxVQUFVLE9BQU9DLFNBQVM7QUFFaEMsSUFBSUQsU0FBU0QsYUFBYUcsSUFBSSxDQUFDO0FBRS9CQyxPQUFPQyxPQUFPLEdBQUc7SUFDZkw7SUFDQU0sY0FBY0MsT0FBT0MsS0FBSyxDQUFDO0lBQzNCQyxNQUFNO0lBQ05SO0lBQ0FTLHNCQUFzQkMsT0FBTztJQUM3QkMsV0FBV0QsT0FBTztJQUNsQkUsYUFBYUYsT0FBTztJQUNwQkcsWUFBWUgsT0FBTztJQUNuQkksTUFBTSxLQUFPO0FBQ2YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdHVkZW50LW1hbmFnZW1lbnQtc3lzdGVtLy4vbm9kZV9tb2R1bGVzL3dzL2xpYi9jb25zdGFudHMuanM/YzJhMyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbmNvbnN0IEJJTkFSWV9UWVBFUyA9IFsnbm9kZWJ1ZmZlcicsICdhcnJheWJ1ZmZlcicsICdmcmFnbWVudHMnXTtcbmNvbnN0IGhhc0Jsb2IgPSB0eXBlb2YgQmxvYiAhPT0gJ3VuZGVmaW5lZCc7XG5cbmlmIChoYXNCbG9iKSBCSU5BUllfVFlQRVMucHVzaCgnYmxvYicpO1xuXG5tb2R1bGUuZXhwb3J0cyA9IHtcbiAgQklOQVJZX1RZUEVTLFxuICBFTVBUWV9CVUZGRVI6IEJ1ZmZlci5hbGxvYygwKSxcbiAgR1VJRDogJzI1OEVBRkE1LUU5MTQtNDdEQS05NUNBLUM1QUIwREM4NUIxMScsXG4gIGhhc0Jsb2IsXG4gIGtGb3JPbkV2ZW50QXR0cmlidXRlOiBTeW1ib2woJ2tJc0Zvck9uRXZlbnRBdHRyaWJ1dGUnKSxcbiAga0xpc3RlbmVyOiBTeW1ib2woJ2tMaXN0ZW5lcicpLFxuICBrU3RhdHVzQ29kZTogU3ltYm9sKCdzdGF0dXMtY29kZScpLFxuICBrV2ViU29ja2V0OiBTeW1ib2woJ3dlYnNvY2tldCcpLFxuICBOT09QOiAoKSA9PiB7fVxufTtcbiJdLCJuYW1lcyI6WyJCSU5BUllfVFlQRVMiLCJoYXNCbG9iIiwiQmxvYiIsInB1c2giLCJtb2R1bGUiLCJleHBvcnRzIiwiRU1QVFlfQlVGRkVSIiwiQnVmZmVyIiwiYWxsb2MiLCJHVUlEIiwia0Zvck9uRXZlbnRBdHRyaWJ1dGUiLCJTeW1ib2wiLCJrTGlzdGVuZXIiLCJrU3RhdHVzQ29kZSIsImtXZWJTb2NrZXQiLCJOT09QIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ws/lib/constants.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ws/lib/event-target.js":
/*!*********************************************!*\
  !*** ./node_modules/ws/lib/event-target.js ***!
  \*********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst { kForOnEventAttribute, kListener } = __webpack_require__(/*! ./constants */ \"(ssr)/./node_modules/ws/lib/constants.js\");\nconst kCode = Symbol(\"kCode\");\nconst kData = Symbol(\"kData\");\nconst kError = Symbol(\"kError\");\nconst kMessage = Symbol(\"kMessage\");\nconst kReason = Symbol(\"kReason\");\nconst kTarget = Symbol(\"kTarget\");\nconst kType = Symbol(\"kType\");\nconst kWasClean = Symbol(\"kWasClean\");\n/**\n * Class representing an event.\n */ class Event {\n    /**\n   * Create a new `Event`.\n   *\n   * @param {String} type The name of the event\n   * @throws {TypeError} If the `type` argument is not specified\n   */ constructor(type){\n        this[kTarget] = null;\n        this[kType] = type;\n    }\n    /**\n   * @type {*}\n   */ get target() {\n        return this[kTarget];\n    }\n    /**\n   * @type {String}\n   */ get type() {\n        return this[kType];\n    }\n}\nObject.defineProperty(Event.prototype, \"target\", {\n    enumerable: true\n});\nObject.defineProperty(Event.prototype, \"type\", {\n    enumerable: true\n});\n/**\n * Class representing a close event.\n *\n * @extends Event\n */ class CloseEvent extends Event {\n    /**\n   * Create a new `CloseEvent`.\n   *\n   * @param {String} type The name of the event\n   * @param {Object} [options] A dictionary object that allows for setting\n   *     attributes via object members of the same name\n   * @param {Number} [options.code=0] The status code explaining why the\n   *     connection was closed\n   * @param {String} [options.reason=''] A human-readable string explaining why\n   *     the connection was closed\n   * @param {Boolean} [options.wasClean=false] Indicates whether or not the\n   *     connection was cleanly closed\n   */ constructor(type, options = {}){\n        super(type);\n        this[kCode] = options.code === undefined ? 0 : options.code;\n        this[kReason] = options.reason === undefined ? \"\" : options.reason;\n        this[kWasClean] = options.wasClean === undefined ? false : options.wasClean;\n    }\n    /**\n   * @type {Number}\n   */ get code() {\n        return this[kCode];\n    }\n    /**\n   * @type {String}\n   */ get reason() {\n        return this[kReason];\n    }\n    /**\n   * @type {Boolean}\n   */ get wasClean() {\n        return this[kWasClean];\n    }\n}\nObject.defineProperty(CloseEvent.prototype, \"code\", {\n    enumerable: true\n});\nObject.defineProperty(CloseEvent.prototype, \"reason\", {\n    enumerable: true\n});\nObject.defineProperty(CloseEvent.prototype, \"wasClean\", {\n    enumerable: true\n});\n/**\n * Class representing an error event.\n *\n * @extends Event\n */ class ErrorEvent extends Event {\n    /**\n   * Create a new `ErrorEvent`.\n   *\n   * @param {String} type The name of the event\n   * @param {Object} [options] A dictionary object that allows for setting\n   *     attributes via object members of the same name\n   * @param {*} [options.error=null] The error that generated this event\n   * @param {String} [options.message=''] The error message\n   */ constructor(type, options = {}){\n        super(type);\n        this[kError] = options.error === undefined ? null : options.error;\n        this[kMessage] = options.message === undefined ? \"\" : options.message;\n    }\n    /**\n   * @type {*}\n   */ get error() {\n        return this[kError];\n    }\n    /**\n   * @type {String}\n   */ get message() {\n        return this[kMessage];\n    }\n}\nObject.defineProperty(ErrorEvent.prototype, \"error\", {\n    enumerable: true\n});\nObject.defineProperty(ErrorEvent.prototype, \"message\", {\n    enumerable: true\n});\n/**\n * Class representing a message event.\n *\n * @extends Event\n */ class MessageEvent extends Event {\n    /**\n   * Create a new `MessageEvent`.\n   *\n   * @param {String} type The name of the event\n   * @param {Object} [options] A dictionary object that allows for setting\n   *     attributes via object members of the same name\n   * @param {*} [options.data=null] The message content\n   */ constructor(type, options = {}){\n        super(type);\n        this[kData] = options.data === undefined ? null : options.data;\n    }\n    /**\n   * @type {*}\n   */ get data() {\n        return this[kData];\n    }\n}\nObject.defineProperty(MessageEvent.prototype, \"data\", {\n    enumerable: true\n});\n/**\n * This provides methods for emulating the `EventTarget` interface. It's not\n * meant to be used directly.\n *\n * @mixin\n */ const EventTarget = {\n    /**\n   * Register an event listener.\n   *\n   * @param {String} type A string representing the event type to listen for\n   * @param {(Function|Object)} handler The listener to add\n   * @param {Object} [options] An options object specifies characteristics about\n   *     the event listener\n   * @param {Boolean} [options.once=false] A `Boolean` indicating that the\n   *     listener should be invoked at most once after being added. If `true`,\n   *     the listener would be automatically removed when invoked.\n   * @public\n   */ addEventListener (type, handler, options = {}) {\n        for (const listener of this.listeners(type)){\n            if (!options[kForOnEventAttribute] && listener[kListener] === handler && !listener[kForOnEventAttribute]) {\n                return;\n            }\n        }\n        let wrapper;\n        if (type === \"message\") {\n            wrapper = function onMessage(data, isBinary) {\n                const event = new MessageEvent(\"message\", {\n                    data: isBinary ? data : data.toString()\n                });\n                event[kTarget] = this;\n                callListener(handler, this, event);\n            };\n        } else if (type === \"close\") {\n            wrapper = function onClose(code, message) {\n                const event = new CloseEvent(\"close\", {\n                    code,\n                    reason: message.toString(),\n                    wasClean: this._closeFrameReceived && this._closeFrameSent\n                });\n                event[kTarget] = this;\n                callListener(handler, this, event);\n            };\n        } else if (type === \"error\") {\n            wrapper = function onError(error) {\n                const event = new ErrorEvent(\"error\", {\n                    error,\n                    message: error.message\n                });\n                event[kTarget] = this;\n                callListener(handler, this, event);\n            };\n        } else if (type === \"open\") {\n            wrapper = function onOpen() {\n                const event = new Event(\"open\");\n                event[kTarget] = this;\n                callListener(handler, this, event);\n            };\n        } else {\n            return;\n        }\n        wrapper[kForOnEventAttribute] = !!options[kForOnEventAttribute];\n        wrapper[kListener] = handler;\n        if (options.once) {\n            this.once(type, wrapper);\n        } else {\n            this.on(type, wrapper);\n        }\n    },\n    /**\n   * Remove an event listener.\n   *\n   * @param {String} type A string representing the event type to remove\n   * @param {(Function|Object)} handler The listener to remove\n   * @public\n   */ removeEventListener (type, handler) {\n        for (const listener of this.listeners(type)){\n            if (listener[kListener] === handler && !listener[kForOnEventAttribute]) {\n                this.removeListener(type, listener);\n                break;\n            }\n        }\n    }\n};\nmodule.exports = {\n    CloseEvent,\n    ErrorEvent,\n    Event,\n    EventTarget,\n    MessageEvent\n};\n/**\n * Call an event listener\n *\n * @param {(Function|Object)} listener The listener to call\n * @param {*} thisArg The value to use as `this`` when calling the listener\n * @param {Event} event The event to pass to the listener\n * @private\n */ function callListener(listener, thisArg, event) {\n    if (typeof listener === \"object\" && listener.handleEvent) {\n        listener.handleEvent.call(listener, event);\n    } else {\n        listener.call(thisArg, event);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ws/lib/event-target.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ws/lib/extension.js":
/*!******************************************!*\
  !*** ./node_modules/ws/lib/extension.js ***!
  \******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst { tokenChars } = __webpack_require__(/*! ./validation */ \"(ssr)/./node_modules/ws/lib/validation.js\");\n/**\n * Adds an offer to the map of extension offers or a parameter to the map of\n * parameters.\n *\n * @param {Object} dest The map of extension offers or parameters\n * @param {String} name The extension or parameter name\n * @param {(Object|Boolean|String)} elem The extension parameters or the\n *     parameter value\n * @private\n */ function push(dest, name, elem) {\n    if (dest[name] === undefined) dest[name] = [\n        elem\n    ];\n    else dest[name].push(elem);\n}\n/**\n * Parses the `Sec-WebSocket-Extensions` header into an object.\n *\n * @param {String} header The field value of the header\n * @return {Object} The parsed object\n * @public\n */ function parse(header) {\n    const offers = Object.create(null);\n    let params = Object.create(null);\n    let mustUnescape = false;\n    let isEscaping = false;\n    let inQuotes = false;\n    let extensionName;\n    let paramName;\n    let start = -1;\n    let code = -1;\n    let end = -1;\n    let i = 0;\n    for(; i < header.length; i++){\n        code = header.charCodeAt(i);\n        if (extensionName === undefined) {\n            if (end === -1 && tokenChars[code] === 1) {\n                if (start === -1) start = i;\n            } else if (i !== 0 && (code === 0x20 /* ' ' */  || code === 0x09)) {\n                if (end === -1 && start !== -1) end = i;\n            } else if (code === 0x3b /* ';' */  || code === 0x2c /* ',' */ ) {\n                if (start === -1) {\n                    throw new SyntaxError(`Unexpected character at index ${i}`);\n                }\n                if (end === -1) end = i;\n                const name = header.slice(start, end);\n                if (code === 0x2c) {\n                    push(offers, name, params);\n                    params = Object.create(null);\n                } else {\n                    extensionName = name;\n                }\n                start = end = -1;\n            } else {\n                throw new SyntaxError(`Unexpected character at index ${i}`);\n            }\n        } else if (paramName === undefined) {\n            if (end === -1 && tokenChars[code] === 1) {\n                if (start === -1) start = i;\n            } else if (code === 0x20 || code === 0x09) {\n                if (end === -1 && start !== -1) end = i;\n            } else if (code === 0x3b || code === 0x2c) {\n                if (start === -1) {\n                    throw new SyntaxError(`Unexpected character at index ${i}`);\n                }\n                if (end === -1) end = i;\n                push(params, header.slice(start, end), true);\n                if (code === 0x2c) {\n                    push(offers, extensionName, params);\n                    params = Object.create(null);\n                    extensionName = undefined;\n                }\n                start = end = -1;\n            } else if (code === 0x3d /* '=' */  && start !== -1 && end === -1) {\n                paramName = header.slice(start, i);\n                start = end = -1;\n            } else {\n                throw new SyntaxError(`Unexpected character at index ${i}`);\n            }\n        } else {\n            //\n            // The value of a quoted-string after unescaping must conform to the\n            // token ABNF, so only token characters are valid.\n            // Ref: https://tools.ietf.org/html/rfc6455#section-9.1\n            //\n            if (isEscaping) {\n                if (tokenChars[code] !== 1) {\n                    throw new SyntaxError(`Unexpected character at index ${i}`);\n                }\n                if (start === -1) start = i;\n                else if (!mustUnescape) mustUnescape = true;\n                isEscaping = false;\n            } else if (inQuotes) {\n                if (tokenChars[code] === 1) {\n                    if (start === -1) start = i;\n                } else if (code === 0x22 /* '\"' */  && start !== -1) {\n                    inQuotes = false;\n                    end = i;\n                } else if (code === 0x5c /* '\\' */ ) {\n                    isEscaping = true;\n                } else {\n                    throw new SyntaxError(`Unexpected character at index ${i}`);\n                }\n            } else if (code === 0x22 && header.charCodeAt(i - 1) === 0x3d) {\n                inQuotes = true;\n            } else if (end === -1 && tokenChars[code] === 1) {\n                if (start === -1) start = i;\n            } else if (start !== -1 && (code === 0x20 || code === 0x09)) {\n                if (end === -1) end = i;\n            } else if (code === 0x3b || code === 0x2c) {\n                if (start === -1) {\n                    throw new SyntaxError(`Unexpected character at index ${i}`);\n                }\n                if (end === -1) end = i;\n                let value = header.slice(start, end);\n                if (mustUnescape) {\n                    value = value.replace(/\\\\/g, \"\");\n                    mustUnescape = false;\n                }\n                push(params, paramName, value);\n                if (code === 0x2c) {\n                    push(offers, extensionName, params);\n                    params = Object.create(null);\n                    extensionName = undefined;\n                }\n                paramName = undefined;\n                start = end = -1;\n            } else {\n                throw new SyntaxError(`Unexpected character at index ${i}`);\n            }\n        }\n    }\n    if (start === -1 || inQuotes || code === 0x20 || code === 0x09) {\n        throw new SyntaxError(\"Unexpected end of input\");\n    }\n    if (end === -1) end = i;\n    const token = header.slice(start, end);\n    if (extensionName === undefined) {\n        push(offers, token, params);\n    } else {\n        if (paramName === undefined) {\n            push(params, token, true);\n        } else if (mustUnescape) {\n            push(params, paramName, token.replace(/\\\\/g, \"\"));\n        } else {\n            push(params, paramName, token);\n        }\n        push(offers, extensionName, params);\n    }\n    return offers;\n}\n/**\n * Builds the `Sec-WebSocket-Extensions` header field value.\n *\n * @param {Object} extensions The map of extensions and parameters to format\n * @return {String} A string representing the given object\n * @public\n */ function format(extensions) {\n    return Object.keys(extensions).map((extension)=>{\n        let configurations = extensions[extension];\n        if (!Array.isArray(configurations)) configurations = [\n            configurations\n        ];\n        return configurations.map((params)=>{\n            return [\n                extension\n            ].concat(Object.keys(params).map((k)=>{\n                let values = params[k];\n                if (!Array.isArray(values)) values = [\n                    values\n                ];\n                return values.map((v)=>v === true ? k : `${k}=${v}`).join(\"; \");\n            })).join(\"; \");\n        }).join(\", \");\n    }).join(\", \");\n}\nmodule.exports = {\n    format,\n    parse\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ws/lib/extension.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ws/lib/limiter.js":
/*!****************************************!*\
  !*** ./node_modules/ws/lib/limiter.js ***!
  \****************************************/
/***/ ((module) => {

eval("\nconst kDone = Symbol(\"kDone\");\nconst kRun = Symbol(\"kRun\");\n/**\n * A very simple job queue with adjustable concurrency. Adapted from\n * https://github.com/STRML/async-limiter\n */ class Limiter {\n    /**\n   * Creates a new `Limiter`.\n   *\n   * @param {Number} [concurrency=Infinity] The maximum number of jobs allowed\n   *     to run concurrently\n   */ constructor(concurrency){\n        this[kDone] = ()=>{\n            this.pending--;\n            this[kRun]();\n        };\n        this.concurrency = concurrency || Infinity;\n        this.jobs = [];\n        this.pending = 0;\n    }\n    /**\n   * Adds a job to the queue.\n   *\n   * @param {Function} job The job to run\n   * @public\n   */ add(job) {\n        this.jobs.push(job);\n        this[kRun]();\n    }\n    /**\n   * Removes a job from the queue and runs it if possible.\n   *\n   * @private\n   */ [kRun]() {\n        if (this.pending === this.concurrency) return;\n        if (this.jobs.length) {\n            const job = this.jobs.shift();\n            this.pending++;\n            job(this[kDone]);\n        }\n    }\n}\nmodule.exports = Limiter;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ws/lib/limiter.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ws/lib/permessage-deflate.js":
/*!***************************************************!*\
  !*** ./node_modules/ws/lib/permessage-deflate.js ***!
  \***************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst zlib = __webpack_require__(/*! zlib */ \"zlib\");\nconst bufferUtil = __webpack_require__(/*! ./buffer-util */ \"(ssr)/./node_modules/ws/lib/buffer-util.js\");\nconst Limiter = __webpack_require__(/*! ./limiter */ \"(ssr)/./node_modules/ws/lib/limiter.js\");\nconst { kStatusCode } = __webpack_require__(/*! ./constants */ \"(ssr)/./node_modules/ws/lib/constants.js\");\nconst FastBuffer = Buffer[Symbol.species];\nconst TRAILER = Buffer.from([\n    0x00,\n    0x00,\n    0xff,\n    0xff\n]);\nconst kPerMessageDeflate = Symbol(\"permessage-deflate\");\nconst kTotalLength = Symbol(\"total-length\");\nconst kCallback = Symbol(\"callback\");\nconst kBuffers = Symbol(\"buffers\");\nconst kError = Symbol(\"error\");\n//\n// We limit zlib concurrency, which prevents severe memory fragmentation\n// as documented in https://github.com/nodejs/node/issues/8871#issuecomment-250915913\n// and https://github.com/websockets/ws/issues/1202\n//\n// Intentionally global; it's the global thread pool that's an issue.\n//\nlet zlibLimiter;\n/**\n * permessage-deflate implementation.\n */ class PerMessageDeflate {\n    /**\n   * Creates a PerMessageDeflate instance.\n   *\n   * @param {Object} [options] Configuration options\n   * @param {(Boolean|Number)} [options.clientMaxWindowBits] Advertise support\n   *     for, or request, a custom client window size\n   * @param {Boolean} [options.clientNoContextTakeover=false] Advertise/\n   *     acknowledge disabling of client context takeover\n   * @param {Number} [options.concurrencyLimit=10] The number of concurrent\n   *     calls to zlib\n   * @param {(Boolean|Number)} [options.serverMaxWindowBits] Request/confirm the\n   *     use of a custom server window size\n   * @param {Boolean} [options.serverNoContextTakeover=false] Request/accept\n   *     disabling of server context takeover\n   * @param {Number} [options.threshold=1024] Size (in bytes) below which\n   *     messages should not be compressed if context takeover is disabled\n   * @param {Object} [options.zlibDeflateOptions] Options to pass to zlib on\n   *     deflate\n   * @param {Object} [options.zlibInflateOptions] Options to pass to zlib on\n   *     inflate\n   * @param {Boolean} [isServer=false] Create the instance in either server or\n   *     client mode\n   * @param {Number} [maxPayload=0] The maximum allowed message length\n   */ constructor(options, isServer, maxPayload){\n        this._maxPayload = maxPayload | 0;\n        this._options = options || {};\n        this._threshold = this._options.threshold !== undefined ? this._options.threshold : 1024;\n        this._isServer = !!isServer;\n        this._deflate = null;\n        this._inflate = null;\n        this.params = null;\n        if (!zlibLimiter) {\n            const concurrency = this._options.concurrencyLimit !== undefined ? this._options.concurrencyLimit : 10;\n            zlibLimiter = new Limiter(concurrency);\n        }\n    }\n    /**\n   * @type {String}\n   */ static get extensionName() {\n        return \"permessage-deflate\";\n    }\n    /**\n   * Create an extension negotiation offer.\n   *\n   * @return {Object} Extension parameters\n   * @public\n   */ offer() {\n        const params = {};\n        if (this._options.serverNoContextTakeover) {\n            params.server_no_context_takeover = true;\n        }\n        if (this._options.clientNoContextTakeover) {\n            params.client_no_context_takeover = true;\n        }\n        if (this._options.serverMaxWindowBits) {\n            params.server_max_window_bits = this._options.serverMaxWindowBits;\n        }\n        if (this._options.clientMaxWindowBits) {\n            params.client_max_window_bits = this._options.clientMaxWindowBits;\n        } else if (this._options.clientMaxWindowBits == null) {\n            params.client_max_window_bits = true;\n        }\n        return params;\n    }\n    /**\n   * Accept an extension negotiation offer/response.\n   *\n   * @param {Array} configurations The extension negotiation offers/reponse\n   * @return {Object} Accepted configuration\n   * @public\n   */ accept(configurations) {\n        configurations = this.normalizeParams(configurations);\n        this.params = this._isServer ? this.acceptAsServer(configurations) : this.acceptAsClient(configurations);\n        return this.params;\n    }\n    /**\n   * Releases all resources used by the extension.\n   *\n   * @public\n   */ cleanup() {\n        if (this._inflate) {\n            this._inflate.close();\n            this._inflate = null;\n        }\n        if (this._deflate) {\n            const callback = this._deflate[kCallback];\n            this._deflate.close();\n            this._deflate = null;\n            if (callback) {\n                callback(new Error(\"The deflate stream was closed while data was being processed\"));\n            }\n        }\n    }\n    /**\n   *  Accept an extension negotiation offer.\n   *\n   * @param {Array} offers The extension negotiation offers\n   * @return {Object} Accepted configuration\n   * @private\n   */ acceptAsServer(offers) {\n        const opts = this._options;\n        const accepted = offers.find((params)=>{\n            if (opts.serverNoContextTakeover === false && params.server_no_context_takeover || params.server_max_window_bits && (opts.serverMaxWindowBits === false || typeof opts.serverMaxWindowBits === \"number\" && opts.serverMaxWindowBits > params.server_max_window_bits) || typeof opts.clientMaxWindowBits === \"number\" && !params.client_max_window_bits) {\n                return false;\n            }\n            return true;\n        });\n        if (!accepted) {\n            throw new Error(\"None of the extension offers can be accepted\");\n        }\n        if (opts.serverNoContextTakeover) {\n            accepted.server_no_context_takeover = true;\n        }\n        if (opts.clientNoContextTakeover) {\n            accepted.client_no_context_takeover = true;\n        }\n        if (typeof opts.serverMaxWindowBits === \"number\") {\n            accepted.server_max_window_bits = opts.serverMaxWindowBits;\n        }\n        if (typeof opts.clientMaxWindowBits === \"number\") {\n            accepted.client_max_window_bits = opts.clientMaxWindowBits;\n        } else if (accepted.client_max_window_bits === true || opts.clientMaxWindowBits === false) {\n            delete accepted.client_max_window_bits;\n        }\n        return accepted;\n    }\n    /**\n   * Accept the extension negotiation response.\n   *\n   * @param {Array} response The extension negotiation response\n   * @return {Object} Accepted configuration\n   * @private\n   */ acceptAsClient(response) {\n        const params = response[0];\n        if (this._options.clientNoContextTakeover === false && params.client_no_context_takeover) {\n            throw new Error('Unexpected parameter \"client_no_context_takeover\"');\n        }\n        if (!params.client_max_window_bits) {\n            if (typeof this._options.clientMaxWindowBits === \"number\") {\n                params.client_max_window_bits = this._options.clientMaxWindowBits;\n            }\n        } else if (this._options.clientMaxWindowBits === false || typeof this._options.clientMaxWindowBits === \"number\" && params.client_max_window_bits > this._options.clientMaxWindowBits) {\n            throw new Error('Unexpected or invalid parameter \"client_max_window_bits\"');\n        }\n        return params;\n    }\n    /**\n   * Normalize parameters.\n   *\n   * @param {Array} configurations The extension negotiation offers/reponse\n   * @return {Array} The offers/response with normalized parameters\n   * @private\n   */ normalizeParams(configurations) {\n        configurations.forEach((params)=>{\n            Object.keys(params).forEach((key)=>{\n                let value = params[key];\n                if (value.length > 1) {\n                    throw new Error(`Parameter \"${key}\" must have only a single value`);\n                }\n                value = value[0];\n                if (key === \"client_max_window_bits\") {\n                    if (value !== true) {\n                        const num = +value;\n                        if (!Number.isInteger(num) || num < 8 || num > 15) {\n                            throw new TypeError(`Invalid value for parameter \"${key}\": ${value}`);\n                        }\n                        value = num;\n                    } else if (!this._isServer) {\n                        throw new TypeError(`Invalid value for parameter \"${key}\": ${value}`);\n                    }\n                } else if (key === \"server_max_window_bits\") {\n                    const num = +value;\n                    if (!Number.isInteger(num) || num < 8 || num > 15) {\n                        throw new TypeError(`Invalid value for parameter \"${key}\": ${value}`);\n                    }\n                    value = num;\n                } else if (key === \"client_no_context_takeover\" || key === \"server_no_context_takeover\") {\n                    if (value !== true) {\n                        throw new TypeError(`Invalid value for parameter \"${key}\": ${value}`);\n                    }\n                } else {\n                    throw new Error(`Unknown parameter \"${key}\"`);\n                }\n                params[key] = value;\n            });\n        });\n        return configurations;\n    }\n    /**\n   * Decompress data. Concurrency limited.\n   *\n   * @param {Buffer} data Compressed data\n   * @param {Boolean} fin Specifies whether or not this is the last fragment\n   * @param {Function} callback Callback\n   * @public\n   */ decompress(data, fin, callback) {\n        zlibLimiter.add((done)=>{\n            this._decompress(data, fin, (err, result)=>{\n                done();\n                callback(err, result);\n            });\n        });\n    }\n    /**\n   * Compress data. Concurrency limited.\n   *\n   * @param {(Buffer|String)} data Data to compress\n   * @param {Boolean} fin Specifies whether or not this is the last fragment\n   * @param {Function} callback Callback\n   * @public\n   */ compress(data, fin, callback) {\n        zlibLimiter.add((done)=>{\n            this._compress(data, fin, (err, result)=>{\n                done();\n                callback(err, result);\n            });\n        });\n    }\n    /**\n   * Decompress data.\n   *\n   * @param {Buffer} data Compressed data\n   * @param {Boolean} fin Specifies whether or not this is the last fragment\n   * @param {Function} callback Callback\n   * @private\n   */ _decompress(data, fin, callback) {\n        const endpoint = this._isServer ? \"client\" : \"server\";\n        if (!this._inflate) {\n            const key = `${endpoint}_max_window_bits`;\n            const windowBits = typeof this.params[key] !== \"number\" ? zlib.Z_DEFAULT_WINDOWBITS : this.params[key];\n            this._inflate = zlib.createInflateRaw({\n                ...this._options.zlibInflateOptions,\n                windowBits\n            });\n            this._inflate[kPerMessageDeflate] = this;\n            this._inflate[kTotalLength] = 0;\n            this._inflate[kBuffers] = [];\n            this._inflate.on(\"error\", inflateOnError);\n            this._inflate.on(\"data\", inflateOnData);\n        }\n        this._inflate[kCallback] = callback;\n        this._inflate.write(data);\n        if (fin) this._inflate.write(TRAILER);\n        this._inflate.flush(()=>{\n            const err = this._inflate[kError];\n            if (err) {\n                this._inflate.close();\n                this._inflate = null;\n                callback(err);\n                return;\n            }\n            const data = bufferUtil.concat(this._inflate[kBuffers], this._inflate[kTotalLength]);\n            if (this._inflate._readableState.endEmitted) {\n                this._inflate.close();\n                this._inflate = null;\n            } else {\n                this._inflate[kTotalLength] = 0;\n                this._inflate[kBuffers] = [];\n                if (fin && this.params[`${endpoint}_no_context_takeover`]) {\n                    this._inflate.reset();\n                }\n            }\n            callback(null, data);\n        });\n    }\n    /**\n   * Compress data.\n   *\n   * @param {(Buffer|String)} data Data to compress\n   * @param {Boolean} fin Specifies whether or not this is the last fragment\n   * @param {Function} callback Callback\n   * @private\n   */ _compress(data, fin, callback) {\n        const endpoint = this._isServer ? \"server\" : \"client\";\n        if (!this._deflate) {\n            const key = `${endpoint}_max_window_bits`;\n            const windowBits = typeof this.params[key] !== \"number\" ? zlib.Z_DEFAULT_WINDOWBITS : this.params[key];\n            this._deflate = zlib.createDeflateRaw({\n                ...this._options.zlibDeflateOptions,\n                windowBits\n            });\n            this._deflate[kTotalLength] = 0;\n            this._deflate[kBuffers] = [];\n            this._deflate.on(\"data\", deflateOnData);\n        }\n        this._deflate[kCallback] = callback;\n        this._deflate.write(data);\n        this._deflate.flush(zlib.Z_SYNC_FLUSH, ()=>{\n            if (!this._deflate) {\n                //\n                // The deflate stream was closed while data was being processed.\n                //\n                return;\n            }\n            let data = bufferUtil.concat(this._deflate[kBuffers], this._deflate[kTotalLength]);\n            if (fin) {\n                data = new FastBuffer(data.buffer, data.byteOffset, data.length - 4);\n            }\n            //\n            // Ensure that the callback will not be called again in\n            // `PerMessageDeflate#cleanup()`.\n            //\n            this._deflate[kCallback] = null;\n            this._deflate[kTotalLength] = 0;\n            this._deflate[kBuffers] = [];\n            if (fin && this.params[`${endpoint}_no_context_takeover`]) {\n                this._deflate.reset();\n            }\n            callback(null, data);\n        });\n    }\n}\nmodule.exports = PerMessageDeflate;\n/**\n * The listener of the `zlib.DeflateRaw` stream `'data'` event.\n *\n * @param {Buffer} chunk A chunk of data\n * @private\n */ function deflateOnData(chunk) {\n    this[kBuffers].push(chunk);\n    this[kTotalLength] += chunk.length;\n}\n/**\n * The listener of the `zlib.InflateRaw` stream `'data'` event.\n *\n * @param {Buffer} chunk A chunk of data\n * @private\n */ function inflateOnData(chunk) {\n    this[kTotalLength] += chunk.length;\n    if (this[kPerMessageDeflate]._maxPayload < 1 || this[kTotalLength] <= this[kPerMessageDeflate]._maxPayload) {\n        this[kBuffers].push(chunk);\n        return;\n    }\n    this[kError] = new RangeError(\"Max payload size exceeded\");\n    this[kError].code = \"WS_ERR_UNSUPPORTED_MESSAGE_LENGTH\";\n    this[kError][kStatusCode] = 1009;\n    this.removeListener(\"data\", inflateOnData);\n    //\n    // The choice to employ `zlib.reset()` over `zlib.close()` is dictated by the\n    // fact that in Node.js versions prior to 13.10.0, the callback for\n    // `zlib.flush()` is not called if `zlib.close()` is used. Utilizing\n    // `zlib.reset()` ensures that either the callback is invoked or an error is\n    // emitted.\n    //\n    this.reset();\n}\n/**\n * The listener of the `zlib.InflateRaw` stream `'error'` event.\n *\n * @param {Error} err The emitted error\n * @private\n */ function inflateOnError(err) {\n    //\n    // There is no need to call `Zlib#close()` as the handle is automatically\n    // closed when an error is emitted.\n    //\n    this[kPerMessageDeflate]._inflate = null;\n    if (this[kError]) {\n        this[kCallback](this[kError]);\n        return;\n    }\n    err[kStatusCode] = 1007;\n    this[kCallback](err);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ws/lib/permessage-deflate.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ws/lib/receiver.js":
/*!*****************************************!*\
  !*** ./node_modules/ws/lib/receiver.js ***!
  \*****************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst { Writable } = __webpack_require__(/*! stream */ \"stream\");\nconst PerMessageDeflate = __webpack_require__(/*! ./permessage-deflate */ \"(ssr)/./node_modules/ws/lib/permessage-deflate.js\");\nconst { BINARY_TYPES, EMPTY_BUFFER, kStatusCode, kWebSocket } = __webpack_require__(/*! ./constants */ \"(ssr)/./node_modules/ws/lib/constants.js\");\nconst { concat, toArrayBuffer, unmask } = __webpack_require__(/*! ./buffer-util */ \"(ssr)/./node_modules/ws/lib/buffer-util.js\");\nconst { isValidStatusCode, isValidUTF8 } = __webpack_require__(/*! ./validation */ \"(ssr)/./node_modules/ws/lib/validation.js\");\nconst FastBuffer = Buffer[Symbol.species];\nconst GET_INFO = 0;\nconst GET_PAYLOAD_LENGTH_16 = 1;\nconst GET_PAYLOAD_LENGTH_64 = 2;\nconst GET_MASK = 3;\nconst GET_DATA = 4;\nconst INFLATING = 5;\nconst DEFER_EVENT = 6;\n/**\n * HyBi Receiver implementation.\n *\n * @extends Writable\n */ class Receiver extends Writable {\n    /**\n   * Creates a Receiver instance.\n   *\n   * @param {Object} [options] Options object\n   * @param {Boolean} [options.allowSynchronousEvents=true] Specifies whether\n   *     any of the `'message'`, `'ping'`, and `'pong'` events can be emitted\n   *     multiple times in the same tick\n   * @param {String} [options.binaryType=nodebuffer] The type for binary data\n   * @param {Object} [options.extensions] An object containing the negotiated\n   *     extensions\n   * @param {Boolean} [options.isServer=false] Specifies whether to operate in\n   *     client or server mode\n   * @param {Number} [options.maxPayload=0] The maximum allowed message length\n   * @param {Boolean} [options.skipUTF8Validation=false] Specifies whether or\n   *     not to skip UTF-8 validation for text and close messages\n   */ constructor(options = {}){\n        super();\n        this._allowSynchronousEvents = options.allowSynchronousEvents !== undefined ? options.allowSynchronousEvents : true;\n        this._binaryType = options.binaryType || BINARY_TYPES[0];\n        this._extensions = options.extensions || {};\n        this._isServer = !!options.isServer;\n        this._maxPayload = options.maxPayload | 0;\n        this._skipUTF8Validation = !!options.skipUTF8Validation;\n        this[kWebSocket] = undefined;\n        this._bufferedBytes = 0;\n        this._buffers = [];\n        this._compressed = false;\n        this._payloadLength = 0;\n        this._mask = undefined;\n        this._fragmented = 0;\n        this._masked = false;\n        this._fin = false;\n        this._opcode = 0;\n        this._totalPayloadLength = 0;\n        this._messageLength = 0;\n        this._fragments = [];\n        this._errored = false;\n        this._loop = false;\n        this._state = GET_INFO;\n    }\n    /**\n   * Implements `Writable.prototype._write()`.\n   *\n   * @param {Buffer} chunk The chunk of data to write\n   * @param {String} encoding The character encoding of `chunk`\n   * @param {Function} cb Callback\n   * @private\n   */ _write(chunk, encoding, cb) {\n        if (this._opcode === 0x08 && this._state == GET_INFO) return cb();\n        this._bufferedBytes += chunk.length;\n        this._buffers.push(chunk);\n        this.startLoop(cb);\n    }\n    /**\n   * Consumes `n` bytes from the buffered data.\n   *\n   * @param {Number} n The number of bytes to consume\n   * @return {Buffer} The consumed bytes\n   * @private\n   */ consume(n) {\n        this._bufferedBytes -= n;\n        if (n === this._buffers[0].length) return this._buffers.shift();\n        if (n < this._buffers[0].length) {\n            const buf = this._buffers[0];\n            this._buffers[0] = new FastBuffer(buf.buffer, buf.byteOffset + n, buf.length - n);\n            return new FastBuffer(buf.buffer, buf.byteOffset, n);\n        }\n        const dst = Buffer.allocUnsafe(n);\n        do {\n            const buf = this._buffers[0];\n            const offset = dst.length - n;\n            if (n >= buf.length) {\n                dst.set(this._buffers.shift(), offset);\n            } else {\n                dst.set(new Uint8Array(buf.buffer, buf.byteOffset, n), offset);\n                this._buffers[0] = new FastBuffer(buf.buffer, buf.byteOffset + n, buf.length - n);\n            }\n            n -= buf.length;\n        }while (n > 0);\n        return dst;\n    }\n    /**\n   * Starts the parsing loop.\n   *\n   * @param {Function} cb Callback\n   * @private\n   */ startLoop(cb) {\n        this._loop = true;\n        do {\n            switch(this._state){\n                case GET_INFO:\n                    this.getInfo(cb);\n                    break;\n                case GET_PAYLOAD_LENGTH_16:\n                    this.getPayloadLength16(cb);\n                    break;\n                case GET_PAYLOAD_LENGTH_64:\n                    this.getPayloadLength64(cb);\n                    break;\n                case GET_MASK:\n                    this.getMask();\n                    break;\n                case GET_DATA:\n                    this.getData(cb);\n                    break;\n                case INFLATING:\n                case DEFER_EVENT:\n                    this._loop = false;\n                    return;\n            }\n        }while (this._loop);\n        if (!this._errored) cb();\n    }\n    /**\n   * Reads the first two bytes of a frame.\n   *\n   * @param {Function} cb Callback\n   * @private\n   */ getInfo(cb) {\n        if (this._bufferedBytes < 2) {\n            this._loop = false;\n            return;\n        }\n        const buf = this.consume(2);\n        if ((buf[0] & 0x30) !== 0x00) {\n            const error = this.createError(RangeError, \"RSV2 and RSV3 must be clear\", true, 1002, \"WS_ERR_UNEXPECTED_RSV_2_3\");\n            cb(error);\n            return;\n        }\n        const compressed = (buf[0] & 0x40) === 0x40;\n        if (compressed && !this._extensions[PerMessageDeflate.extensionName]) {\n            const error = this.createError(RangeError, \"RSV1 must be clear\", true, 1002, \"WS_ERR_UNEXPECTED_RSV_1\");\n            cb(error);\n            return;\n        }\n        this._fin = (buf[0] & 0x80) === 0x80;\n        this._opcode = buf[0] & 0x0f;\n        this._payloadLength = buf[1] & 0x7f;\n        if (this._opcode === 0x00) {\n            if (compressed) {\n                const error = this.createError(RangeError, \"RSV1 must be clear\", true, 1002, \"WS_ERR_UNEXPECTED_RSV_1\");\n                cb(error);\n                return;\n            }\n            if (!this._fragmented) {\n                const error = this.createError(RangeError, \"invalid opcode 0\", true, 1002, \"WS_ERR_INVALID_OPCODE\");\n                cb(error);\n                return;\n            }\n            this._opcode = this._fragmented;\n        } else if (this._opcode === 0x01 || this._opcode === 0x02) {\n            if (this._fragmented) {\n                const error = this.createError(RangeError, `invalid opcode ${this._opcode}`, true, 1002, \"WS_ERR_INVALID_OPCODE\");\n                cb(error);\n                return;\n            }\n            this._compressed = compressed;\n        } else if (this._opcode > 0x07 && this._opcode < 0x0b) {\n            if (!this._fin) {\n                const error = this.createError(RangeError, \"FIN must be set\", true, 1002, \"WS_ERR_EXPECTED_FIN\");\n                cb(error);\n                return;\n            }\n            if (compressed) {\n                const error = this.createError(RangeError, \"RSV1 must be clear\", true, 1002, \"WS_ERR_UNEXPECTED_RSV_1\");\n                cb(error);\n                return;\n            }\n            if (this._payloadLength > 0x7d || this._opcode === 0x08 && this._payloadLength === 1) {\n                const error = this.createError(RangeError, `invalid payload length ${this._payloadLength}`, true, 1002, \"WS_ERR_INVALID_CONTROL_PAYLOAD_LENGTH\");\n                cb(error);\n                return;\n            }\n        } else {\n            const error = this.createError(RangeError, `invalid opcode ${this._opcode}`, true, 1002, \"WS_ERR_INVALID_OPCODE\");\n            cb(error);\n            return;\n        }\n        if (!this._fin && !this._fragmented) this._fragmented = this._opcode;\n        this._masked = (buf[1] & 0x80) === 0x80;\n        if (this._isServer) {\n            if (!this._masked) {\n                const error = this.createError(RangeError, \"MASK must be set\", true, 1002, \"WS_ERR_EXPECTED_MASK\");\n                cb(error);\n                return;\n            }\n        } else if (this._masked) {\n            const error = this.createError(RangeError, \"MASK must be clear\", true, 1002, \"WS_ERR_UNEXPECTED_MASK\");\n            cb(error);\n            return;\n        }\n        if (this._payloadLength === 126) this._state = GET_PAYLOAD_LENGTH_16;\n        else if (this._payloadLength === 127) this._state = GET_PAYLOAD_LENGTH_64;\n        else this.haveLength(cb);\n    }\n    /**\n   * Gets extended payload length (7+16).\n   *\n   * @param {Function} cb Callback\n   * @private\n   */ getPayloadLength16(cb) {\n        if (this._bufferedBytes < 2) {\n            this._loop = false;\n            return;\n        }\n        this._payloadLength = this.consume(2).readUInt16BE(0);\n        this.haveLength(cb);\n    }\n    /**\n   * Gets extended payload length (7+64).\n   *\n   * @param {Function} cb Callback\n   * @private\n   */ getPayloadLength64(cb) {\n        if (this._bufferedBytes < 8) {\n            this._loop = false;\n            return;\n        }\n        const buf = this.consume(8);\n        const num = buf.readUInt32BE(0);\n        //\n        // The maximum safe integer in JavaScript is 2^53 - 1. An error is returned\n        // if payload length is greater than this number.\n        //\n        if (num > Math.pow(2, 53 - 32) - 1) {\n            const error = this.createError(RangeError, \"Unsupported WebSocket frame: payload length > 2^53 - 1\", false, 1009, \"WS_ERR_UNSUPPORTED_DATA_PAYLOAD_LENGTH\");\n            cb(error);\n            return;\n        }\n        this._payloadLength = num * Math.pow(2, 32) + buf.readUInt32BE(4);\n        this.haveLength(cb);\n    }\n    /**\n   * Payload length has been read.\n   *\n   * @param {Function} cb Callback\n   * @private\n   */ haveLength(cb) {\n        if (this._payloadLength && this._opcode < 0x08) {\n            this._totalPayloadLength += this._payloadLength;\n            if (this._totalPayloadLength > this._maxPayload && this._maxPayload > 0) {\n                const error = this.createError(RangeError, \"Max payload size exceeded\", false, 1009, \"WS_ERR_UNSUPPORTED_MESSAGE_LENGTH\");\n                cb(error);\n                return;\n            }\n        }\n        if (this._masked) this._state = GET_MASK;\n        else this._state = GET_DATA;\n    }\n    /**\n   * Reads mask bytes.\n   *\n   * @private\n   */ getMask() {\n        if (this._bufferedBytes < 4) {\n            this._loop = false;\n            return;\n        }\n        this._mask = this.consume(4);\n        this._state = GET_DATA;\n    }\n    /**\n   * Reads data bytes.\n   *\n   * @param {Function} cb Callback\n   * @private\n   */ getData(cb) {\n        let data = EMPTY_BUFFER;\n        if (this._payloadLength) {\n            if (this._bufferedBytes < this._payloadLength) {\n                this._loop = false;\n                return;\n            }\n            data = this.consume(this._payloadLength);\n            if (this._masked && (this._mask[0] | this._mask[1] | this._mask[2] | this._mask[3]) !== 0) {\n                unmask(data, this._mask);\n            }\n        }\n        if (this._opcode > 0x07) {\n            this.controlMessage(data, cb);\n            return;\n        }\n        if (this._compressed) {\n            this._state = INFLATING;\n            this.decompress(data, cb);\n            return;\n        }\n        if (data.length) {\n            //\n            // This message is not compressed so its length is the sum of the payload\n            // length of all fragments.\n            //\n            this._messageLength = this._totalPayloadLength;\n            this._fragments.push(data);\n        }\n        this.dataMessage(cb);\n    }\n    /**\n   * Decompresses data.\n   *\n   * @param {Buffer} data Compressed data\n   * @param {Function} cb Callback\n   * @private\n   */ decompress(data, cb) {\n        const perMessageDeflate = this._extensions[PerMessageDeflate.extensionName];\n        perMessageDeflate.decompress(data, this._fin, (err, buf)=>{\n            if (err) return cb(err);\n            if (buf.length) {\n                this._messageLength += buf.length;\n                if (this._messageLength > this._maxPayload && this._maxPayload > 0) {\n                    const error = this.createError(RangeError, \"Max payload size exceeded\", false, 1009, \"WS_ERR_UNSUPPORTED_MESSAGE_LENGTH\");\n                    cb(error);\n                    return;\n                }\n                this._fragments.push(buf);\n            }\n            this.dataMessage(cb);\n            if (this._state === GET_INFO) this.startLoop(cb);\n        });\n    }\n    /**\n   * Handles a data message.\n   *\n   * @param {Function} cb Callback\n   * @private\n   */ dataMessage(cb) {\n        if (!this._fin) {\n            this._state = GET_INFO;\n            return;\n        }\n        const messageLength = this._messageLength;\n        const fragments = this._fragments;\n        this._totalPayloadLength = 0;\n        this._messageLength = 0;\n        this._fragmented = 0;\n        this._fragments = [];\n        if (this._opcode === 2) {\n            let data;\n            if (this._binaryType === \"nodebuffer\") {\n                data = concat(fragments, messageLength);\n            } else if (this._binaryType === \"arraybuffer\") {\n                data = toArrayBuffer(concat(fragments, messageLength));\n            } else if (this._binaryType === \"blob\") {\n                data = new Blob(fragments);\n            } else {\n                data = fragments;\n            }\n            if (this._allowSynchronousEvents) {\n                this.emit(\"message\", data, true);\n                this._state = GET_INFO;\n            } else {\n                this._state = DEFER_EVENT;\n                setImmediate(()=>{\n                    this.emit(\"message\", data, true);\n                    this._state = GET_INFO;\n                    this.startLoop(cb);\n                });\n            }\n        } else {\n            const buf = concat(fragments, messageLength);\n            if (!this._skipUTF8Validation && !isValidUTF8(buf)) {\n                const error = this.createError(Error, \"invalid UTF-8 sequence\", true, 1007, \"WS_ERR_INVALID_UTF8\");\n                cb(error);\n                return;\n            }\n            if (this._state === INFLATING || this._allowSynchronousEvents) {\n                this.emit(\"message\", buf, false);\n                this._state = GET_INFO;\n            } else {\n                this._state = DEFER_EVENT;\n                setImmediate(()=>{\n                    this.emit(\"message\", buf, false);\n                    this._state = GET_INFO;\n                    this.startLoop(cb);\n                });\n            }\n        }\n    }\n    /**\n   * Handles a control message.\n   *\n   * @param {Buffer} data Data to handle\n   * @return {(Error|RangeError|undefined)} A possible error\n   * @private\n   */ controlMessage(data, cb) {\n        if (this._opcode === 0x08) {\n            if (data.length === 0) {\n                this._loop = false;\n                this.emit(\"conclude\", 1005, EMPTY_BUFFER);\n                this.end();\n            } else {\n                const code = data.readUInt16BE(0);\n                if (!isValidStatusCode(code)) {\n                    const error = this.createError(RangeError, `invalid status code ${code}`, true, 1002, \"WS_ERR_INVALID_CLOSE_CODE\");\n                    cb(error);\n                    return;\n                }\n                const buf = new FastBuffer(data.buffer, data.byteOffset + 2, data.length - 2);\n                if (!this._skipUTF8Validation && !isValidUTF8(buf)) {\n                    const error = this.createError(Error, \"invalid UTF-8 sequence\", true, 1007, \"WS_ERR_INVALID_UTF8\");\n                    cb(error);\n                    return;\n                }\n                this._loop = false;\n                this.emit(\"conclude\", code, buf);\n                this.end();\n            }\n            this._state = GET_INFO;\n            return;\n        }\n        if (this._allowSynchronousEvents) {\n            this.emit(this._opcode === 0x09 ? \"ping\" : \"pong\", data);\n            this._state = GET_INFO;\n        } else {\n            this._state = DEFER_EVENT;\n            setImmediate(()=>{\n                this.emit(this._opcode === 0x09 ? \"ping\" : \"pong\", data);\n                this._state = GET_INFO;\n                this.startLoop(cb);\n            });\n        }\n    }\n    /**\n   * Builds an error object.\n   *\n   * @param {function(new:Error|RangeError)} ErrorCtor The error constructor\n   * @param {String} message The error message\n   * @param {Boolean} prefix Specifies whether or not to add a default prefix to\n   *     `message`\n   * @param {Number} statusCode The status code\n   * @param {String} errorCode The exposed error code\n   * @return {(Error|RangeError)} The error\n   * @private\n   */ createError(ErrorCtor, message, prefix, statusCode, errorCode) {\n        this._loop = false;\n        this._errored = true;\n        const err = new ErrorCtor(prefix ? `Invalid WebSocket frame: ${message}` : message);\n        Error.captureStackTrace(err, this.createError);\n        err.code = errorCode;\n        err[kStatusCode] = statusCode;\n        return err;\n    }\n}\nmodule.exports = Receiver;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ws/lib/receiver.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ws/lib/sender.js":
/*!***************************************!*\
  !*** ./node_modules/ws/lib/sender.js ***!
  \***************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/* eslint no-unused-vars: [\"error\", { \"varsIgnorePattern\": \"^Duplex\" }] */ \nconst { Duplex } = __webpack_require__(/*! stream */ \"stream\");\nconst { randomFillSync } = __webpack_require__(/*! crypto */ \"crypto\");\nconst PerMessageDeflate = __webpack_require__(/*! ./permessage-deflate */ \"(ssr)/./node_modules/ws/lib/permessage-deflate.js\");\nconst { EMPTY_BUFFER, kWebSocket, NOOP } = __webpack_require__(/*! ./constants */ \"(ssr)/./node_modules/ws/lib/constants.js\");\nconst { isBlob, isValidStatusCode } = __webpack_require__(/*! ./validation */ \"(ssr)/./node_modules/ws/lib/validation.js\");\nconst { mask: applyMask, toBuffer } = __webpack_require__(/*! ./buffer-util */ \"(ssr)/./node_modules/ws/lib/buffer-util.js\");\nconst kByteLength = Symbol(\"kByteLength\");\nconst maskBuffer = Buffer.alloc(4);\nconst RANDOM_POOL_SIZE = 8 * 1024;\nlet randomPool;\nlet randomPoolPointer = RANDOM_POOL_SIZE;\nconst DEFAULT = 0;\nconst DEFLATING = 1;\nconst GET_BLOB_DATA = 2;\n/**\n * HyBi Sender implementation.\n */ class Sender {\n    /**\n   * Creates a Sender instance.\n   *\n   * @param {Duplex} socket The connection socket\n   * @param {Object} [extensions] An object containing the negotiated extensions\n   * @param {Function} [generateMask] The function used to generate the masking\n   *     key\n   */ constructor(socket, extensions, generateMask){\n        this._extensions = extensions || {};\n        if (generateMask) {\n            this._generateMask = generateMask;\n            this._maskBuffer = Buffer.alloc(4);\n        }\n        this._socket = socket;\n        this._firstFragment = true;\n        this._compress = false;\n        this._bufferedBytes = 0;\n        this._queue = [];\n        this._state = DEFAULT;\n        this.onerror = NOOP;\n        this[kWebSocket] = undefined;\n    }\n    /**\n   * Frames a piece of data according to the HyBi WebSocket protocol.\n   *\n   * @param {(Buffer|String)} data The data to frame\n   * @param {Object} options Options object\n   * @param {Boolean} [options.fin=false] Specifies whether or not to set the\n   *     FIN bit\n   * @param {Function} [options.generateMask] The function used to generate the\n   *     masking key\n   * @param {Boolean} [options.mask=false] Specifies whether or not to mask\n   *     `data`\n   * @param {Buffer} [options.maskBuffer] The buffer used to store the masking\n   *     key\n   * @param {Number} options.opcode The opcode\n   * @param {Boolean} [options.readOnly=false] Specifies whether `data` can be\n   *     modified\n   * @param {Boolean} [options.rsv1=false] Specifies whether or not to set the\n   *     RSV1 bit\n   * @return {(Buffer|String)[]} The framed data\n   * @public\n   */ static frame(data, options) {\n        let mask;\n        let merge = false;\n        let offset = 2;\n        let skipMasking = false;\n        if (options.mask) {\n            mask = options.maskBuffer || maskBuffer;\n            if (options.generateMask) {\n                options.generateMask(mask);\n            } else {\n                if (randomPoolPointer === RANDOM_POOL_SIZE) {\n                    /* istanbul ignore else  */ if (randomPool === undefined) {\n                        //\n                        // This is lazily initialized because server-sent frames must not\n                        // be masked so it may never be used.\n                        //\n                        randomPool = Buffer.alloc(RANDOM_POOL_SIZE);\n                    }\n                    randomFillSync(randomPool, 0, RANDOM_POOL_SIZE);\n                    randomPoolPointer = 0;\n                }\n                mask[0] = randomPool[randomPoolPointer++];\n                mask[1] = randomPool[randomPoolPointer++];\n                mask[2] = randomPool[randomPoolPointer++];\n                mask[3] = randomPool[randomPoolPointer++];\n            }\n            skipMasking = (mask[0] | mask[1] | mask[2] | mask[3]) === 0;\n            offset = 6;\n        }\n        let dataLength;\n        if (typeof data === \"string\") {\n            if ((!options.mask || skipMasking) && options[kByteLength] !== undefined) {\n                dataLength = options[kByteLength];\n            } else {\n                data = Buffer.from(data);\n                dataLength = data.length;\n            }\n        } else {\n            dataLength = data.length;\n            merge = options.mask && options.readOnly && !skipMasking;\n        }\n        let payloadLength = dataLength;\n        if (dataLength >= 65536) {\n            offset += 8;\n            payloadLength = 127;\n        } else if (dataLength > 125) {\n            offset += 2;\n            payloadLength = 126;\n        }\n        const target = Buffer.allocUnsafe(merge ? dataLength + offset : offset);\n        target[0] = options.fin ? options.opcode | 0x80 : options.opcode;\n        if (options.rsv1) target[0] |= 0x40;\n        target[1] = payloadLength;\n        if (payloadLength === 126) {\n            target.writeUInt16BE(dataLength, 2);\n        } else if (payloadLength === 127) {\n            target[2] = target[3] = 0;\n            target.writeUIntBE(dataLength, 4, 6);\n        }\n        if (!options.mask) return [\n            target,\n            data\n        ];\n        target[1] |= 0x80;\n        target[offset - 4] = mask[0];\n        target[offset - 3] = mask[1];\n        target[offset - 2] = mask[2];\n        target[offset - 1] = mask[3];\n        if (skipMasking) return [\n            target,\n            data\n        ];\n        if (merge) {\n            applyMask(data, mask, target, offset, dataLength);\n            return [\n                target\n            ];\n        }\n        applyMask(data, mask, data, 0, dataLength);\n        return [\n            target,\n            data\n        ];\n    }\n    /**\n   * Sends a close message to the other peer.\n   *\n   * @param {Number} [code] The status code component of the body\n   * @param {(String|Buffer)} [data] The message component of the body\n   * @param {Boolean} [mask=false] Specifies whether or not to mask the message\n   * @param {Function} [cb] Callback\n   * @public\n   */ close(code, data, mask, cb) {\n        let buf;\n        if (code === undefined) {\n            buf = EMPTY_BUFFER;\n        } else if (typeof code !== \"number\" || !isValidStatusCode(code)) {\n            throw new TypeError(\"First argument must be a valid error code number\");\n        } else if (data === undefined || !data.length) {\n            buf = Buffer.allocUnsafe(2);\n            buf.writeUInt16BE(code, 0);\n        } else {\n            const length = Buffer.byteLength(data);\n            if (length > 123) {\n                throw new RangeError(\"The message must not be greater than 123 bytes\");\n            }\n            buf = Buffer.allocUnsafe(2 + length);\n            buf.writeUInt16BE(code, 0);\n            if (typeof data === \"string\") {\n                buf.write(data, 2);\n            } else {\n                buf.set(data, 2);\n            }\n        }\n        const options = {\n            [kByteLength]: buf.length,\n            fin: true,\n            generateMask: this._generateMask,\n            mask,\n            maskBuffer: this._maskBuffer,\n            opcode: 0x08,\n            readOnly: false,\n            rsv1: false\n        };\n        if (this._state !== DEFAULT) {\n            this.enqueue([\n                this.dispatch,\n                buf,\n                false,\n                options,\n                cb\n            ]);\n        } else {\n            this.sendFrame(Sender.frame(buf, options), cb);\n        }\n    }\n    /**\n   * Sends a ping message to the other peer.\n   *\n   * @param {*} data The message to send\n   * @param {Boolean} [mask=false] Specifies whether or not to mask `data`\n   * @param {Function} [cb] Callback\n   * @public\n   */ ping(data, mask, cb) {\n        let byteLength;\n        let readOnly;\n        if (typeof data === \"string\") {\n            byteLength = Buffer.byteLength(data);\n            readOnly = false;\n        } else if (isBlob(data)) {\n            byteLength = data.size;\n            readOnly = false;\n        } else {\n            data = toBuffer(data);\n            byteLength = data.length;\n            readOnly = toBuffer.readOnly;\n        }\n        if (byteLength > 125) {\n            throw new RangeError(\"The data size must not be greater than 125 bytes\");\n        }\n        const options = {\n            [kByteLength]: byteLength,\n            fin: true,\n            generateMask: this._generateMask,\n            mask,\n            maskBuffer: this._maskBuffer,\n            opcode: 0x09,\n            readOnly,\n            rsv1: false\n        };\n        if (isBlob(data)) {\n            if (this._state !== DEFAULT) {\n                this.enqueue([\n                    this.getBlobData,\n                    data,\n                    false,\n                    options,\n                    cb\n                ]);\n            } else {\n                this.getBlobData(data, false, options, cb);\n            }\n        } else if (this._state !== DEFAULT) {\n            this.enqueue([\n                this.dispatch,\n                data,\n                false,\n                options,\n                cb\n            ]);\n        } else {\n            this.sendFrame(Sender.frame(data, options), cb);\n        }\n    }\n    /**\n   * Sends a pong message to the other peer.\n   *\n   * @param {*} data The message to send\n   * @param {Boolean} [mask=false] Specifies whether or not to mask `data`\n   * @param {Function} [cb] Callback\n   * @public\n   */ pong(data, mask, cb) {\n        let byteLength;\n        let readOnly;\n        if (typeof data === \"string\") {\n            byteLength = Buffer.byteLength(data);\n            readOnly = false;\n        } else if (isBlob(data)) {\n            byteLength = data.size;\n            readOnly = false;\n        } else {\n            data = toBuffer(data);\n            byteLength = data.length;\n            readOnly = toBuffer.readOnly;\n        }\n        if (byteLength > 125) {\n            throw new RangeError(\"The data size must not be greater than 125 bytes\");\n        }\n        const options = {\n            [kByteLength]: byteLength,\n            fin: true,\n            generateMask: this._generateMask,\n            mask,\n            maskBuffer: this._maskBuffer,\n            opcode: 0x0a,\n            readOnly,\n            rsv1: false\n        };\n        if (isBlob(data)) {\n            if (this._state !== DEFAULT) {\n                this.enqueue([\n                    this.getBlobData,\n                    data,\n                    false,\n                    options,\n                    cb\n                ]);\n            } else {\n                this.getBlobData(data, false, options, cb);\n            }\n        } else if (this._state !== DEFAULT) {\n            this.enqueue([\n                this.dispatch,\n                data,\n                false,\n                options,\n                cb\n            ]);\n        } else {\n            this.sendFrame(Sender.frame(data, options), cb);\n        }\n    }\n    /**\n   * Sends a data message to the other peer.\n   *\n   * @param {*} data The message to send\n   * @param {Object} options Options object\n   * @param {Boolean} [options.binary=false] Specifies whether `data` is binary\n   *     or text\n   * @param {Boolean} [options.compress=false] Specifies whether or not to\n   *     compress `data`\n   * @param {Boolean} [options.fin=false] Specifies whether the fragment is the\n   *     last one\n   * @param {Boolean} [options.mask=false] Specifies whether or not to mask\n   *     `data`\n   * @param {Function} [cb] Callback\n   * @public\n   */ send(data, options, cb) {\n        const perMessageDeflate = this._extensions[PerMessageDeflate.extensionName];\n        let opcode = options.binary ? 2 : 1;\n        let rsv1 = options.compress;\n        let byteLength;\n        let readOnly;\n        if (typeof data === \"string\") {\n            byteLength = Buffer.byteLength(data);\n            readOnly = false;\n        } else if (isBlob(data)) {\n            byteLength = data.size;\n            readOnly = false;\n        } else {\n            data = toBuffer(data);\n            byteLength = data.length;\n            readOnly = toBuffer.readOnly;\n        }\n        if (this._firstFragment) {\n            this._firstFragment = false;\n            if (rsv1 && perMessageDeflate && perMessageDeflate.params[perMessageDeflate._isServer ? \"server_no_context_takeover\" : \"client_no_context_takeover\"]) {\n                rsv1 = byteLength >= perMessageDeflate._threshold;\n            }\n            this._compress = rsv1;\n        } else {\n            rsv1 = false;\n            opcode = 0;\n        }\n        if (options.fin) this._firstFragment = true;\n        const opts = {\n            [kByteLength]: byteLength,\n            fin: options.fin,\n            generateMask: this._generateMask,\n            mask: options.mask,\n            maskBuffer: this._maskBuffer,\n            opcode,\n            readOnly,\n            rsv1\n        };\n        if (isBlob(data)) {\n            if (this._state !== DEFAULT) {\n                this.enqueue([\n                    this.getBlobData,\n                    data,\n                    this._compress,\n                    opts,\n                    cb\n                ]);\n            } else {\n                this.getBlobData(data, this._compress, opts, cb);\n            }\n        } else if (this._state !== DEFAULT) {\n            this.enqueue([\n                this.dispatch,\n                data,\n                this._compress,\n                opts,\n                cb\n            ]);\n        } else {\n            this.dispatch(data, this._compress, opts, cb);\n        }\n    }\n    /**\n   * Gets the contents of a blob as binary data.\n   *\n   * @param {Blob} blob The blob\n   * @param {Boolean} [compress=false] Specifies whether or not to compress\n   *     the data\n   * @param {Object} options Options object\n   * @param {Boolean} [options.fin=false] Specifies whether or not to set the\n   *     FIN bit\n   * @param {Function} [options.generateMask] The function used to generate the\n   *     masking key\n   * @param {Boolean} [options.mask=false] Specifies whether or not to mask\n   *     `data`\n   * @param {Buffer} [options.maskBuffer] The buffer used to store the masking\n   *     key\n   * @param {Number} options.opcode The opcode\n   * @param {Boolean} [options.readOnly=false] Specifies whether `data` can be\n   *     modified\n   * @param {Boolean} [options.rsv1=false] Specifies whether or not to set the\n   *     RSV1 bit\n   * @param {Function} [cb] Callback\n   * @private\n   */ getBlobData(blob, compress, options, cb) {\n        this._bufferedBytes += options[kByteLength];\n        this._state = GET_BLOB_DATA;\n        blob.arrayBuffer().then((arrayBuffer)=>{\n            if (this._socket.destroyed) {\n                const err = new Error(\"The socket was closed while the blob was being read\");\n                //\n                // `callCallbacks` is called in the next tick to ensure that errors\n                // that might be thrown in the callbacks behave like errors thrown\n                // outside the promise chain.\n                //\n                process.nextTick(callCallbacks, this, err, cb);\n                return;\n            }\n            this._bufferedBytes -= options[kByteLength];\n            const data = toBuffer(arrayBuffer);\n            if (!compress) {\n                this._state = DEFAULT;\n                this.sendFrame(Sender.frame(data, options), cb);\n                this.dequeue();\n            } else {\n                this.dispatch(data, compress, options, cb);\n            }\n        }).catch((err)=>{\n            //\n            // `onError` is called in the next tick for the same reason that\n            // `callCallbacks` above is.\n            //\n            process.nextTick(onError, this, err, cb);\n        });\n    }\n    /**\n   * Dispatches a message.\n   *\n   * @param {(Buffer|String)} data The message to send\n   * @param {Boolean} [compress=false] Specifies whether or not to compress\n   *     `data`\n   * @param {Object} options Options object\n   * @param {Boolean} [options.fin=false] Specifies whether or not to set the\n   *     FIN bit\n   * @param {Function} [options.generateMask] The function used to generate the\n   *     masking key\n   * @param {Boolean} [options.mask=false] Specifies whether or not to mask\n   *     `data`\n   * @param {Buffer} [options.maskBuffer] The buffer used to store the masking\n   *     key\n   * @param {Number} options.opcode The opcode\n   * @param {Boolean} [options.readOnly=false] Specifies whether `data` can be\n   *     modified\n   * @param {Boolean} [options.rsv1=false] Specifies whether or not to set the\n   *     RSV1 bit\n   * @param {Function} [cb] Callback\n   * @private\n   */ dispatch(data, compress, options, cb) {\n        if (!compress) {\n            this.sendFrame(Sender.frame(data, options), cb);\n            return;\n        }\n        const perMessageDeflate = this._extensions[PerMessageDeflate.extensionName];\n        this._bufferedBytes += options[kByteLength];\n        this._state = DEFLATING;\n        perMessageDeflate.compress(data, options.fin, (_, buf)=>{\n            if (this._socket.destroyed) {\n                const err = new Error(\"The socket was closed while data was being compressed\");\n                callCallbacks(this, err, cb);\n                return;\n            }\n            this._bufferedBytes -= options[kByteLength];\n            this._state = DEFAULT;\n            options.readOnly = false;\n            this.sendFrame(Sender.frame(buf, options), cb);\n            this.dequeue();\n        });\n    }\n    /**\n   * Executes queued send operations.\n   *\n   * @private\n   */ dequeue() {\n        while(this._state === DEFAULT && this._queue.length){\n            const params = this._queue.shift();\n            this._bufferedBytes -= params[3][kByteLength];\n            Reflect.apply(params[0], this, params.slice(1));\n        }\n    }\n    /**\n   * Enqueues a send operation.\n   *\n   * @param {Array} params Send operation parameters.\n   * @private\n   */ enqueue(params) {\n        this._bufferedBytes += params[3][kByteLength];\n        this._queue.push(params);\n    }\n    /**\n   * Sends a frame.\n   *\n   * @param {(Buffer | String)[]} list The frame to send\n   * @param {Function} [cb] Callback\n   * @private\n   */ sendFrame(list, cb) {\n        if (list.length === 2) {\n            this._socket.cork();\n            this._socket.write(list[0]);\n            this._socket.write(list[1], cb);\n            this._socket.uncork();\n        } else {\n            this._socket.write(list[0], cb);\n        }\n    }\n}\nmodule.exports = Sender;\n/**\n * Calls queued callbacks with an error.\n *\n * @param {Sender} sender The `Sender` instance\n * @param {Error} err The error to call the callbacks with\n * @param {Function} [cb] The first callback\n * @private\n */ function callCallbacks(sender, err, cb) {\n    if (typeof cb === \"function\") cb(err);\n    for(let i = 0; i < sender._queue.length; i++){\n        const params = sender._queue[i];\n        const callback = params[params.length - 1];\n        if (typeof callback === \"function\") callback(err);\n    }\n}\n/**\n * Handles a `Sender` error.\n *\n * @param {Sender} sender The `Sender` instance\n * @param {Error} err The error\n * @param {Function} [cb] The first pending callback\n * @private\n */ function onError(sender, err, cb) {\n    callCallbacks(sender, err, cb);\n    sender.onerror(err);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvd3MvbGliL3NlbmRlci5qcyIsIm1hcHBpbmdzIjoiQUFBQSx3RUFBd0UsR0FFeEU7QUFFQSxNQUFNLEVBQUVBLE1BQU0sRUFBRSxHQUFHQyxtQkFBT0EsQ0FBQztBQUMzQixNQUFNLEVBQUVDLGNBQWMsRUFBRSxHQUFHRCxtQkFBT0EsQ0FBQztBQUVuQyxNQUFNRSxvQkFBb0JGLG1CQUFPQSxDQUFDO0FBQ2xDLE1BQU0sRUFBRUcsWUFBWSxFQUFFQyxVQUFVLEVBQUVDLElBQUksRUFBRSxHQUFHTCxtQkFBT0EsQ0FBQztBQUNuRCxNQUFNLEVBQUVNLE1BQU0sRUFBRUMsaUJBQWlCLEVBQUUsR0FBR1AsbUJBQU9BLENBQUM7QUFDOUMsTUFBTSxFQUFFUSxNQUFNQyxTQUFTLEVBQUVDLFFBQVEsRUFBRSxHQUFHVixtQkFBT0EsQ0FBQztBQUU5QyxNQUFNVyxjQUFjQyxPQUFPO0FBQzNCLE1BQU1DLGFBQWFDLE9BQU9DLEtBQUssQ0FBQztBQUNoQyxNQUFNQyxtQkFBbUIsSUFBSTtBQUM3QixJQUFJQztBQUNKLElBQUlDLG9CQUFvQkY7QUFFeEIsTUFBTUcsVUFBVTtBQUNoQixNQUFNQyxZQUFZO0FBQ2xCLE1BQU1DLGdCQUFnQjtBQUV0Qjs7Q0FFQyxHQUNELE1BQU1DO0lBQ0o7Ozs7Ozs7R0FPQyxHQUNEQyxZQUFZQyxNQUFNLEVBQUVDLFVBQVUsRUFBRUMsWUFBWSxDQUFFO1FBQzVDLElBQUksQ0FBQ0MsV0FBVyxHQUFHRixjQUFjLENBQUM7UUFFbEMsSUFBSUMsY0FBYztZQUNoQixJQUFJLENBQUNFLGFBQWEsR0FBR0Y7WUFDckIsSUFBSSxDQUFDRyxXQUFXLEdBQUdmLE9BQU9DLEtBQUssQ0FBQztRQUNsQztRQUVBLElBQUksQ0FBQ2UsT0FBTyxHQUFHTjtRQUVmLElBQUksQ0FBQ08sY0FBYyxHQUFHO1FBQ3RCLElBQUksQ0FBQ0MsU0FBUyxHQUFHO1FBRWpCLElBQUksQ0FBQ0MsY0FBYyxHQUFHO1FBQ3RCLElBQUksQ0FBQ0MsTUFBTSxHQUFHLEVBQUU7UUFDaEIsSUFBSSxDQUFDQyxNQUFNLEdBQUdoQjtRQUNkLElBQUksQ0FBQ2lCLE9BQU8sR0FBRy9CO1FBQ2YsSUFBSSxDQUFDRCxXQUFXLEdBQUdpQztJQUNyQjtJQUVBOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OztHQW9CQyxHQUNELE9BQU9DLE1BQU1DLElBQUksRUFBRUMsT0FBTyxFQUFFO1FBQzFCLElBQUloQztRQUNKLElBQUlpQyxRQUFRO1FBQ1osSUFBSUMsU0FBUztRQUNiLElBQUlDLGNBQWM7UUFFbEIsSUFBSUgsUUFBUWhDLElBQUksRUFBRTtZQUNoQkEsT0FBT2dDLFFBQVEzQixVQUFVLElBQUlBO1lBRTdCLElBQUkyQixRQUFRZCxZQUFZLEVBQUU7Z0JBQ3hCYyxRQUFRZCxZQUFZLENBQUNsQjtZQUN2QixPQUFPO2dCQUNMLElBQUlVLHNCQUFzQkYsa0JBQWtCO29CQUMxQyx5QkFBeUIsR0FDekIsSUFBSUMsZUFBZW9CLFdBQVc7d0JBQzVCLEVBQUU7d0JBQ0YsaUVBQWlFO3dCQUNqRSxxQ0FBcUM7d0JBQ3JDLEVBQUU7d0JBQ0ZwQixhQUFhSCxPQUFPQyxLQUFLLENBQUNDO29CQUM1QjtvQkFFQWYsZUFBZWdCLFlBQVksR0FBR0Q7b0JBQzlCRSxvQkFBb0I7Z0JBQ3RCO2dCQUVBVixJQUFJLENBQUMsRUFBRSxHQUFHUyxVQUFVLENBQUNDLG9CQUFvQjtnQkFDekNWLElBQUksQ0FBQyxFQUFFLEdBQUdTLFVBQVUsQ0FBQ0Msb0JBQW9CO2dCQUN6Q1YsSUFBSSxDQUFDLEVBQUUsR0FBR1MsVUFBVSxDQUFDQyxvQkFBb0I7Z0JBQ3pDVixJQUFJLENBQUMsRUFBRSxHQUFHUyxVQUFVLENBQUNDLG9CQUFvQjtZQUMzQztZQUVBeUIsY0FBYyxDQUFDbkMsSUFBSSxDQUFDLEVBQUUsR0FBR0EsSUFBSSxDQUFDLEVBQUUsR0FBR0EsSUFBSSxDQUFDLEVBQUUsR0FBR0EsSUFBSSxDQUFDLEVBQUUsTUFBTTtZQUMxRGtDLFNBQVM7UUFDWDtRQUVBLElBQUlFO1FBRUosSUFBSSxPQUFPTCxTQUFTLFVBQVU7WUFDNUIsSUFDRSxDQUFDLENBQUNDLFFBQVFoQyxJQUFJLElBQUltQyxXQUFVLEtBQzVCSCxPQUFPLENBQUM3QixZQUFZLEtBQUswQixXQUN6QjtnQkFDQU8sYUFBYUosT0FBTyxDQUFDN0IsWUFBWTtZQUNuQyxPQUFPO2dCQUNMNEIsT0FBT3pCLE9BQU8rQixJQUFJLENBQUNOO2dCQUNuQkssYUFBYUwsS0FBS08sTUFBTTtZQUMxQjtRQUNGLE9BQU87WUFDTEYsYUFBYUwsS0FBS08sTUFBTTtZQUN4QkwsUUFBUUQsUUFBUWhDLElBQUksSUFBSWdDLFFBQVFPLFFBQVEsSUFBSSxDQUFDSjtRQUMvQztRQUVBLElBQUlLLGdCQUFnQko7UUFFcEIsSUFBSUEsY0FBYyxPQUFPO1lBQ3ZCRixVQUFVO1lBQ1ZNLGdCQUFnQjtRQUNsQixPQUFPLElBQUlKLGFBQWEsS0FBSztZQUMzQkYsVUFBVTtZQUNWTSxnQkFBZ0I7UUFDbEI7UUFFQSxNQUFNQyxTQUFTbkMsT0FBT29DLFdBQVcsQ0FBQ1QsUUFBUUcsYUFBYUYsU0FBU0E7UUFFaEVPLE1BQU0sQ0FBQyxFQUFFLEdBQUdULFFBQVFXLEdBQUcsR0FBR1gsUUFBUVksTUFBTSxHQUFHLE9BQU9aLFFBQVFZLE1BQU07UUFDaEUsSUFBSVosUUFBUWEsSUFBSSxFQUFFSixNQUFNLENBQUMsRUFBRSxJQUFJO1FBRS9CQSxNQUFNLENBQUMsRUFBRSxHQUFHRDtRQUVaLElBQUlBLGtCQUFrQixLQUFLO1lBQ3pCQyxPQUFPSyxhQUFhLENBQUNWLFlBQVk7UUFDbkMsT0FBTyxJQUFJSSxrQkFBa0IsS0FBSztZQUNoQ0MsTUFBTSxDQUFDLEVBQUUsR0FBR0EsTUFBTSxDQUFDLEVBQUUsR0FBRztZQUN4QkEsT0FBT00sV0FBVyxDQUFDWCxZQUFZLEdBQUc7UUFDcEM7UUFFQSxJQUFJLENBQUNKLFFBQVFoQyxJQUFJLEVBQUUsT0FBTztZQUFDeUM7WUFBUVY7U0FBSztRQUV4Q1UsTUFBTSxDQUFDLEVBQUUsSUFBSTtRQUNiQSxNQUFNLENBQUNQLFNBQVMsRUFBRSxHQUFHbEMsSUFBSSxDQUFDLEVBQUU7UUFDNUJ5QyxNQUFNLENBQUNQLFNBQVMsRUFBRSxHQUFHbEMsSUFBSSxDQUFDLEVBQUU7UUFDNUJ5QyxNQUFNLENBQUNQLFNBQVMsRUFBRSxHQUFHbEMsSUFBSSxDQUFDLEVBQUU7UUFDNUJ5QyxNQUFNLENBQUNQLFNBQVMsRUFBRSxHQUFHbEMsSUFBSSxDQUFDLEVBQUU7UUFFNUIsSUFBSW1DLGFBQWEsT0FBTztZQUFDTTtZQUFRVjtTQUFLO1FBRXRDLElBQUlFLE9BQU87WUFDVGhDLFVBQVU4QixNQUFNL0IsTUFBTXlDLFFBQVFQLFFBQVFFO1lBQ3RDLE9BQU87Z0JBQUNLO2FBQU87UUFDakI7UUFFQXhDLFVBQVU4QixNQUFNL0IsTUFBTStCLE1BQU0sR0FBR0s7UUFDL0IsT0FBTztZQUFDSztZQUFRVjtTQUFLO0lBQ3ZCO0lBRUE7Ozs7Ozs7O0dBUUMsR0FDRGlCLE1BQU1DLElBQUksRUFBRWxCLElBQUksRUFBRS9CLElBQUksRUFBRWtELEVBQUUsRUFBRTtRQUMxQixJQUFJQztRQUVKLElBQUlGLFNBQVNwQixXQUFXO1lBQ3RCc0IsTUFBTXhEO1FBQ1IsT0FBTyxJQUFJLE9BQU9zRCxTQUFTLFlBQVksQ0FBQ2xELGtCQUFrQmtELE9BQU87WUFDL0QsTUFBTSxJQUFJRyxVQUFVO1FBQ3RCLE9BQU8sSUFBSXJCLFNBQVNGLGFBQWEsQ0FBQ0UsS0FBS08sTUFBTSxFQUFFO1lBQzdDYSxNQUFNN0MsT0FBT29DLFdBQVcsQ0FBQztZQUN6QlMsSUFBSUwsYUFBYSxDQUFDRyxNQUFNO1FBQzFCLE9BQU87WUFDTCxNQUFNWCxTQUFTaEMsT0FBTytDLFVBQVUsQ0FBQ3RCO1lBRWpDLElBQUlPLFNBQVMsS0FBSztnQkFDaEIsTUFBTSxJQUFJZ0IsV0FBVztZQUN2QjtZQUVBSCxNQUFNN0MsT0FBT29DLFdBQVcsQ0FBQyxJQUFJSjtZQUM3QmEsSUFBSUwsYUFBYSxDQUFDRyxNQUFNO1lBRXhCLElBQUksT0FBT2xCLFNBQVMsVUFBVTtnQkFDNUJvQixJQUFJSSxLQUFLLENBQUN4QixNQUFNO1lBQ2xCLE9BQU87Z0JBQ0xvQixJQUFJSyxHQUFHLENBQUN6QixNQUFNO1lBQ2hCO1FBQ0Y7UUFFQSxNQUFNQyxVQUFVO1lBQ2QsQ0FBQzdCLFlBQVksRUFBRWdELElBQUliLE1BQU07WUFDekJLLEtBQUs7WUFDTHpCLGNBQWMsSUFBSSxDQUFDRSxhQUFhO1lBQ2hDcEI7WUFDQUssWUFBWSxJQUFJLENBQUNnQixXQUFXO1lBQzVCdUIsUUFBUTtZQUNSTCxVQUFVO1lBQ1ZNLE1BQU07UUFDUjtRQUVBLElBQUksSUFBSSxDQUFDbEIsTUFBTSxLQUFLaEIsU0FBUztZQUMzQixJQUFJLENBQUM4QyxPQUFPLENBQUM7Z0JBQUMsSUFBSSxDQUFDQyxRQUFRO2dCQUFFUDtnQkFBSztnQkFBT25CO2dCQUFTa0I7YUFBRztRQUN2RCxPQUFPO1lBQ0wsSUFBSSxDQUFDUyxTQUFTLENBQUM3QyxPQUFPZ0IsS0FBSyxDQUFDcUIsS0FBS25CLFVBQVVrQjtRQUM3QztJQUNGO0lBRUE7Ozs7Ozs7R0FPQyxHQUNEVSxLQUFLN0IsSUFBSSxFQUFFL0IsSUFBSSxFQUFFa0QsRUFBRSxFQUFFO1FBQ25CLElBQUlHO1FBQ0osSUFBSWQ7UUFFSixJQUFJLE9BQU9SLFNBQVMsVUFBVTtZQUM1QnNCLGFBQWEvQyxPQUFPK0MsVUFBVSxDQUFDdEI7WUFDL0JRLFdBQVc7UUFDYixPQUFPLElBQUl6QyxPQUFPaUMsT0FBTztZQUN2QnNCLGFBQWF0QixLQUFLOEIsSUFBSTtZQUN0QnRCLFdBQVc7UUFDYixPQUFPO1lBQ0xSLE9BQU83QixTQUFTNkI7WUFDaEJzQixhQUFhdEIsS0FBS08sTUFBTTtZQUN4QkMsV0FBV3JDLFNBQVNxQyxRQUFRO1FBQzlCO1FBRUEsSUFBSWMsYUFBYSxLQUFLO1lBQ3BCLE1BQU0sSUFBSUMsV0FBVztRQUN2QjtRQUVBLE1BQU10QixVQUFVO1lBQ2QsQ0FBQzdCLFlBQVksRUFBRWtEO1lBQ2ZWLEtBQUs7WUFDTHpCLGNBQWMsSUFBSSxDQUFDRSxhQUFhO1lBQ2hDcEI7WUFDQUssWUFBWSxJQUFJLENBQUNnQixXQUFXO1lBQzVCdUIsUUFBUTtZQUNSTDtZQUNBTSxNQUFNO1FBQ1I7UUFFQSxJQUFJL0MsT0FBT2lDLE9BQU87WUFDaEIsSUFBSSxJQUFJLENBQUNKLE1BQU0sS0FBS2hCLFNBQVM7Z0JBQzNCLElBQUksQ0FBQzhDLE9BQU8sQ0FBQztvQkFBQyxJQUFJLENBQUNLLFdBQVc7b0JBQUUvQjtvQkFBTTtvQkFBT0M7b0JBQVNrQjtpQkFBRztZQUMzRCxPQUFPO2dCQUNMLElBQUksQ0FBQ1ksV0FBVyxDQUFDL0IsTUFBTSxPQUFPQyxTQUFTa0I7WUFDekM7UUFDRixPQUFPLElBQUksSUFBSSxDQUFDdkIsTUFBTSxLQUFLaEIsU0FBUztZQUNsQyxJQUFJLENBQUM4QyxPQUFPLENBQUM7Z0JBQUMsSUFBSSxDQUFDQyxRQUFRO2dCQUFFM0I7Z0JBQU07Z0JBQU9DO2dCQUFTa0I7YUFBRztRQUN4RCxPQUFPO1lBQ0wsSUFBSSxDQUFDUyxTQUFTLENBQUM3QyxPQUFPZ0IsS0FBSyxDQUFDQyxNQUFNQyxVQUFVa0I7UUFDOUM7SUFDRjtJQUVBOzs7Ozs7O0dBT0MsR0FDRGEsS0FBS2hDLElBQUksRUFBRS9CLElBQUksRUFBRWtELEVBQUUsRUFBRTtRQUNuQixJQUFJRztRQUNKLElBQUlkO1FBRUosSUFBSSxPQUFPUixTQUFTLFVBQVU7WUFDNUJzQixhQUFhL0MsT0FBTytDLFVBQVUsQ0FBQ3RCO1lBQy9CUSxXQUFXO1FBQ2IsT0FBTyxJQUFJekMsT0FBT2lDLE9BQU87WUFDdkJzQixhQUFhdEIsS0FBSzhCLElBQUk7WUFDdEJ0QixXQUFXO1FBQ2IsT0FBTztZQUNMUixPQUFPN0IsU0FBUzZCO1lBQ2hCc0IsYUFBYXRCLEtBQUtPLE1BQU07WUFDeEJDLFdBQVdyQyxTQUFTcUMsUUFBUTtRQUM5QjtRQUVBLElBQUljLGFBQWEsS0FBSztZQUNwQixNQUFNLElBQUlDLFdBQVc7UUFDdkI7UUFFQSxNQUFNdEIsVUFBVTtZQUNkLENBQUM3QixZQUFZLEVBQUVrRDtZQUNmVixLQUFLO1lBQ0x6QixjQUFjLElBQUksQ0FBQ0UsYUFBYTtZQUNoQ3BCO1lBQ0FLLFlBQVksSUFBSSxDQUFDZ0IsV0FBVztZQUM1QnVCLFFBQVE7WUFDUkw7WUFDQU0sTUFBTTtRQUNSO1FBRUEsSUFBSS9DLE9BQU9pQyxPQUFPO1lBQ2hCLElBQUksSUFBSSxDQUFDSixNQUFNLEtBQUtoQixTQUFTO2dCQUMzQixJQUFJLENBQUM4QyxPQUFPLENBQUM7b0JBQUMsSUFBSSxDQUFDSyxXQUFXO29CQUFFL0I7b0JBQU07b0JBQU9DO29CQUFTa0I7aUJBQUc7WUFDM0QsT0FBTztnQkFDTCxJQUFJLENBQUNZLFdBQVcsQ0FBQy9CLE1BQU0sT0FBT0MsU0FBU2tCO1lBQ3pDO1FBQ0YsT0FBTyxJQUFJLElBQUksQ0FBQ3ZCLE1BQU0sS0FBS2hCLFNBQVM7WUFDbEMsSUFBSSxDQUFDOEMsT0FBTyxDQUFDO2dCQUFDLElBQUksQ0FBQ0MsUUFBUTtnQkFBRTNCO2dCQUFNO2dCQUFPQztnQkFBU2tCO2FBQUc7UUFDeEQsT0FBTztZQUNMLElBQUksQ0FBQ1MsU0FBUyxDQUFDN0MsT0FBT2dCLEtBQUssQ0FBQ0MsTUFBTUMsVUFBVWtCO1FBQzlDO0lBQ0Y7SUFFQTs7Ozs7Ozs7Ozs7Ozs7O0dBZUMsR0FDRGMsS0FBS2pDLElBQUksRUFBRUMsT0FBTyxFQUFFa0IsRUFBRSxFQUFFO1FBQ3RCLE1BQU1lLG9CQUFvQixJQUFJLENBQUM5QyxXQUFXLENBQUN6QixrQkFBa0J3RSxhQUFhLENBQUM7UUFDM0UsSUFBSXRCLFNBQVNaLFFBQVFtQyxNQUFNLEdBQUcsSUFBSTtRQUNsQyxJQUFJdEIsT0FBT2IsUUFBUW9DLFFBQVE7UUFFM0IsSUFBSWY7UUFDSixJQUFJZDtRQUVKLElBQUksT0FBT1IsU0FBUyxVQUFVO1lBQzVCc0IsYUFBYS9DLE9BQU8rQyxVQUFVLENBQUN0QjtZQUMvQlEsV0FBVztRQUNiLE9BQU8sSUFBSXpDLE9BQU9pQyxPQUFPO1lBQ3ZCc0IsYUFBYXRCLEtBQUs4QixJQUFJO1lBQ3RCdEIsV0FBVztRQUNiLE9BQU87WUFDTFIsT0FBTzdCLFNBQVM2QjtZQUNoQnNCLGFBQWF0QixLQUFLTyxNQUFNO1lBQ3hCQyxXQUFXckMsU0FBU3FDLFFBQVE7UUFDOUI7UUFFQSxJQUFJLElBQUksQ0FBQ2hCLGNBQWMsRUFBRTtZQUN2QixJQUFJLENBQUNBLGNBQWMsR0FBRztZQUN0QixJQUNFc0IsUUFDQW9CLHFCQUNBQSxrQkFBa0JJLE1BQU0sQ0FDdEJKLGtCQUFrQkssU0FBUyxHQUN2QiwrQkFDQSw2QkFDTCxFQUNEO2dCQUNBekIsT0FBT1EsY0FBY1ksa0JBQWtCTSxVQUFVO1lBQ25EO1lBQ0EsSUFBSSxDQUFDL0MsU0FBUyxHQUFHcUI7UUFDbkIsT0FBTztZQUNMQSxPQUFPO1lBQ1BELFNBQVM7UUFDWDtRQUVBLElBQUlaLFFBQVFXLEdBQUcsRUFBRSxJQUFJLENBQUNwQixjQUFjLEdBQUc7UUFFdkMsTUFBTWlELE9BQU87WUFDWCxDQUFDckUsWUFBWSxFQUFFa0Q7WUFDZlYsS0FBS1gsUUFBUVcsR0FBRztZQUNoQnpCLGNBQWMsSUFBSSxDQUFDRSxhQUFhO1lBQ2hDcEIsTUFBTWdDLFFBQVFoQyxJQUFJO1lBQ2xCSyxZQUFZLElBQUksQ0FBQ2dCLFdBQVc7WUFDNUJ1QjtZQUNBTDtZQUNBTTtRQUNGO1FBRUEsSUFBSS9DLE9BQU9pQyxPQUFPO1lBQ2hCLElBQUksSUFBSSxDQUFDSixNQUFNLEtBQUtoQixTQUFTO2dCQUMzQixJQUFJLENBQUM4QyxPQUFPLENBQUM7b0JBQUMsSUFBSSxDQUFDSyxXQUFXO29CQUFFL0I7b0JBQU0sSUFBSSxDQUFDUCxTQUFTO29CQUFFZ0Q7b0JBQU10QjtpQkFBRztZQUNqRSxPQUFPO2dCQUNMLElBQUksQ0FBQ1ksV0FBVyxDQUFDL0IsTUFBTSxJQUFJLENBQUNQLFNBQVMsRUFBRWdELE1BQU10QjtZQUMvQztRQUNGLE9BQU8sSUFBSSxJQUFJLENBQUN2QixNQUFNLEtBQUtoQixTQUFTO1lBQ2xDLElBQUksQ0FBQzhDLE9BQU8sQ0FBQztnQkFBQyxJQUFJLENBQUNDLFFBQVE7Z0JBQUUzQjtnQkFBTSxJQUFJLENBQUNQLFNBQVM7Z0JBQUVnRDtnQkFBTXRCO2FBQUc7UUFDOUQsT0FBTztZQUNMLElBQUksQ0FBQ1EsUUFBUSxDQUFDM0IsTUFBTSxJQUFJLENBQUNQLFNBQVMsRUFBRWdELE1BQU10QjtRQUM1QztJQUNGO0lBRUE7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7R0FzQkMsR0FDRFksWUFBWVcsSUFBSSxFQUFFTCxRQUFRLEVBQUVwQyxPQUFPLEVBQUVrQixFQUFFLEVBQUU7UUFDdkMsSUFBSSxDQUFDekIsY0FBYyxJQUFJTyxPQUFPLENBQUM3QixZQUFZO1FBQzNDLElBQUksQ0FBQ3dCLE1BQU0sR0FBR2Q7UUFFZDRELEtBQ0dDLFdBQVcsR0FDWEMsSUFBSSxDQUFDLENBQUNEO1lBQ0wsSUFBSSxJQUFJLENBQUNwRCxPQUFPLENBQUNzRCxTQUFTLEVBQUU7Z0JBQzFCLE1BQU1DLE1BQU0sSUFBSUMsTUFDZDtnQkFHRixFQUFFO2dCQUNGLG1FQUFtRTtnQkFDbkUsa0VBQWtFO2dCQUNsRSw2QkFBNkI7Z0JBQzdCLEVBQUU7Z0JBQ0ZDLFFBQVFDLFFBQVEsQ0FBQ0MsZUFBZSxJQUFJLEVBQUVKLEtBQUszQjtnQkFDM0M7WUFDRjtZQUVBLElBQUksQ0FBQ3pCLGNBQWMsSUFBSU8sT0FBTyxDQUFDN0IsWUFBWTtZQUMzQyxNQUFNNEIsT0FBTzdCLFNBQVN3RTtZQUV0QixJQUFJLENBQUNOLFVBQVU7Z0JBQ2IsSUFBSSxDQUFDekMsTUFBTSxHQUFHaEI7Z0JBQ2QsSUFBSSxDQUFDZ0QsU0FBUyxDQUFDN0MsT0FBT2dCLEtBQUssQ0FBQ0MsTUFBTUMsVUFBVWtCO2dCQUM1QyxJQUFJLENBQUNnQyxPQUFPO1lBQ2QsT0FBTztnQkFDTCxJQUFJLENBQUN4QixRQUFRLENBQUMzQixNQUFNcUMsVUFBVXBDLFNBQVNrQjtZQUN6QztRQUNGLEdBQ0NpQyxLQUFLLENBQUMsQ0FBQ047WUFDTixFQUFFO1lBQ0YsZ0VBQWdFO1lBQ2hFLDRCQUE0QjtZQUM1QixFQUFFO1lBQ0ZFLFFBQVFDLFFBQVEsQ0FBQ0ksU0FBUyxJQUFJLEVBQUVQLEtBQUszQjtRQUN2QztJQUNKO0lBRUE7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7R0FzQkMsR0FDRFEsU0FBUzNCLElBQUksRUFBRXFDLFFBQVEsRUFBRXBDLE9BQU8sRUFBRWtCLEVBQUUsRUFBRTtRQUNwQyxJQUFJLENBQUNrQixVQUFVO1lBQ2IsSUFBSSxDQUFDVCxTQUFTLENBQUM3QyxPQUFPZ0IsS0FBSyxDQUFDQyxNQUFNQyxVQUFVa0I7WUFDNUM7UUFDRjtRQUVBLE1BQU1lLG9CQUFvQixJQUFJLENBQUM5QyxXQUFXLENBQUN6QixrQkFBa0J3RSxhQUFhLENBQUM7UUFFM0UsSUFBSSxDQUFDekMsY0FBYyxJQUFJTyxPQUFPLENBQUM3QixZQUFZO1FBQzNDLElBQUksQ0FBQ3dCLE1BQU0sR0FBR2Y7UUFDZHFELGtCQUFrQkcsUUFBUSxDQUFDckMsTUFBTUMsUUFBUVcsR0FBRyxFQUFFLENBQUMwQyxHQUFHbEM7WUFDaEQsSUFBSSxJQUFJLENBQUM3QixPQUFPLENBQUNzRCxTQUFTLEVBQUU7Z0JBQzFCLE1BQU1DLE1BQU0sSUFBSUMsTUFDZDtnQkFHRkcsY0FBYyxJQUFJLEVBQUVKLEtBQUszQjtnQkFDekI7WUFDRjtZQUVBLElBQUksQ0FBQ3pCLGNBQWMsSUFBSU8sT0FBTyxDQUFDN0IsWUFBWTtZQUMzQyxJQUFJLENBQUN3QixNQUFNLEdBQUdoQjtZQUNkcUIsUUFBUU8sUUFBUSxHQUFHO1lBQ25CLElBQUksQ0FBQ29CLFNBQVMsQ0FBQzdDLE9BQU9nQixLQUFLLENBQUNxQixLQUFLbkIsVUFBVWtCO1lBQzNDLElBQUksQ0FBQ2dDLE9BQU87UUFDZDtJQUNGO0lBRUE7Ozs7R0FJQyxHQUNEQSxVQUFVO1FBQ1IsTUFBTyxJQUFJLENBQUN2RCxNQUFNLEtBQUtoQixXQUFXLElBQUksQ0FBQ2UsTUFBTSxDQUFDWSxNQUFNLENBQUU7WUFDcEQsTUFBTStCLFNBQVMsSUFBSSxDQUFDM0MsTUFBTSxDQUFDNEQsS0FBSztZQUVoQyxJQUFJLENBQUM3RCxjQUFjLElBQUk0QyxNQUFNLENBQUMsRUFBRSxDQUFDbEUsWUFBWTtZQUM3Q29GLFFBQVFDLEtBQUssQ0FBQ25CLE1BQU0sQ0FBQyxFQUFFLEVBQUUsSUFBSSxFQUFFQSxPQUFPb0IsS0FBSyxDQUFDO1FBQzlDO0lBQ0Y7SUFFQTs7Ozs7R0FLQyxHQUNEaEMsUUFBUVksTUFBTSxFQUFFO1FBQ2QsSUFBSSxDQUFDNUMsY0FBYyxJQUFJNEMsTUFBTSxDQUFDLEVBQUUsQ0FBQ2xFLFlBQVk7UUFDN0MsSUFBSSxDQUFDdUIsTUFBTSxDQUFDZ0UsSUFBSSxDQUFDckI7SUFDbkI7SUFFQTs7Ozs7O0dBTUMsR0FDRFYsVUFBVWdDLElBQUksRUFBRXpDLEVBQUUsRUFBRTtRQUNsQixJQUFJeUMsS0FBS3JELE1BQU0sS0FBSyxHQUFHO1lBQ3JCLElBQUksQ0FBQ2hCLE9BQU8sQ0FBQ3NFLElBQUk7WUFDakIsSUFBSSxDQUFDdEUsT0FBTyxDQUFDaUMsS0FBSyxDQUFDb0MsSUFBSSxDQUFDLEVBQUU7WUFDMUIsSUFBSSxDQUFDckUsT0FBTyxDQUFDaUMsS0FBSyxDQUFDb0MsSUFBSSxDQUFDLEVBQUUsRUFBRXpDO1lBQzVCLElBQUksQ0FBQzVCLE9BQU8sQ0FBQ3VFLE1BQU07UUFDckIsT0FBTztZQUNMLElBQUksQ0FBQ3ZFLE9BQU8sQ0FBQ2lDLEtBQUssQ0FBQ29DLElBQUksQ0FBQyxFQUFFLEVBQUV6QztRQUM5QjtJQUNGO0FBQ0Y7QUFFQTRDLE9BQU9DLE9BQU8sR0FBR2pGO0FBRWpCOzs7Ozs7O0NBT0MsR0FDRCxTQUFTbUUsY0FBY2UsTUFBTSxFQUFFbkIsR0FBRyxFQUFFM0IsRUFBRTtJQUNwQyxJQUFJLE9BQU9BLE9BQU8sWUFBWUEsR0FBRzJCO0lBRWpDLElBQUssSUFBSW9CLElBQUksR0FBR0EsSUFBSUQsT0FBT3RFLE1BQU0sQ0FBQ1ksTUFBTSxFQUFFMkQsSUFBSztRQUM3QyxNQUFNNUIsU0FBUzJCLE9BQU90RSxNQUFNLENBQUN1RSxFQUFFO1FBQy9CLE1BQU1DLFdBQVc3QixNQUFNLENBQUNBLE9BQU8vQixNQUFNLEdBQUcsRUFBRTtRQUUxQyxJQUFJLE9BQU80RCxhQUFhLFlBQVlBLFNBQVNyQjtJQUMvQztBQUNGO0FBRUE7Ozs7Ozs7Q0FPQyxHQUNELFNBQVNPLFFBQVFZLE1BQU0sRUFBRW5CLEdBQUcsRUFBRTNCLEVBQUU7SUFDOUIrQixjQUFjZSxRQUFRbkIsS0FBSzNCO0lBQzNCOEMsT0FBT3BFLE9BQU8sQ0FBQ2lEO0FBQ2pCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3R1ZGVudC1tYW5hZ2VtZW50LXN5c3RlbS8uL25vZGVfbW9kdWxlcy93cy9saWIvc2VuZGVyLmpzPzZiOGIiXSwic291cmNlc0NvbnRlbnQiOlsiLyogZXNsaW50IG5vLXVudXNlZC12YXJzOiBbXCJlcnJvclwiLCB7IFwidmFyc0lnbm9yZVBhdHRlcm5cIjogXCJeRHVwbGV4XCIgfV0gKi9cblxuJ3VzZSBzdHJpY3QnO1xuXG5jb25zdCB7IER1cGxleCB9ID0gcmVxdWlyZSgnc3RyZWFtJyk7XG5jb25zdCB7IHJhbmRvbUZpbGxTeW5jIH0gPSByZXF1aXJlKCdjcnlwdG8nKTtcblxuY29uc3QgUGVyTWVzc2FnZURlZmxhdGUgPSByZXF1aXJlKCcuL3Blcm1lc3NhZ2UtZGVmbGF0ZScpO1xuY29uc3QgeyBFTVBUWV9CVUZGRVIsIGtXZWJTb2NrZXQsIE5PT1AgfSA9IHJlcXVpcmUoJy4vY29uc3RhbnRzJyk7XG5jb25zdCB7IGlzQmxvYiwgaXNWYWxpZFN0YXR1c0NvZGUgfSA9IHJlcXVpcmUoJy4vdmFsaWRhdGlvbicpO1xuY29uc3QgeyBtYXNrOiBhcHBseU1hc2ssIHRvQnVmZmVyIH0gPSByZXF1aXJlKCcuL2J1ZmZlci11dGlsJyk7XG5cbmNvbnN0IGtCeXRlTGVuZ3RoID0gU3ltYm9sKCdrQnl0ZUxlbmd0aCcpO1xuY29uc3QgbWFza0J1ZmZlciA9IEJ1ZmZlci5hbGxvYyg0KTtcbmNvbnN0IFJBTkRPTV9QT09MX1NJWkUgPSA4ICogMTAyNDtcbmxldCByYW5kb21Qb29sO1xubGV0IHJhbmRvbVBvb2xQb2ludGVyID0gUkFORE9NX1BPT0xfU0laRTtcblxuY29uc3QgREVGQVVMVCA9IDA7XG5jb25zdCBERUZMQVRJTkcgPSAxO1xuY29uc3QgR0VUX0JMT0JfREFUQSA9IDI7XG5cbi8qKlxuICogSHlCaSBTZW5kZXIgaW1wbGVtZW50YXRpb24uXG4gKi9cbmNsYXNzIFNlbmRlciB7XG4gIC8qKlxuICAgKiBDcmVhdGVzIGEgU2VuZGVyIGluc3RhbmNlLlxuICAgKlxuICAgKiBAcGFyYW0ge0R1cGxleH0gc29ja2V0IFRoZSBjb25uZWN0aW9uIHNvY2tldFxuICAgKiBAcGFyYW0ge09iamVjdH0gW2V4dGVuc2lvbnNdIEFuIG9iamVjdCBjb250YWluaW5nIHRoZSBuZWdvdGlhdGVkIGV4dGVuc2lvbnNcbiAgICogQHBhcmFtIHtGdW5jdGlvbn0gW2dlbmVyYXRlTWFza10gVGhlIGZ1bmN0aW9uIHVzZWQgdG8gZ2VuZXJhdGUgdGhlIG1hc2tpbmdcbiAgICogICAgIGtleVxuICAgKi9cbiAgY29uc3RydWN0b3Ioc29ja2V0LCBleHRlbnNpb25zLCBnZW5lcmF0ZU1hc2spIHtcbiAgICB0aGlzLl9leHRlbnNpb25zID0gZXh0ZW5zaW9ucyB8fCB7fTtcblxuICAgIGlmIChnZW5lcmF0ZU1hc2spIHtcbiAgICAgIHRoaXMuX2dlbmVyYXRlTWFzayA9IGdlbmVyYXRlTWFzaztcbiAgICAgIHRoaXMuX21hc2tCdWZmZXIgPSBCdWZmZXIuYWxsb2MoNCk7XG4gICAgfVxuXG4gICAgdGhpcy5fc29ja2V0ID0gc29ja2V0O1xuXG4gICAgdGhpcy5fZmlyc3RGcmFnbWVudCA9IHRydWU7XG4gICAgdGhpcy5fY29tcHJlc3MgPSBmYWxzZTtcblxuICAgIHRoaXMuX2J1ZmZlcmVkQnl0ZXMgPSAwO1xuICAgIHRoaXMuX3F1ZXVlID0gW107XG4gICAgdGhpcy5fc3RhdGUgPSBERUZBVUxUO1xuICAgIHRoaXMub25lcnJvciA9IE5PT1A7XG4gICAgdGhpc1trV2ViU29ja2V0XSA9IHVuZGVmaW5lZDtcbiAgfVxuXG4gIC8qKlxuICAgKiBGcmFtZXMgYSBwaWVjZSBvZiBkYXRhIGFjY29yZGluZyB0byB0aGUgSHlCaSBXZWJTb2NrZXQgcHJvdG9jb2wuXG4gICAqXG4gICAqIEBwYXJhbSB7KEJ1ZmZlcnxTdHJpbmcpfSBkYXRhIFRoZSBkYXRhIHRvIGZyYW1lXG4gICAqIEBwYXJhbSB7T2JqZWN0fSBvcHRpb25zIE9wdGlvbnMgb2JqZWN0XG4gICAqIEBwYXJhbSB7Qm9vbGVhbn0gW29wdGlvbnMuZmluPWZhbHNlXSBTcGVjaWZpZXMgd2hldGhlciBvciBub3QgdG8gc2V0IHRoZVxuICAgKiAgICAgRklOIGJpdFxuICAgKiBAcGFyYW0ge0Z1bmN0aW9ufSBbb3B0aW9ucy5nZW5lcmF0ZU1hc2tdIFRoZSBmdW5jdGlvbiB1c2VkIHRvIGdlbmVyYXRlIHRoZVxuICAgKiAgICAgbWFza2luZyBrZXlcbiAgICogQHBhcmFtIHtCb29sZWFufSBbb3B0aW9ucy5tYXNrPWZhbHNlXSBTcGVjaWZpZXMgd2hldGhlciBvciBub3QgdG8gbWFza1xuICAgKiAgICAgYGRhdGFgXG4gICAqIEBwYXJhbSB7QnVmZmVyfSBbb3B0aW9ucy5tYXNrQnVmZmVyXSBUaGUgYnVmZmVyIHVzZWQgdG8gc3RvcmUgdGhlIG1hc2tpbmdcbiAgICogICAgIGtleVxuICAgKiBAcGFyYW0ge051bWJlcn0gb3B0aW9ucy5vcGNvZGUgVGhlIG9wY29kZVxuICAgKiBAcGFyYW0ge0Jvb2xlYW59IFtvcHRpb25zLnJlYWRPbmx5PWZhbHNlXSBTcGVjaWZpZXMgd2hldGhlciBgZGF0YWAgY2FuIGJlXG4gICAqICAgICBtb2RpZmllZFxuICAgKiBAcGFyYW0ge0Jvb2xlYW59IFtvcHRpb25zLnJzdjE9ZmFsc2VdIFNwZWNpZmllcyB3aGV0aGVyIG9yIG5vdCB0byBzZXQgdGhlXG4gICAqICAgICBSU1YxIGJpdFxuICAgKiBAcmV0dXJuIHsoQnVmZmVyfFN0cmluZylbXX0gVGhlIGZyYW1lZCBkYXRhXG4gICAqIEBwdWJsaWNcbiAgICovXG4gIHN0YXRpYyBmcmFtZShkYXRhLCBvcHRpb25zKSB7XG4gICAgbGV0IG1hc2s7XG4gICAgbGV0IG1lcmdlID0gZmFsc2U7XG4gICAgbGV0IG9mZnNldCA9IDI7XG4gICAgbGV0IHNraXBNYXNraW5nID0gZmFsc2U7XG5cbiAgICBpZiAob3B0aW9ucy5tYXNrKSB7XG4gICAgICBtYXNrID0gb3B0aW9ucy5tYXNrQnVmZmVyIHx8IG1hc2tCdWZmZXI7XG5cbiAgICAgIGlmIChvcHRpb25zLmdlbmVyYXRlTWFzaykge1xuICAgICAgICBvcHRpb25zLmdlbmVyYXRlTWFzayhtYXNrKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGlmIChyYW5kb21Qb29sUG9pbnRlciA9PT0gUkFORE9NX1BPT0xfU0laRSkge1xuICAgICAgICAgIC8qIGlzdGFuYnVsIGlnbm9yZSBlbHNlICAqL1xuICAgICAgICAgIGlmIChyYW5kb21Qb29sID09PSB1bmRlZmluZWQpIHtcbiAgICAgICAgICAgIC8vXG4gICAgICAgICAgICAvLyBUaGlzIGlzIGxhemlseSBpbml0aWFsaXplZCBiZWNhdXNlIHNlcnZlci1zZW50IGZyYW1lcyBtdXN0IG5vdFxuICAgICAgICAgICAgLy8gYmUgbWFza2VkIHNvIGl0IG1heSBuZXZlciBiZSB1c2VkLlxuICAgICAgICAgICAgLy9cbiAgICAgICAgICAgIHJhbmRvbVBvb2wgPSBCdWZmZXIuYWxsb2MoUkFORE9NX1BPT0xfU0laRSk7XG4gICAgICAgICAgfVxuXG4gICAgICAgICAgcmFuZG9tRmlsbFN5bmMocmFuZG9tUG9vbCwgMCwgUkFORE9NX1BPT0xfU0laRSk7XG4gICAgICAgICAgcmFuZG9tUG9vbFBvaW50ZXIgPSAwO1xuICAgICAgICB9XG5cbiAgICAgICAgbWFza1swXSA9IHJhbmRvbVBvb2xbcmFuZG9tUG9vbFBvaW50ZXIrK107XG4gICAgICAgIG1hc2tbMV0gPSByYW5kb21Qb29sW3JhbmRvbVBvb2xQb2ludGVyKytdO1xuICAgICAgICBtYXNrWzJdID0gcmFuZG9tUG9vbFtyYW5kb21Qb29sUG9pbnRlcisrXTtcbiAgICAgICAgbWFza1szXSA9IHJhbmRvbVBvb2xbcmFuZG9tUG9vbFBvaW50ZXIrK107XG4gICAgICB9XG5cbiAgICAgIHNraXBNYXNraW5nID0gKG1hc2tbMF0gfCBtYXNrWzFdIHwgbWFza1syXSB8IG1hc2tbM10pID09PSAwO1xuICAgICAgb2Zmc2V0ID0gNjtcbiAgICB9XG5cbiAgICBsZXQgZGF0YUxlbmd0aDtcblxuICAgIGlmICh0eXBlb2YgZGF0YSA9PT0gJ3N0cmluZycpIHtcbiAgICAgIGlmIChcbiAgICAgICAgKCFvcHRpb25zLm1hc2sgfHwgc2tpcE1hc2tpbmcpICYmXG4gICAgICAgIG9wdGlvbnNba0J5dGVMZW5ndGhdICE9PSB1bmRlZmluZWRcbiAgICAgICkge1xuICAgICAgICBkYXRhTGVuZ3RoID0gb3B0aW9uc1trQnl0ZUxlbmd0aF07XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBkYXRhID0gQnVmZmVyLmZyb20oZGF0YSk7XG4gICAgICAgIGRhdGFMZW5ndGggPSBkYXRhLmxlbmd0aDtcbiAgICAgIH1cbiAgICB9IGVsc2Uge1xuICAgICAgZGF0YUxlbmd0aCA9IGRhdGEubGVuZ3RoO1xuICAgICAgbWVyZ2UgPSBvcHRpb25zLm1hc2sgJiYgb3B0aW9ucy5yZWFkT25seSAmJiAhc2tpcE1hc2tpbmc7XG4gICAgfVxuXG4gICAgbGV0IHBheWxvYWRMZW5ndGggPSBkYXRhTGVuZ3RoO1xuXG4gICAgaWYgKGRhdGFMZW5ndGggPj0gNjU1MzYpIHtcbiAgICAgIG9mZnNldCArPSA4O1xuICAgICAgcGF5bG9hZExlbmd0aCA9IDEyNztcbiAgICB9IGVsc2UgaWYgKGRhdGFMZW5ndGggPiAxMjUpIHtcbiAgICAgIG9mZnNldCArPSAyO1xuICAgICAgcGF5bG9hZExlbmd0aCA9IDEyNjtcbiAgICB9XG5cbiAgICBjb25zdCB0YXJnZXQgPSBCdWZmZXIuYWxsb2NVbnNhZmUobWVyZ2UgPyBkYXRhTGVuZ3RoICsgb2Zmc2V0IDogb2Zmc2V0KTtcblxuICAgIHRhcmdldFswXSA9IG9wdGlvbnMuZmluID8gb3B0aW9ucy5vcGNvZGUgfCAweDgwIDogb3B0aW9ucy5vcGNvZGU7XG4gICAgaWYgKG9wdGlvbnMucnN2MSkgdGFyZ2V0WzBdIHw9IDB4NDA7XG5cbiAgICB0YXJnZXRbMV0gPSBwYXlsb2FkTGVuZ3RoO1xuXG4gICAgaWYgKHBheWxvYWRMZW5ndGggPT09IDEyNikge1xuICAgICAgdGFyZ2V0LndyaXRlVUludDE2QkUoZGF0YUxlbmd0aCwgMik7XG4gICAgfSBlbHNlIGlmIChwYXlsb2FkTGVuZ3RoID09PSAxMjcpIHtcbiAgICAgIHRhcmdldFsyXSA9IHRhcmdldFszXSA9IDA7XG4gICAgICB0YXJnZXQud3JpdGVVSW50QkUoZGF0YUxlbmd0aCwgNCwgNik7XG4gICAgfVxuXG4gICAgaWYgKCFvcHRpb25zLm1hc2spIHJldHVybiBbdGFyZ2V0LCBkYXRhXTtcblxuICAgIHRhcmdldFsxXSB8PSAweDgwO1xuICAgIHRhcmdldFtvZmZzZXQgLSA0XSA9IG1hc2tbMF07XG4gICAgdGFyZ2V0W29mZnNldCAtIDNdID0gbWFza1sxXTtcbiAgICB0YXJnZXRbb2Zmc2V0IC0gMl0gPSBtYXNrWzJdO1xuICAgIHRhcmdldFtvZmZzZXQgLSAxXSA9IG1hc2tbM107XG5cbiAgICBpZiAoc2tpcE1hc2tpbmcpIHJldHVybiBbdGFyZ2V0LCBkYXRhXTtcblxuICAgIGlmIChtZXJnZSkge1xuICAgICAgYXBwbHlNYXNrKGRhdGEsIG1hc2ssIHRhcmdldCwgb2Zmc2V0LCBkYXRhTGVuZ3RoKTtcbiAgICAgIHJldHVybiBbdGFyZ2V0XTtcbiAgICB9XG5cbiAgICBhcHBseU1hc2soZGF0YSwgbWFzaywgZGF0YSwgMCwgZGF0YUxlbmd0aCk7XG4gICAgcmV0dXJuIFt0YXJnZXQsIGRhdGFdO1xuICB9XG5cbiAgLyoqXG4gICAqIFNlbmRzIGEgY2xvc2UgbWVzc2FnZSB0byB0aGUgb3RoZXIgcGVlci5cbiAgICpcbiAgICogQHBhcmFtIHtOdW1iZXJ9IFtjb2RlXSBUaGUgc3RhdHVzIGNvZGUgY29tcG9uZW50IG9mIHRoZSBib2R5XG4gICAqIEBwYXJhbSB7KFN0cmluZ3xCdWZmZXIpfSBbZGF0YV0gVGhlIG1lc3NhZ2UgY29tcG9uZW50IG9mIHRoZSBib2R5XG4gICAqIEBwYXJhbSB7Qm9vbGVhbn0gW21hc2s9ZmFsc2VdIFNwZWNpZmllcyB3aGV0aGVyIG9yIG5vdCB0byBtYXNrIHRoZSBtZXNzYWdlXG4gICAqIEBwYXJhbSB7RnVuY3Rpb259IFtjYl0gQ2FsbGJhY2tcbiAgICogQHB1YmxpY1xuICAgKi9cbiAgY2xvc2UoY29kZSwgZGF0YSwgbWFzaywgY2IpIHtcbiAgICBsZXQgYnVmO1xuXG4gICAgaWYgKGNvZGUgPT09IHVuZGVmaW5lZCkge1xuICAgICAgYnVmID0gRU1QVFlfQlVGRkVSO1xuICAgIH0gZWxzZSBpZiAodHlwZW9mIGNvZGUgIT09ICdudW1iZXInIHx8ICFpc1ZhbGlkU3RhdHVzQ29kZShjb2RlKSkge1xuICAgICAgdGhyb3cgbmV3IFR5cGVFcnJvcignRmlyc3QgYXJndW1lbnQgbXVzdCBiZSBhIHZhbGlkIGVycm9yIGNvZGUgbnVtYmVyJyk7XG4gICAgfSBlbHNlIGlmIChkYXRhID09PSB1bmRlZmluZWQgfHwgIWRhdGEubGVuZ3RoKSB7XG4gICAgICBidWYgPSBCdWZmZXIuYWxsb2NVbnNhZmUoMik7XG4gICAgICBidWYud3JpdGVVSW50MTZCRShjb2RlLCAwKTtcbiAgICB9IGVsc2Uge1xuICAgICAgY29uc3QgbGVuZ3RoID0gQnVmZmVyLmJ5dGVMZW5ndGgoZGF0YSk7XG5cbiAgICAgIGlmIChsZW5ndGggPiAxMjMpIHtcbiAgICAgICAgdGhyb3cgbmV3IFJhbmdlRXJyb3IoJ1RoZSBtZXNzYWdlIG11c3Qgbm90IGJlIGdyZWF0ZXIgdGhhbiAxMjMgYnl0ZXMnKTtcbiAgICAgIH1cblxuICAgICAgYnVmID0gQnVmZmVyLmFsbG9jVW5zYWZlKDIgKyBsZW5ndGgpO1xuICAgICAgYnVmLndyaXRlVUludDE2QkUoY29kZSwgMCk7XG5cbiAgICAgIGlmICh0eXBlb2YgZGF0YSA9PT0gJ3N0cmluZycpIHtcbiAgICAgICAgYnVmLndyaXRlKGRhdGEsIDIpO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgYnVmLnNldChkYXRhLCAyKTtcbiAgICAgIH1cbiAgICB9XG5cbiAgICBjb25zdCBvcHRpb25zID0ge1xuICAgICAgW2tCeXRlTGVuZ3RoXTogYnVmLmxlbmd0aCxcbiAgICAgIGZpbjogdHJ1ZSxcbiAgICAgIGdlbmVyYXRlTWFzazogdGhpcy5fZ2VuZXJhdGVNYXNrLFxuICAgICAgbWFzayxcbiAgICAgIG1hc2tCdWZmZXI6IHRoaXMuX21hc2tCdWZmZXIsXG4gICAgICBvcGNvZGU6IDB4MDgsXG4gICAgICByZWFkT25seTogZmFsc2UsXG4gICAgICByc3YxOiBmYWxzZVxuICAgIH07XG5cbiAgICBpZiAodGhpcy5fc3RhdGUgIT09IERFRkFVTFQpIHtcbiAgICAgIHRoaXMuZW5xdWV1ZShbdGhpcy5kaXNwYXRjaCwgYnVmLCBmYWxzZSwgb3B0aW9ucywgY2JdKTtcbiAgICB9IGVsc2Uge1xuICAgICAgdGhpcy5zZW5kRnJhbWUoU2VuZGVyLmZyYW1lKGJ1Ziwgb3B0aW9ucyksIGNiKTtcbiAgICB9XG4gIH1cblxuICAvKipcbiAgICogU2VuZHMgYSBwaW5nIG1lc3NhZ2UgdG8gdGhlIG90aGVyIHBlZXIuXG4gICAqXG4gICAqIEBwYXJhbSB7Kn0gZGF0YSBUaGUgbWVzc2FnZSB0byBzZW5kXG4gICAqIEBwYXJhbSB7Qm9vbGVhbn0gW21hc2s9ZmFsc2VdIFNwZWNpZmllcyB3aGV0aGVyIG9yIG5vdCB0byBtYXNrIGBkYXRhYFxuICAgKiBAcGFyYW0ge0Z1bmN0aW9ufSBbY2JdIENhbGxiYWNrXG4gICAqIEBwdWJsaWNcbiAgICovXG4gIHBpbmcoZGF0YSwgbWFzaywgY2IpIHtcbiAgICBsZXQgYnl0ZUxlbmd0aDtcbiAgICBsZXQgcmVhZE9ubHk7XG5cbiAgICBpZiAodHlwZW9mIGRhdGEgPT09ICdzdHJpbmcnKSB7XG4gICAgICBieXRlTGVuZ3RoID0gQnVmZmVyLmJ5dGVMZW5ndGgoZGF0YSk7XG4gICAgICByZWFkT25seSA9IGZhbHNlO1xuICAgIH0gZWxzZSBpZiAoaXNCbG9iKGRhdGEpKSB7XG4gICAgICBieXRlTGVuZ3RoID0gZGF0YS5zaXplO1xuICAgICAgcmVhZE9ubHkgPSBmYWxzZTtcbiAgICB9IGVsc2Uge1xuICAgICAgZGF0YSA9IHRvQnVmZmVyKGRhdGEpO1xuICAgICAgYnl0ZUxlbmd0aCA9IGRhdGEubGVuZ3RoO1xuICAgICAgcmVhZE9ubHkgPSB0b0J1ZmZlci5yZWFkT25seTtcbiAgICB9XG5cbiAgICBpZiAoYnl0ZUxlbmd0aCA+IDEyNSkge1xuICAgICAgdGhyb3cgbmV3IFJhbmdlRXJyb3IoJ1RoZSBkYXRhIHNpemUgbXVzdCBub3QgYmUgZ3JlYXRlciB0aGFuIDEyNSBieXRlcycpO1xuICAgIH1cblxuICAgIGNvbnN0IG9wdGlvbnMgPSB7XG4gICAgICBba0J5dGVMZW5ndGhdOiBieXRlTGVuZ3RoLFxuICAgICAgZmluOiB0cnVlLFxuICAgICAgZ2VuZXJhdGVNYXNrOiB0aGlzLl9nZW5lcmF0ZU1hc2ssXG4gICAgICBtYXNrLFxuICAgICAgbWFza0J1ZmZlcjogdGhpcy5fbWFza0J1ZmZlcixcbiAgICAgIG9wY29kZTogMHgwOSxcbiAgICAgIHJlYWRPbmx5LFxuICAgICAgcnN2MTogZmFsc2VcbiAgICB9O1xuXG4gICAgaWYgKGlzQmxvYihkYXRhKSkge1xuICAgICAgaWYgKHRoaXMuX3N0YXRlICE9PSBERUZBVUxUKSB7XG4gICAgICAgIHRoaXMuZW5xdWV1ZShbdGhpcy5nZXRCbG9iRGF0YSwgZGF0YSwgZmFsc2UsIG9wdGlvbnMsIGNiXSk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICB0aGlzLmdldEJsb2JEYXRhKGRhdGEsIGZhbHNlLCBvcHRpb25zLCBjYik7XG4gICAgICB9XG4gICAgfSBlbHNlIGlmICh0aGlzLl9zdGF0ZSAhPT0gREVGQVVMVCkge1xuICAgICAgdGhpcy5lbnF1ZXVlKFt0aGlzLmRpc3BhdGNoLCBkYXRhLCBmYWxzZSwgb3B0aW9ucywgY2JdKTtcbiAgICB9IGVsc2Uge1xuICAgICAgdGhpcy5zZW5kRnJhbWUoU2VuZGVyLmZyYW1lKGRhdGEsIG9wdGlvbnMpLCBjYik7XG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIFNlbmRzIGEgcG9uZyBtZXNzYWdlIHRvIHRoZSBvdGhlciBwZWVyLlxuICAgKlxuICAgKiBAcGFyYW0geyp9IGRhdGEgVGhlIG1lc3NhZ2UgdG8gc2VuZFxuICAgKiBAcGFyYW0ge0Jvb2xlYW59IFttYXNrPWZhbHNlXSBTcGVjaWZpZXMgd2hldGhlciBvciBub3QgdG8gbWFzayBgZGF0YWBcbiAgICogQHBhcmFtIHtGdW5jdGlvbn0gW2NiXSBDYWxsYmFja1xuICAgKiBAcHVibGljXG4gICAqL1xuICBwb25nKGRhdGEsIG1hc2ssIGNiKSB7XG4gICAgbGV0IGJ5dGVMZW5ndGg7XG4gICAgbGV0IHJlYWRPbmx5O1xuXG4gICAgaWYgKHR5cGVvZiBkYXRhID09PSAnc3RyaW5nJykge1xuICAgICAgYnl0ZUxlbmd0aCA9IEJ1ZmZlci5ieXRlTGVuZ3RoKGRhdGEpO1xuICAgICAgcmVhZE9ubHkgPSBmYWxzZTtcbiAgICB9IGVsc2UgaWYgKGlzQmxvYihkYXRhKSkge1xuICAgICAgYnl0ZUxlbmd0aCA9IGRhdGEuc2l6ZTtcbiAgICAgIHJlYWRPbmx5ID0gZmFsc2U7XG4gICAgfSBlbHNlIHtcbiAgICAgIGRhdGEgPSB0b0J1ZmZlcihkYXRhKTtcbiAgICAgIGJ5dGVMZW5ndGggPSBkYXRhLmxlbmd0aDtcbiAgICAgIHJlYWRPbmx5ID0gdG9CdWZmZXIucmVhZE9ubHk7XG4gICAgfVxuXG4gICAgaWYgKGJ5dGVMZW5ndGggPiAxMjUpIHtcbiAgICAgIHRocm93IG5ldyBSYW5nZUVycm9yKCdUaGUgZGF0YSBzaXplIG11c3Qgbm90IGJlIGdyZWF0ZXIgdGhhbiAxMjUgYnl0ZXMnKTtcbiAgICB9XG5cbiAgICBjb25zdCBvcHRpb25zID0ge1xuICAgICAgW2tCeXRlTGVuZ3RoXTogYnl0ZUxlbmd0aCxcbiAgICAgIGZpbjogdHJ1ZSxcbiAgICAgIGdlbmVyYXRlTWFzazogdGhpcy5fZ2VuZXJhdGVNYXNrLFxuICAgICAgbWFzayxcbiAgICAgIG1hc2tCdWZmZXI6IHRoaXMuX21hc2tCdWZmZXIsXG4gICAgICBvcGNvZGU6IDB4MGEsXG4gICAgICByZWFkT25seSxcbiAgICAgIHJzdjE6IGZhbHNlXG4gICAgfTtcblxuICAgIGlmIChpc0Jsb2IoZGF0YSkpIHtcbiAgICAgIGlmICh0aGlzLl9zdGF0ZSAhPT0gREVGQVVMVCkge1xuICAgICAgICB0aGlzLmVucXVldWUoW3RoaXMuZ2V0QmxvYkRhdGEsIGRhdGEsIGZhbHNlLCBvcHRpb25zLCBjYl0pO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgdGhpcy5nZXRCbG9iRGF0YShkYXRhLCBmYWxzZSwgb3B0aW9ucywgY2IpO1xuICAgICAgfVxuICAgIH0gZWxzZSBpZiAodGhpcy5fc3RhdGUgIT09IERFRkFVTFQpIHtcbiAgICAgIHRoaXMuZW5xdWV1ZShbdGhpcy5kaXNwYXRjaCwgZGF0YSwgZmFsc2UsIG9wdGlvbnMsIGNiXSk7XG4gICAgfSBlbHNlIHtcbiAgICAgIHRoaXMuc2VuZEZyYW1lKFNlbmRlci5mcmFtZShkYXRhLCBvcHRpb25zKSwgY2IpO1xuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiBTZW5kcyBhIGRhdGEgbWVzc2FnZSB0byB0aGUgb3RoZXIgcGVlci5cbiAgICpcbiAgICogQHBhcmFtIHsqfSBkYXRhIFRoZSBtZXNzYWdlIHRvIHNlbmRcbiAgICogQHBhcmFtIHtPYmplY3R9IG9wdGlvbnMgT3B0aW9ucyBvYmplY3RcbiAgICogQHBhcmFtIHtCb29sZWFufSBbb3B0aW9ucy5iaW5hcnk9ZmFsc2VdIFNwZWNpZmllcyB3aGV0aGVyIGBkYXRhYCBpcyBiaW5hcnlcbiAgICogICAgIG9yIHRleHRcbiAgICogQHBhcmFtIHtCb29sZWFufSBbb3B0aW9ucy5jb21wcmVzcz1mYWxzZV0gU3BlY2lmaWVzIHdoZXRoZXIgb3Igbm90IHRvXG4gICAqICAgICBjb21wcmVzcyBgZGF0YWBcbiAgICogQHBhcmFtIHtCb29sZWFufSBbb3B0aW9ucy5maW49ZmFsc2VdIFNwZWNpZmllcyB3aGV0aGVyIHRoZSBmcmFnbWVudCBpcyB0aGVcbiAgICogICAgIGxhc3Qgb25lXG4gICAqIEBwYXJhbSB7Qm9vbGVhbn0gW29wdGlvbnMubWFzaz1mYWxzZV0gU3BlY2lmaWVzIHdoZXRoZXIgb3Igbm90IHRvIG1hc2tcbiAgICogICAgIGBkYXRhYFxuICAgKiBAcGFyYW0ge0Z1bmN0aW9ufSBbY2JdIENhbGxiYWNrXG4gICAqIEBwdWJsaWNcbiAgICovXG4gIHNlbmQoZGF0YSwgb3B0aW9ucywgY2IpIHtcbiAgICBjb25zdCBwZXJNZXNzYWdlRGVmbGF0ZSA9IHRoaXMuX2V4dGVuc2lvbnNbUGVyTWVzc2FnZURlZmxhdGUuZXh0ZW5zaW9uTmFtZV07XG4gICAgbGV0IG9wY29kZSA9IG9wdGlvbnMuYmluYXJ5ID8gMiA6IDE7XG4gICAgbGV0IHJzdjEgPSBvcHRpb25zLmNvbXByZXNzO1xuXG4gICAgbGV0IGJ5dGVMZW5ndGg7XG4gICAgbGV0IHJlYWRPbmx5O1xuXG4gICAgaWYgKHR5cGVvZiBkYXRhID09PSAnc3RyaW5nJykge1xuICAgICAgYnl0ZUxlbmd0aCA9IEJ1ZmZlci5ieXRlTGVuZ3RoKGRhdGEpO1xuICAgICAgcmVhZE9ubHkgPSBmYWxzZTtcbiAgICB9IGVsc2UgaWYgKGlzQmxvYihkYXRhKSkge1xuICAgICAgYnl0ZUxlbmd0aCA9IGRhdGEuc2l6ZTtcbiAgICAgIHJlYWRPbmx5ID0gZmFsc2U7XG4gICAgfSBlbHNlIHtcbiAgICAgIGRhdGEgPSB0b0J1ZmZlcihkYXRhKTtcbiAgICAgIGJ5dGVMZW5ndGggPSBkYXRhLmxlbmd0aDtcbiAgICAgIHJlYWRPbmx5ID0gdG9CdWZmZXIucmVhZE9ubHk7XG4gICAgfVxuXG4gICAgaWYgKHRoaXMuX2ZpcnN0RnJhZ21lbnQpIHtcbiAgICAgIHRoaXMuX2ZpcnN0RnJhZ21lbnQgPSBmYWxzZTtcbiAgICAgIGlmIChcbiAgICAgICAgcnN2MSAmJlxuICAgICAgICBwZXJNZXNzYWdlRGVmbGF0ZSAmJlxuICAgICAgICBwZXJNZXNzYWdlRGVmbGF0ZS5wYXJhbXNbXG4gICAgICAgICAgcGVyTWVzc2FnZURlZmxhdGUuX2lzU2VydmVyXG4gICAgICAgICAgICA/ICdzZXJ2ZXJfbm9fY29udGV4dF90YWtlb3ZlcidcbiAgICAgICAgICAgIDogJ2NsaWVudF9ub19jb250ZXh0X3Rha2VvdmVyJ1xuICAgICAgICBdXG4gICAgICApIHtcbiAgICAgICAgcnN2MSA9IGJ5dGVMZW5ndGggPj0gcGVyTWVzc2FnZURlZmxhdGUuX3RocmVzaG9sZDtcbiAgICAgIH1cbiAgICAgIHRoaXMuX2NvbXByZXNzID0gcnN2MTtcbiAgICB9IGVsc2Uge1xuICAgICAgcnN2MSA9IGZhbHNlO1xuICAgICAgb3Bjb2RlID0gMDtcbiAgICB9XG5cbiAgICBpZiAob3B0aW9ucy5maW4pIHRoaXMuX2ZpcnN0RnJhZ21lbnQgPSB0cnVlO1xuXG4gICAgY29uc3Qgb3B0cyA9IHtcbiAgICAgIFtrQnl0ZUxlbmd0aF06IGJ5dGVMZW5ndGgsXG4gICAgICBmaW46IG9wdGlvbnMuZmluLFxuICAgICAgZ2VuZXJhdGVNYXNrOiB0aGlzLl9nZW5lcmF0ZU1hc2ssXG4gICAgICBtYXNrOiBvcHRpb25zLm1hc2ssXG4gICAgICBtYXNrQnVmZmVyOiB0aGlzLl9tYXNrQnVmZmVyLFxuICAgICAgb3Bjb2RlLFxuICAgICAgcmVhZE9ubHksXG4gICAgICByc3YxXG4gICAgfTtcblxuICAgIGlmIChpc0Jsb2IoZGF0YSkpIHtcbiAgICAgIGlmICh0aGlzLl9zdGF0ZSAhPT0gREVGQVVMVCkge1xuICAgICAgICB0aGlzLmVucXVldWUoW3RoaXMuZ2V0QmxvYkRhdGEsIGRhdGEsIHRoaXMuX2NvbXByZXNzLCBvcHRzLCBjYl0pO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgdGhpcy5nZXRCbG9iRGF0YShkYXRhLCB0aGlzLl9jb21wcmVzcywgb3B0cywgY2IpO1xuICAgICAgfVxuICAgIH0gZWxzZSBpZiAodGhpcy5fc3RhdGUgIT09IERFRkFVTFQpIHtcbiAgICAgIHRoaXMuZW5xdWV1ZShbdGhpcy5kaXNwYXRjaCwgZGF0YSwgdGhpcy5fY29tcHJlc3MsIG9wdHMsIGNiXSk7XG4gICAgfSBlbHNlIHtcbiAgICAgIHRoaXMuZGlzcGF0Y2goZGF0YSwgdGhpcy5fY29tcHJlc3MsIG9wdHMsIGNiKTtcbiAgICB9XG4gIH1cblxuICAvKipcbiAgICogR2V0cyB0aGUgY29udGVudHMgb2YgYSBibG9iIGFzIGJpbmFyeSBkYXRhLlxuICAgKlxuICAgKiBAcGFyYW0ge0Jsb2J9IGJsb2IgVGhlIGJsb2JcbiAgICogQHBhcmFtIHtCb29sZWFufSBbY29tcHJlc3M9ZmFsc2VdIFNwZWNpZmllcyB3aGV0aGVyIG9yIG5vdCB0byBjb21wcmVzc1xuICAgKiAgICAgdGhlIGRhdGFcbiAgICogQHBhcmFtIHtPYmplY3R9IG9wdGlvbnMgT3B0aW9ucyBvYmplY3RcbiAgICogQHBhcmFtIHtCb29sZWFufSBbb3B0aW9ucy5maW49ZmFsc2VdIFNwZWNpZmllcyB3aGV0aGVyIG9yIG5vdCB0byBzZXQgdGhlXG4gICAqICAgICBGSU4gYml0XG4gICAqIEBwYXJhbSB7RnVuY3Rpb259IFtvcHRpb25zLmdlbmVyYXRlTWFza10gVGhlIGZ1bmN0aW9uIHVzZWQgdG8gZ2VuZXJhdGUgdGhlXG4gICAqICAgICBtYXNraW5nIGtleVxuICAgKiBAcGFyYW0ge0Jvb2xlYW59IFtvcHRpb25zLm1hc2s9ZmFsc2VdIFNwZWNpZmllcyB3aGV0aGVyIG9yIG5vdCB0byBtYXNrXG4gICAqICAgICBgZGF0YWBcbiAgICogQHBhcmFtIHtCdWZmZXJ9IFtvcHRpb25zLm1hc2tCdWZmZXJdIFRoZSBidWZmZXIgdXNlZCB0byBzdG9yZSB0aGUgbWFza2luZ1xuICAgKiAgICAga2V5XG4gICAqIEBwYXJhbSB7TnVtYmVyfSBvcHRpb25zLm9wY29kZSBUaGUgb3Bjb2RlXG4gICAqIEBwYXJhbSB7Qm9vbGVhbn0gW29wdGlvbnMucmVhZE9ubHk9ZmFsc2VdIFNwZWNpZmllcyB3aGV0aGVyIGBkYXRhYCBjYW4gYmVcbiAgICogICAgIG1vZGlmaWVkXG4gICAqIEBwYXJhbSB7Qm9vbGVhbn0gW29wdGlvbnMucnN2MT1mYWxzZV0gU3BlY2lmaWVzIHdoZXRoZXIgb3Igbm90IHRvIHNldCB0aGVcbiAgICogICAgIFJTVjEgYml0XG4gICAqIEBwYXJhbSB7RnVuY3Rpb259IFtjYl0gQ2FsbGJhY2tcbiAgICogQHByaXZhdGVcbiAgICovXG4gIGdldEJsb2JEYXRhKGJsb2IsIGNvbXByZXNzLCBvcHRpb25zLCBjYikge1xuICAgIHRoaXMuX2J1ZmZlcmVkQnl0ZXMgKz0gb3B0aW9uc1trQnl0ZUxlbmd0aF07XG4gICAgdGhpcy5fc3RhdGUgPSBHRVRfQkxPQl9EQVRBO1xuXG4gICAgYmxvYlxuICAgICAgLmFycmF5QnVmZmVyKClcbiAgICAgIC50aGVuKChhcnJheUJ1ZmZlcikgPT4ge1xuICAgICAgICBpZiAodGhpcy5fc29ja2V0LmRlc3Ryb3llZCkge1xuICAgICAgICAgIGNvbnN0IGVyciA9IG5ldyBFcnJvcihcbiAgICAgICAgICAgICdUaGUgc29ja2V0IHdhcyBjbG9zZWQgd2hpbGUgdGhlIGJsb2Igd2FzIGJlaW5nIHJlYWQnXG4gICAgICAgICAgKTtcblxuICAgICAgICAgIC8vXG4gICAgICAgICAgLy8gYGNhbGxDYWxsYmFja3NgIGlzIGNhbGxlZCBpbiB0aGUgbmV4dCB0aWNrIHRvIGVuc3VyZSB0aGF0IGVycm9yc1xuICAgICAgICAgIC8vIHRoYXQgbWlnaHQgYmUgdGhyb3duIGluIHRoZSBjYWxsYmFja3MgYmVoYXZlIGxpa2UgZXJyb3JzIHRocm93blxuICAgICAgICAgIC8vIG91dHNpZGUgdGhlIHByb21pc2UgY2hhaW4uXG4gICAgICAgICAgLy9cbiAgICAgICAgICBwcm9jZXNzLm5leHRUaWNrKGNhbGxDYWxsYmFja3MsIHRoaXMsIGVyciwgY2IpO1xuICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuXG4gICAgICAgIHRoaXMuX2J1ZmZlcmVkQnl0ZXMgLT0gb3B0aW9uc1trQnl0ZUxlbmd0aF07XG4gICAgICAgIGNvbnN0IGRhdGEgPSB0b0J1ZmZlcihhcnJheUJ1ZmZlcik7XG5cbiAgICAgICAgaWYgKCFjb21wcmVzcykge1xuICAgICAgICAgIHRoaXMuX3N0YXRlID0gREVGQVVMVDtcbiAgICAgICAgICB0aGlzLnNlbmRGcmFtZShTZW5kZXIuZnJhbWUoZGF0YSwgb3B0aW9ucyksIGNiKTtcbiAgICAgICAgICB0aGlzLmRlcXVldWUoKTtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICB0aGlzLmRpc3BhdGNoKGRhdGEsIGNvbXByZXNzLCBvcHRpb25zLCBjYik7XG4gICAgICAgIH1cbiAgICAgIH0pXG4gICAgICAuY2F0Y2goKGVycikgPT4ge1xuICAgICAgICAvL1xuICAgICAgICAvLyBgb25FcnJvcmAgaXMgY2FsbGVkIGluIHRoZSBuZXh0IHRpY2sgZm9yIHRoZSBzYW1lIHJlYXNvbiB0aGF0XG4gICAgICAgIC8vIGBjYWxsQ2FsbGJhY2tzYCBhYm92ZSBpcy5cbiAgICAgICAgLy9cbiAgICAgICAgcHJvY2Vzcy5uZXh0VGljayhvbkVycm9yLCB0aGlzLCBlcnIsIGNiKTtcbiAgICAgIH0pO1xuICB9XG5cbiAgLyoqXG4gICAqIERpc3BhdGNoZXMgYSBtZXNzYWdlLlxuICAgKlxuICAgKiBAcGFyYW0geyhCdWZmZXJ8U3RyaW5nKX0gZGF0YSBUaGUgbWVzc2FnZSB0byBzZW5kXG4gICAqIEBwYXJhbSB7Qm9vbGVhbn0gW2NvbXByZXNzPWZhbHNlXSBTcGVjaWZpZXMgd2hldGhlciBvciBub3QgdG8gY29tcHJlc3NcbiAgICogICAgIGBkYXRhYFxuICAgKiBAcGFyYW0ge09iamVjdH0gb3B0aW9ucyBPcHRpb25zIG9iamVjdFxuICAgKiBAcGFyYW0ge0Jvb2xlYW59IFtvcHRpb25zLmZpbj1mYWxzZV0gU3BlY2lmaWVzIHdoZXRoZXIgb3Igbm90IHRvIHNldCB0aGVcbiAgICogICAgIEZJTiBiaXRcbiAgICogQHBhcmFtIHtGdW5jdGlvbn0gW29wdGlvbnMuZ2VuZXJhdGVNYXNrXSBUaGUgZnVuY3Rpb24gdXNlZCB0byBnZW5lcmF0ZSB0aGVcbiAgICogICAgIG1hc2tpbmcga2V5XG4gICAqIEBwYXJhbSB7Qm9vbGVhbn0gW29wdGlvbnMubWFzaz1mYWxzZV0gU3BlY2lmaWVzIHdoZXRoZXIgb3Igbm90IHRvIG1hc2tcbiAgICogICAgIGBkYXRhYFxuICAgKiBAcGFyYW0ge0J1ZmZlcn0gW29wdGlvbnMubWFza0J1ZmZlcl0gVGhlIGJ1ZmZlciB1c2VkIHRvIHN0b3JlIHRoZSBtYXNraW5nXG4gICAqICAgICBrZXlcbiAgICogQHBhcmFtIHtOdW1iZXJ9IG9wdGlvbnMub3Bjb2RlIFRoZSBvcGNvZGVcbiAgICogQHBhcmFtIHtCb29sZWFufSBbb3B0aW9ucy5yZWFkT25seT1mYWxzZV0gU3BlY2lmaWVzIHdoZXRoZXIgYGRhdGFgIGNhbiBiZVxuICAgKiAgICAgbW9kaWZpZWRcbiAgICogQHBhcmFtIHtCb29sZWFufSBbb3B0aW9ucy5yc3YxPWZhbHNlXSBTcGVjaWZpZXMgd2hldGhlciBvciBub3QgdG8gc2V0IHRoZVxuICAgKiAgICAgUlNWMSBiaXRcbiAgICogQHBhcmFtIHtGdW5jdGlvbn0gW2NiXSBDYWxsYmFja1xuICAgKiBAcHJpdmF0ZVxuICAgKi9cbiAgZGlzcGF0Y2goZGF0YSwgY29tcHJlc3MsIG9wdGlvbnMsIGNiKSB7XG4gICAgaWYgKCFjb21wcmVzcykge1xuICAgICAgdGhpcy5zZW5kRnJhbWUoU2VuZGVyLmZyYW1lKGRhdGEsIG9wdGlvbnMpLCBjYik7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgY29uc3QgcGVyTWVzc2FnZURlZmxhdGUgPSB0aGlzLl9leHRlbnNpb25zW1Blck1lc3NhZ2VEZWZsYXRlLmV4dGVuc2lvbk5hbWVdO1xuXG4gICAgdGhpcy5fYnVmZmVyZWRCeXRlcyArPSBvcHRpb25zW2tCeXRlTGVuZ3RoXTtcbiAgICB0aGlzLl9zdGF0ZSA9IERFRkxBVElORztcbiAgICBwZXJNZXNzYWdlRGVmbGF0ZS5jb21wcmVzcyhkYXRhLCBvcHRpb25zLmZpbiwgKF8sIGJ1ZikgPT4ge1xuICAgICAgaWYgKHRoaXMuX3NvY2tldC5kZXN0cm95ZWQpIHtcbiAgICAgICAgY29uc3QgZXJyID0gbmV3IEVycm9yKFxuICAgICAgICAgICdUaGUgc29ja2V0IHdhcyBjbG9zZWQgd2hpbGUgZGF0YSB3YXMgYmVpbmcgY29tcHJlc3NlZCdcbiAgICAgICAgKTtcblxuICAgICAgICBjYWxsQ2FsbGJhY2tzKHRoaXMsIGVyciwgY2IpO1xuICAgICAgICByZXR1cm47XG4gICAgICB9XG5cbiAgICAgIHRoaXMuX2J1ZmZlcmVkQnl0ZXMgLT0gb3B0aW9uc1trQnl0ZUxlbmd0aF07XG4gICAgICB0aGlzLl9zdGF0ZSA9IERFRkFVTFQ7XG4gICAgICBvcHRpb25zLnJlYWRPbmx5ID0gZmFsc2U7XG4gICAgICB0aGlzLnNlbmRGcmFtZShTZW5kZXIuZnJhbWUoYnVmLCBvcHRpb25zKSwgY2IpO1xuICAgICAgdGhpcy5kZXF1ZXVlKCk7XG4gICAgfSk7XG4gIH1cblxuICAvKipcbiAgICogRXhlY3V0ZXMgcXVldWVkIHNlbmQgb3BlcmF0aW9ucy5cbiAgICpcbiAgICogQHByaXZhdGVcbiAgICovXG4gIGRlcXVldWUoKSB7XG4gICAgd2hpbGUgKHRoaXMuX3N0YXRlID09PSBERUZBVUxUICYmIHRoaXMuX3F1ZXVlLmxlbmd0aCkge1xuICAgICAgY29uc3QgcGFyYW1zID0gdGhpcy5fcXVldWUuc2hpZnQoKTtcblxuICAgICAgdGhpcy5fYnVmZmVyZWRCeXRlcyAtPSBwYXJhbXNbM11ba0J5dGVMZW5ndGhdO1xuICAgICAgUmVmbGVjdC5hcHBseShwYXJhbXNbMF0sIHRoaXMsIHBhcmFtcy5zbGljZSgxKSk7XG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIEVucXVldWVzIGEgc2VuZCBvcGVyYXRpb24uXG4gICAqXG4gICAqIEBwYXJhbSB7QXJyYXl9IHBhcmFtcyBTZW5kIG9wZXJhdGlvbiBwYXJhbWV0ZXJzLlxuICAgKiBAcHJpdmF0ZVxuICAgKi9cbiAgZW5xdWV1ZShwYXJhbXMpIHtcbiAgICB0aGlzLl9idWZmZXJlZEJ5dGVzICs9IHBhcmFtc1szXVtrQnl0ZUxlbmd0aF07XG4gICAgdGhpcy5fcXVldWUucHVzaChwYXJhbXMpO1xuICB9XG5cbiAgLyoqXG4gICAqIFNlbmRzIGEgZnJhbWUuXG4gICAqXG4gICAqIEBwYXJhbSB7KEJ1ZmZlciB8IFN0cmluZylbXX0gbGlzdCBUaGUgZnJhbWUgdG8gc2VuZFxuICAgKiBAcGFyYW0ge0Z1bmN0aW9ufSBbY2JdIENhbGxiYWNrXG4gICAqIEBwcml2YXRlXG4gICAqL1xuICBzZW5kRnJhbWUobGlzdCwgY2IpIHtcbiAgICBpZiAobGlzdC5sZW5ndGggPT09IDIpIHtcbiAgICAgIHRoaXMuX3NvY2tldC5jb3JrKCk7XG4gICAgICB0aGlzLl9zb2NrZXQud3JpdGUobGlzdFswXSk7XG4gICAgICB0aGlzLl9zb2NrZXQud3JpdGUobGlzdFsxXSwgY2IpO1xuICAgICAgdGhpcy5fc29ja2V0LnVuY29yaygpO1xuICAgIH0gZWxzZSB7XG4gICAgICB0aGlzLl9zb2NrZXQud3JpdGUobGlzdFswXSwgY2IpO1xuICAgIH1cbiAgfVxufVxuXG5tb2R1bGUuZXhwb3J0cyA9IFNlbmRlcjtcblxuLyoqXG4gKiBDYWxscyBxdWV1ZWQgY2FsbGJhY2tzIHdpdGggYW4gZXJyb3IuXG4gKlxuICogQHBhcmFtIHtTZW5kZXJ9IHNlbmRlciBUaGUgYFNlbmRlcmAgaW5zdGFuY2VcbiAqIEBwYXJhbSB7RXJyb3J9IGVyciBUaGUgZXJyb3IgdG8gY2FsbCB0aGUgY2FsbGJhY2tzIHdpdGhcbiAqIEBwYXJhbSB7RnVuY3Rpb259IFtjYl0gVGhlIGZpcnN0IGNhbGxiYWNrXG4gKiBAcHJpdmF0ZVxuICovXG5mdW5jdGlvbiBjYWxsQ2FsbGJhY2tzKHNlbmRlciwgZXJyLCBjYikge1xuICBpZiAodHlwZW9mIGNiID09PSAnZnVuY3Rpb24nKSBjYihlcnIpO1xuXG4gIGZvciAobGV0IGkgPSAwOyBpIDwgc2VuZGVyLl9xdWV1ZS5sZW5ndGg7IGkrKykge1xuICAgIGNvbnN0IHBhcmFtcyA9IHNlbmRlci5fcXVldWVbaV07XG4gICAgY29uc3QgY2FsbGJhY2sgPSBwYXJhbXNbcGFyYW1zLmxlbmd0aCAtIDFdO1xuXG4gICAgaWYgKHR5cGVvZiBjYWxsYmFjayA9PT0gJ2Z1bmN0aW9uJykgY2FsbGJhY2soZXJyKTtcbiAgfVxufVxuXG4vKipcbiAqIEhhbmRsZXMgYSBgU2VuZGVyYCBlcnJvci5cbiAqXG4gKiBAcGFyYW0ge1NlbmRlcn0gc2VuZGVyIFRoZSBgU2VuZGVyYCBpbnN0YW5jZVxuICogQHBhcmFtIHtFcnJvcn0gZXJyIFRoZSBlcnJvclxuICogQHBhcmFtIHtGdW5jdGlvbn0gW2NiXSBUaGUgZmlyc3QgcGVuZGluZyBjYWxsYmFja1xuICogQHByaXZhdGVcbiAqL1xuZnVuY3Rpb24gb25FcnJvcihzZW5kZXIsIGVyciwgY2IpIHtcbiAgY2FsbENhbGxiYWNrcyhzZW5kZXIsIGVyciwgY2IpO1xuICBzZW5kZXIub25lcnJvcihlcnIpO1xufVxuIl0sIm5hbWVzIjpbIkR1cGxleCIsInJlcXVpcmUiLCJyYW5kb21GaWxsU3luYyIsIlBlck1lc3NhZ2VEZWZsYXRlIiwiRU1QVFlfQlVGRkVSIiwia1dlYlNvY2tldCIsIk5PT1AiLCJpc0Jsb2IiLCJpc1ZhbGlkU3RhdHVzQ29kZSIsIm1hc2siLCJhcHBseU1hc2siLCJ0b0J1ZmZlciIsImtCeXRlTGVuZ3RoIiwiU3ltYm9sIiwibWFza0J1ZmZlciIsIkJ1ZmZlciIsImFsbG9jIiwiUkFORE9NX1BPT0xfU0laRSIsInJhbmRvbVBvb2wiLCJyYW5kb21Qb29sUG9pbnRlciIsIkRFRkFVTFQiLCJERUZMQVRJTkciLCJHRVRfQkxPQl9EQVRBIiwiU2VuZGVyIiwiY29uc3RydWN0b3IiLCJzb2NrZXQiLCJleHRlbnNpb25zIiwiZ2VuZXJhdGVNYXNrIiwiX2V4dGVuc2lvbnMiLCJfZ2VuZXJhdGVNYXNrIiwiX21hc2tCdWZmZXIiLCJfc29ja2V0IiwiX2ZpcnN0RnJhZ21lbnQiLCJfY29tcHJlc3MiLCJfYnVmZmVyZWRCeXRlcyIsIl9xdWV1ZSIsIl9zdGF0ZSIsIm9uZXJyb3IiLCJ1bmRlZmluZWQiLCJmcmFtZSIsImRhdGEiLCJvcHRpb25zIiwibWVyZ2UiLCJvZmZzZXQiLCJza2lwTWFza2luZyIsImRhdGFMZW5ndGgiLCJmcm9tIiwibGVuZ3RoIiwicmVhZE9ubHkiLCJwYXlsb2FkTGVuZ3RoIiwidGFyZ2V0IiwiYWxsb2NVbnNhZmUiLCJmaW4iLCJvcGNvZGUiLCJyc3YxIiwid3JpdGVVSW50MTZCRSIsIndyaXRlVUludEJFIiwiY2xvc2UiLCJjb2RlIiwiY2IiLCJidWYiLCJUeXBlRXJyb3IiLCJieXRlTGVuZ3RoIiwiUmFuZ2VFcnJvciIsIndyaXRlIiwic2V0IiwiZW5xdWV1ZSIsImRpc3BhdGNoIiwic2VuZEZyYW1lIiwicGluZyIsInNpemUiLCJnZXRCbG9iRGF0YSIsInBvbmciLCJzZW5kIiwicGVyTWVzc2FnZURlZmxhdGUiLCJleHRlbnNpb25OYW1lIiwiYmluYXJ5IiwiY29tcHJlc3MiLCJwYXJhbXMiLCJfaXNTZXJ2ZXIiLCJfdGhyZXNob2xkIiwib3B0cyIsImJsb2IiLCJhcnJheUJ1ZmZlciIsInRoZW4iLCJkZXN0cm95ZWQiLCJlcnIiLCJFcnJvciIsInByb2Nlc3MiLCJuZXh0VGljayIsImNhbGxDYWxsYmFja3MiLCJkZXF1ZXVlIiwiY2F0Y2giLCJvbkVycm9yIiwiXyIsInNoaWZ0IiwiUmVmbGVjdCIsImFwcGx5Iiwic2xpY2UiLCJwdXNoIiwibGlzdCIsImNvcmsiLCJ1bmNvcmsiLCJtb2R1bGUiLCJleHBvcnRzIiwic2VuZGVyIiwiaSIsImNhbGxiYWNrIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ws/lib/sender.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ws/lib/stream.js":
/*!***************************************!*\
  !*** ./node_modules/ws/lib/stream.js ***!
  \***************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/* eslint no-unused-vars: [\"error\", { \"varsIgnorePattern\": \"^WebSocket$\" }] */ \nconst WebSocket = __webpack_require__(/*! ./websocket */ \"(ssr)/./node_modules/ws/lib/websocket.js\");\nconst { Duplex } = __webpack_require__(/*! stream */ \"stream\");\n/**\n * Emits the `'close'` event on a stream.\n *\n * @param {Duplex} stream The stream.\n * @private\n */ function emitClose(stream) {\n    stream.emit(\"close\");\n}\n/**\n * The listener of the `'end'` event.\n *\n * @private\n */ function duplexOnEnd() {\n    if (!this.destroyed && this._writableState.finished) {\n        this.destroy();\n    }\n}\n/**\n * The listener of the `'error'` event.\n *\n * @param {Error} err The error\n * @private\n */ function duplexOnError(err) {\n    this.removeListener(\"error\", duplexOnError);\n    this.destroy();\n    if (this.listenerCount(\"error\") === 0) {\n        // Do not suppress the throwing behavior.\n        this.emit(\"error\", err);\n    }\n}\n/**\n * Wraps a `WebSocket` in a duplex stream.\n *\n * @param {WebSocket} ws The `WebSocket` to wrap\n * @param {Object} [options] The options for the `Duplex` constructor\n * @return {Duplex} The duplex stream\n * @public\n */ function createWebSocketStream(ws, options) {\n    let terminateOnDestroy = true;\n    const duplex = new Duplex({\n        ...options,\n        autoDestroy: false,\n        emitClose: false,\n        objectMode: false,\n        writableObjectMode: false\n    });\n    ws.on(\"message\", function message(msg, isBinary) {\n        const data = !isBinary && duplex._readableState.objectMode ? msg.toString() : msg;\n        if (!duplex.push(data)) ws.pause();\n    });\n    ws.once(\"error\", function error(err) {\n        if (duplex.destroyed) return;\n        // Prevent `ws.terminate()` from being called by `duplex._destroy()`.\n        //\n        // - If the `'error'` event is emitted before the `'open'` event, then\n        //   `ws.terminate()` is a noop as no socket is assigned.\n        // - Otherwise, the error is re-emitted by the listener of the `'error'`\n        //   event of the `Receiver` object. The listener already closes the\n        //   connection by calling `ws.close()`. This allows a close frame to be\n        //   sent to the other peer. If `ws.terminate()` is called right after this,\n        //   then the close frame might not be sent.\n        terminateOnDestroy = false;\n        duplex.destroy(err);\n    });\n    ws.once(\"close\", function close() {\n        if (duplex.destroyed) return;\n        duplex.push(null);\n    });\n    duplex._destroy = function(err, callback) {\n        if (ws.readyState === ws.CLOSED) {\n            callback(err);\n            process.nextTick(emitClose, duplex);\n            return;\n        }\n        let called = false;\n        ws.once(\"error\", function error(err) {\n            called = true;\n            callback(err);\n        });\n        ws.once(\"close\", function close() {\n            if (!called) callback(err);\n            process.nextTick(emitClose, duplex);\n        });\n        if (terminateOnDestroy) ws.terminate();\n    };\n    duplex._final = function(callback) {\n        if (ws.readyState === ws.CONNECTING) {\n            ws.once(\"open\", function open() {\n                duplex._final(callback);\n            });\n            return;\n        }\n        // If the value of the `_socket` property is `null` it means that `ws` is a\n        // client websocket and the handshake failed. In fact, when this happens, a\n        // socket is never assigned to the websocket. Wait for the `'error'` event\n        // that will be emitted by the websocket.\n        if (ws._socket === null) return;\n        if (ws._socket._writableState.finished) {\n            callback();\n            if (duplex._readableState.endEmitted) duplex.destroy();\n        } else {\n            ws._socket.once(\"finish\", function finish() {\n                // `duplex` is not destroyed here because the `'end'` event will be\n                // emitted on `duplex` after this `'finish'` event. The EOF signaling\n                // `null` chunk is, in fact, pushed when the websocket emits `'close'`.\n                callback();\n            });\n            ws.close();\n        }\n    };\n    duplex._read = function() {\n        if (ws.isPaused) ws.resume();\n    };\n    duplex._write = function(chunk, encoding, callback) {\n        if (ws.readyState === ws.CONNECTING) {\n            ws.once(\"open\", function open() {\n                duplex._write(chunk, encoding, callback);\n            });\n            return;\n        }\n        ws.send(chunk, callback);\n    };\n    duplex.on(\"end\", duplexOnEnd);\n    duplex.on(\"error\", duplexOnError);\n    return duplex;\n}\nmodule.exports = createWebSocketStream;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ws/lib/stream.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ws/lib/subprotocol.js":
/*!********************************************!*\
  !*** ./node_modules/ws/lib/subprotocol.js ***!
  \********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst { tokenChars } = __webpack_require__(/*! ./validation */ \"(ssr)/./node_modules/ws/lib/validation.js\");\n/**\n * Parses the `Sec-WebSocket-Protocol` header into a set of subprotocol names.\n *\n * @param {String} header The field value of the header\n * @return {Set} The subprotocol names\n * @public\n */ function parse(header) {\n    const protocols = new Set();\n    let start = -1;\n    let end = -1;\n    let i = 0;\n    for(i; i < header.length; i++){\n        const code = header.charCodeAt(i);\n        if (end === -1 && tokenChars[code] === 1) {\n            if (start === -1) start = i;\n        } else if (i !== 0 && (code === 0x20 /* ' ' */  || code === 0x09)) {\n            if (end === -1 && start !== -1) end = i;\n        } else if (code === 0x2c /* ',' */ ) {\n            if (start === -1) {\n                throw new SyntaxError(`Unexpected character at index ${i}`);\n            }\n            if (end === -1) end = i;\n            const protocol = header.slice(start, end);\n            if (protocols.has(protocol)) {\n                throw new SyntaxError(`The \"${protocol}\" subprotocol is duplicated`);\n            }\n            protocols.add(protocol);\n            start = end = -1;\n        } else {\n            throw new SyntaxError(`Unexpected character at index ${i}`);\n        }\n    }\n    if (start === -1 || end !== -1) {\n        throw new SyntaxError(\"Unexpected end of input\");\n    }\n    const protocol = header.slice(start, i);\n    if (protocols.has(protocol)) {\n        throw new SyntaxError(`The \"${protocol}\" subprotocol is duplicated`);\n    }\n    protocols.add(protocol);\n    return protocols;\n}\nmodule.exports = {\n    parse\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ws/lib/subprotocol.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ws/lib/validation.js":
/*!*******************************************!*\
  !*** ./node_modules/ws/lib/validation.js ***!
  \*******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst { isUtf8 } = __webpack_require__(/*! buffer */ \"buffer\");\nconst { hasBlob } = __webpack_require__(/*! ./constants */ \"(ssr)/./node_modules/ws/lib/constants.js\");\n//\n// Allowed token characters:\n//\n// '!', '#', '$', '%', '&', ''', '*', '+', '-',\n// '.', 0-9, A-Z, '^', '_', '`', a-z, '|', '~'\n//\n// tokenChars[32] === 0 // ' '\n// tokenChars[33] === 1 // '!'\n// tokenChars[34] === 0 // '\"'\n// ...\n//\n// prettier-ignore\nconst tokenChars = [\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    1,\n    0,\n    1,\n    1,\n    1,\n    1,\n    1,\n    0,\n    0,\n    1,\n    1,\n    0,\n    1,\n    1,\n    0,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    0,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    0,\n    0,\n    0,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    1,\n    0,\n    1,\n    0,\n    1,\n    0 // 112 - 127\n];\n/**\n * Checks if a status code is allowed in a close frame.\n *\n * @param {Number} code The status code\n * @return {Boolean} `true` if the status code is valid, else `false`\n * @public\n */ function isValidStatusCode(code) {\n    return code >= 1000 && code <= 1014 && code !== 1004 && code !== 1005 && code !== 1006 || code >= 3000 && code <= 4999;\n}\n/**\n * Checks if a given buffer contains only correct UTF-8.\n * Ported from https://www.cl.cam.ac.uk/%7Emgk25/ucs/utf8_check.c by\n * Markus Kuhn.\n *\n * @param {Buffer} buf The buffer to check\n * @return {Boolean} `true` if `buf` contains only correct UTF-8, else `false`\n * @public\n */ function _isValidUTF8(buf) {\n    const len = buf.length;\n    let i = 0;\n    while(i < len){\n        if ((buf[i] & 0x80) === 0) {\n            // 0xxxxxxx\n            i++;\n        } else if ((buf[i] & 0xe0) === 0xc0) {\n            // 110xxxxx 10xxxxxx\n            if (i + 1 === len || (buf[i + 1] & 0xc0) !== 0x80 || (buf[i] & 0xfe) === 0xc0 // Overlong\n            ) {\n                return false;\n            }\n            i += 2;\n        } else if ((buf[i] & 0xf0) === 0xe0) {\n            // 1110xxxx 10xxxxxx 10xxxxxx\n            if (i + 2 >= len || (buf[i + 1] & 0xc0) !== 0x80 || (buf[i + 2] & 0xc0) !== 0x80 || buf[i] === 0xe0 && (buf[i + 1] & 0xe0) === 0x80 || // Overlong\n            buf[i] === 0xed && (buf[i + 1] & 0xe0) === 0xa0 // Surrogate (U+D800 - U+DFFF)\n            ) {\n                return false;\n            }\n            i += 3;\n        } else if ((buf[i] & 0xf8) === 0xf0) {\n            // 11110xxx 10xxxxxx 10xxxxxx 10xxxxxx\n            if (i + 3 >= len || (buf[i + 1] & 0xc0) !== 0x80 || (buf[i + 2] & 0xc0) !== 0x80 || (buf[i + 3] & 0xc0) !== 0x80 || buf[i] === 0xf0 && (buf[i + 1] & 0xf0) === 0x80 || // Overlong\n            buf[i] === 0xf4 && buf[i + 1] > 0x8f || buf[i] > 0xf4 // > U+10FFFF\n            ) {\n                return false;\n            }\n            i += 4;\n        } else {\n            return false;\n        }\n    }\n    return true;\n}\n/**\n * Determines whether a value is a `Blob`.\n *\n * @param {*} value The value to be tested\n * @return {Boolean} `true` if `value` is a `Blob`, else `false`\n * @private\n */ function isBlob(value) {\n    return hasBlob && typeof value === \"object\" && typeof value.arrayBuffer === \"function\" && typeof value.type === \"string\" && typeof value.stream === \"function\" && (value[Symbol.toStringTag] === \"Blob\" || value[Symbol.toStringTag] === \"File\");\n}\nmodule.exports = {\n    isBlob,\n    isValidStatusCode,\n    isValidUTF8: _isValidUTF8,\n    tokenChars\n};\nif (isUtf8) {\n    module.exports.isValidUTF8 = function(buf) {\n        return buf.length < 24 ? _isValidUTF8(buf) : isUtf8(buf);\n    };\n} else if (!process.env.WS_NO_UTF_8_VALIDATE) {\n    try {\n        const isValidUTF8 = __webpack_require__(/*! utf-8-validate */ \"?66e9\");\n        module.exports.isValidUTF8 = function(buf) {\n            return buf.length < 32 ? _isValidUTF8(buf) : isValidUTF8(buf);\n        };\n    } catch (e) {\n    // Continue regardless of the error.\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ws/lib/validation.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ws/lib/websocket-server.js":
/*!*************************************************!*\
  !*** ./node_modules/ws/lib/websocket-server.js ***!
  \*************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/* eslint no-unused-vars: [\"error\", { \"varsIgnorePattern\": \"^Duplex$\", \"caughtErrors\": \"none\" }] */ \nconst EventEmitter = __webpack_require__(/*! events */ \"events\");\nconst http = __webpack_require__(/*! http */ \"http\");\nconst { Duplex } = __webpack_require__(/*! stream */ \"stream\");\nconst { createHash } = __webpack_require__(/*! crypto */ \"crypto\");\nconst extension = __webpack_require__(/*! ./extension */ \"(ssr)/./node_modules/ws/lib/extension.js\");\nconst PerMessageDeflate = __webpack_require__(/*! ./permessage-deflate */ \"(ssr)/./node_modules/ws/lib/permessage-deflate.js\");\nconst subprotocol = __webpack_require__(/*! ./subprotocol */ \"(ssr)/./node_modules/ws/lib/subprotocol.js\");\nconst WebSocket = __webpack_require__(/*! ./websocket */ \"(ssr)/./node_modules/ws/lib/websocket.js\");\nconst { GUID, kWebSocket } = __webpack_require__(/*! ./constants */ \"(ssr)/./node_modules/ws/lib/constants.js\");\nconst keyRegex = /^[+/0-9A-Za-z]{22}==$/;\nconst RUNNING = 0;\nconst CLOSING = 1;\nconst CLOSED = 2;\n/**\n * Class representing a WebSocket server.\n *\n * @extends EventEmitter\n */ class WebSocketServer extends EventEmitter {\n    /**\n   * Create a `WebSocketServer` instance.\n   *\n   * @param {Object} options Configuration options\n   * @param {Boolean} [options.allowSynchronousEvents=true] Specifies whether\n   *     any of the `'message'`, `'ping'`, and `'pong'` events can be emitted\n   *     multiple times in the same tick\n   * @param {Boolean} [options.autoPong=true] Specifies whether or not to\n   *     automatically send a pong in response to a ping\n   * @param {Number} [options.backlog=511] The maximum length of the queue of\n   *     pending connections\n   * @param {Boolean} [options.clientTracking=true] Specifies whether or not to\n   *     track clients\n   * @param {Function} [options.handleProtocols] A hook to handle protocols\n   * @param {String} [options.host] The hostname where to bind the server\n   * @param {Number} [options.maxPayload=104857600] The maximum allowed message\n   *     size\n   * @param {Boolean} [options.noServer=false] Enable no server mode\n   * @param {String} [options.path] Accept only connections matching this path\n   * @param {(Boolean|Object)} [options.perMessageDeflate=false] Enable/disable\n   *     permessage-deflate\n   * @param {Number} [options.port] The port where to bind the server\n   * @param {(http.Server|https.Server)} [options.server] A pre-created HTTP/S\n   *     server to use\n   * @param {Boolean} [options.skipUTF8Validation=false] Specifies whether or\n   *     not to skip UTF-8 validation for text and close messages\n   * @param {Function} [options.verifyClient] A hook to reject connections\n   * @param {Function} [options.WebSocket=WebSocket] Specifies the `WebSocket`\n   *     class to use. It must be the `WebSocket` class or class that extends it\n   * @param {Function} [callback] A listener for the `listening` event\n   */ constructor(options, callback){\n        super();\n        options = {\n            allowSynchronousEvents: true,\n            autoPong: true,\n            maxPayload: 100 * 1024 * 1024,\n            skipUTF8Validation: false,\n            perMessageDeflate: false,\n            handleProtocols: null,\n            clientTracking: true,\n            verifyClient: null,\n            noServer: false,\n            backlog: null,\n            server: null,\n            host: null,\n            path: null,\n            port: null,\n            WebSocket,\n            ...options\n        };\n        if (options.port == null && !options.server && !options.noServer || options.port != null && (options.server || options.noServer) || options.server && options.noServer) {\n            throw new TypeError('One and only one of the \"port\", \"server\", or \"noServer\" options ' + \"must be specified\");\n        }\n        if (options.port != null) {\n            this._server = http.createServer((req, res)=>{\n                const body = http.STATUS_CODES[426];\n                res.writeHead(426, {\n                    \"Content-Length\": body.length,\n                    \"Content-Type\": \"text/plain\"\n                });\n                res.end(body);\n            });\n            this._server.listen(options.port, options.host, options.backlog, callback);\n        } else if (options.server) {\n            this._server = options.server;\n        }\n        if (this._server) {\n            const emitConnection = this.emit.bind(this, \"connection\");\n            this._removeListeners = addListeners(this._server, {\n                listening: this.emit.bind(this, \"listening\"),\n                error: this.emit.bind(this, \"error\"),\n                upgrade: (req, socket, head)=>{\n                    this.handleUpgrade(req, socket, head, emitConnection);\n                }\n            });\n        }\n        if (options.perMessageDeflate === true) options.perMessageDeflate = {};\n        if (options.clientTracking) {\n            this.clients = new Set();\n            this._shouldEmitClose = false;\n        }\n        this.options = options;\n        this._state = RUNNING;\n    }\n    /**\n   * Returns the bound address, the address family name, and port of the server\n   * as reported by the operating system if listening on an IP socket.\n   * If the server is listening on a pipe or UNIX domain socket, the name is\n   * returned as a string.\n   *\n   * @return {(Object|String|null)} The address of the server\n   * @public\n   */ address() {\n        if (this.options.noServer) {\n            throw new Error('The server is operating in \"noServer\" mode');\n        }\n        if (!this._server) return null;\n        return this._server.address();\n    }\n    /**\n   * Stop the server from accepting new connections and emit the `'close'` event\n   * when all existing connections are closed.\n   *\n   * @param {Function} [cb] A one-time listener for the `'close'` event\n   * @public\n   */ close(cb) {\n        if (this._state === CLOSED) {\n            if (cb) {\n                this.once(\"close\", ()=>{\n                    cb(new Error(\"The server is not running\"));\n                });\n            }\n            process.nextTick(emitClose, this);\n            return;\n        }\n        if (cb) this.once(\"close\", cb);\n        if (this._state === CLOSING) return;\n        this._state = CLOSING;\n        if (this.options.noServer || this.options.server) {\n            if (this._server) {\n                this._removeListeners();\n                this._removeListeners = this._server = null;\n            }\n            if (this.clients) {\n                if (!this.clients.size) {\n                    process.nextTick(emitClose, this);\n                } else {\n                    this._shouldEmitClose = true;\n                }\n            } else {\n                process.nextTick(emitClose, this);\n            }\n        } else {\n            const server = this._server;\n            this._removeListeners();\n            this._removeListeners = this._server = null;\n            //\n            // The HTTP/S server was created internally. Close it, and rely on its\n            // `'close'` event.\n            //\n            server.close(()=>{\n                emitClose(this);\n            });\n        }\n    }\n    /**\n   * See if a given request should be handled by this server instance.\n   *\n   * @param {http.IncomingMessage} req Request object to inspect\n   * @return {Boolean} `true` if the request is valid, else `false`\n   * @public\n   */ shouldHandle(req) {\n        if (this.options.path) {\n            const index = req.url.indexOf(\"?\");\n            const pathname = index !== -1 ? req.url.slice(0, index) : req.url;\n            if (pathname !== this.options.path) return false;\n        }\n        return true;\n    }\n    /**\n   * Handle a HTTP Upgrade request.\n   *\n   * @param {http.IncomingMessage} req The request object\n   * @param {Duplex} socket The network socket between the server and client\n   * @param {Buffer} head The first packet of the upgraded stream\n   * @param {Function} cb Callback\n   * @public\n   */ handleUpgrade(req, socket, head, cb) {\n        socket.on(\"error\", socketOnError);\n        const key = req.headers[\"sec-websocket-key\"];\n        const upgrade = req.headers.upgrade;\n        const version = +req.headers[\"sec-websocket-version\"];\n        if (req.method !== \"GET\") {\n            const message = \"Invalid HTTP method\";\n            abortHandshakeOrEmitwsClientError(this, req, socket, 405, message);\n            return;\n        }\n        if (upgrade === undefined || upgrade.toLowerCase() !== \"websocket\") {\n            const message = \"Invalid Upgrade header\";\n            abortHandshakeOrEmitwsClientError(this, req, socket, 400, message);\n            return;\n        }\n        if (key === undefined || !keyRegex.test(key)) {\n            const message = \"Missing or invalid Sec-WebSocket-Key header\";\n            abortHandshakeOrEmitwsClientError(this, req, socket, 400, message);\n            return;\n        }\n        if (version !== 8 && version !== 13) {\n            const message = \"Missing or invalid Sec-WebSocket-Version header\";\n            abortHandshakeOrEmitwsClientError(this, req, socket, 400, message);\n            return;\n        }\n        if (!this.shouldHandle(req)) {\n            abortHandshake(socket, 400);\n            return;\n        }\n        const secWebSocketProtocol = req.headers[\"sec-websocket-protocol\"];\n        let protocols = new Set();\n        if (secWebSocketProtocol !== undefined) {\n            try {\n                protocols = subprotocol.parse(secWebSocketProtocol);\n            } catch (err) {\n                const message = \"Invalid Sec-WebSocket-Protocol header\";\n                abortHandshakeOrEmitwsClientError(this, req, socket, 400, message);\n                return;\n            }\n        }\n        const secWebSocketExtensions = req.headers[\"sec-websocket-extensions\"];\n        const extensions = {};\n        if (this.options.perMessageDeflate && secWebSocketExtensions !== undefined) {\n            const perMessageDeflate = new PerMessageDeflate(this.options.perMessageDeflate, true, this.options.maxPayload);\n            try {\n                const offers = extension.parse(secWebSocketExtensions);\n                if (offers[PerMessageDeflate.extensionName]) {\n                    perMessageDeflate.accept(offers[PerMessageDeflate.extensionName]);\n                    extensions[PerMessageDeflate.extensionName] = perMessageDeflate;\n                }\n            } catch (err) {\n                const message = \"Invalid or unacceptable Sec-WebSocket-Extensions header\";\n                abortHandshakeOrEmitwsClientError(this, req, socket, 400, message);\n                return;\n            }\n        }\n        //\n        // Optionally call external client verification handler.\n        //\n        if (this.options.verifyClient) {\n            const info = {\n                origin: req.headers[`${version === 8 ? \"sec-websocket-origin\" : \"origin\"}`],\n                secure: !!(req.socket.authorized || req.socket.encrypted),\n                req\n            };\n            if (this.options.verifyClient.length === 2) {\n                this.options.verifyClient(info, (verified, code, message, headers)=>{\n                    if (!verified) {\n                        return abortHandshake(socket, code || 401, message, headers);\n                    }\n                    this.completeUpgrade(extensions, key, protocols, req, socket, head, cb);\n                });\n                return;\n            }\n            if (!this.options.verifyClient(info)) return abortHandshake(socket, 401);\n        }\n        this.completeUpgrade(extensions, key, protocols, req, socket, head, cb);\n    }\n    /**\n   * Upgrade the connection to WebSocket.\n   *\n   * @param {Object} extensions The accepted extensions\n   * @param {String} key The value of the `Sec-WebSocket-Key` header\n   * @param {Set} protocols The subprotocols\n   * @param {http.IncomingMessage} req The request object\n   * @param {Duplex} socket The network socket between the server and client\n   * @param {Buffer} head The first packet of the upgraded stream\n   * @param {Function} cb Callback\n   * @throws {Error} If called more than once with the same socket\n   * @private\n   */ completeUpgrade(extensions, key, protocols, req, socket, head, cb) {\n        //\n        // Destroy the socket if the client has already sent a FIN packet.\n        //\n        if (!socket.readable || !socket.writable) return socket.destroy();\n        if (socket[kWebSocket]) {\n            throw new Error(\"server.handleUpgrade() was called more than once with the same \" + \"socket, possibly due to a misconfiguration\");\n        }\n        if (this._state > RUNNING) return abortHandshake(socket, 503);\n        const digest = createHash(\"sha1\").update(key + GUID).digest(\"base64\");\n        const headers = [\n            \"HTTP/1.1 101 Switching Protocols\",\n            \"Upgrade: websocket\",\n            \"Connection: Upgrade\",\n            `Sec-WebSocket-Accept: ${digest}`\n        ];\n        const ws = new this.options.WebSocket(null, undefined, this.options);\n        if (protocols.size) {\n            //\n            // Optionally call external protocol selection handler.\n            //\n            const protocol = this.options.handleProtocols ? this.options.handleProtocols(protocols, req) : protocols.values().next().value;\n            if (protocol) {\n                headers.push(`Sec-WebSocket-Protocol: ${protocol}`);\n                ws._protocol = protocol;\n            }\n        }\n        if (extensions[PerMessageDeflate.extensionName]) {\n            const params = extensions[PerMessageDeflate.extensionName].params;\n            const value = extension.format({\n                [PerMessageDeflate.extensionName]: [\n                    params\n                ]\n            });\n            headers.push(`Sec-WebSocket-Extensions: ${value}`);\n            ws._extensions = extensions;\n        }\n        //\n        // Allow external modification/inspection of handshake headers.\n        //\n        this.emit(\"headers\", headers, req);\n        socket.write(headers.concat(\"\\r\\n\").join(\"\\r\\n\"));\n        socket.removeListener(\"error\", socketOnError);\n        ws.setSocket(socket, head, {\n            allowSynchronousEvents: this.options.allowSynchronousEvents,\n            maxPayload: this.options.maxPayload,\n            skipUTF8Validation: this.options.skipUTF8Validation\n        });\n        if (this.clients) {\n            this.clients.add(ws);\n            ws.on(\"close\", ()=>{\n                this.clients.delete(ws);\n                if (this._shouldEmitClose && !this.clients.size) {\n                    process.nextTick(emitClose, this);\n                }\n            });\n        }\n        cb(ws, req);\n    }\n}\nmodule.exports = WebSocketServer;\n/**\n * Add event listeners on an `EventEmitter` using a map of <event, listener>\n * pairs.\n *\n * @param {EventEmitter} server The event emitter\n * @param {Object.<String, Function>} map The listeners to add\n * @return {Function} A function that will remove the added listeners when\n *     called\n * @private\n */ function addListeners(server, map) {\n    for (const event of Object.keys(map))server.on(event, map[event]);\n    return function removeListeners() {\n        for (const event of Object.keys(map)){\n            server.removeListener(event, map[event]);\n        }\n    };\n}\n/**\n * Emit a `'close'` event on an `EventEmitter`.\n *\n * @param {EventEmitter} server The event emitter\n * @private\n */ function emitClose(server) {\n    server._state = CLOSED;\n    server.emit(\"close\");\n}\n/**\n * Handle socket errors.\n *\n * @private\n */ function socketOnError() {\n    this.destroy();\n}\n/**\n * Close the connection when preconditions are not fulfilled.\n *\n * @param {Duplex} socket The socket of the upgrade request\n * @param {Number} code The HTTP response status code\n * @param {String} [message] The HTTP response body\n * @param {Object} [headers] Additional HTTP response headers\n * @private\n */ function abortHandshake(socket, code, message, headers) {\n    //\n    // The socket is writable unless the user destroyed or ended it before calling\n    // `server.handleUpgrade()` or in the `verifyClient` function, which is a user\n    // error. Handling this does not make much sense as the worst that can happen\n    // is that some of the data written by the user might be discarded due to the\n    // call to `socket.end()` below, which triggers an `'error'` event that in\n    // turn causes the socket to be destroyed.\n    //\n    message = message || http.STATUS_CODES[code];\n    headers = {\n        Connection: \"close\",\n        \"Content-Type\": \"text/html\",\n        \"Content-Length\": Buffer.byteLength(message),\n        ...headers\n    };\n    socket.once(\"finish\", socket.destroy);\n    socket.end(`HTTP/1.1 ${code} ${http.STATUS_CODES[code]}\\r\\n` + Object.keys(headers).map((h)=>`${h}: ${headers[h]}`).join(\"\\r\\n\") + \"\\r\\n\\r\\n\" + message);\n}\n/**\n * Emit a `'wsClientError'` event on a `WebSocketServer` if there is at least\n * one listener for it, otherwise call `abortHandshake()`.\n *\n * @param {WebSocketServer} server The WebSocket server\n * @param {http.IncomingMessage} req The request object\n * @param {Duplex} socket The socket of the upgrade request\n * @param {Number} code The HTTP response status code\n * @param {String} message The HTTP response body\n * @private\n */ function abortHandshakeOrEmitwsClientError(server, req, socket, code, message) {\n    if (server.listenerCount(\"wsClientError\")) {\n        const err = new Error(message);\n        Error.captureStackTrace(err, abortHandshakeOrEmitwsClientError);\n        server.emit(\"wsClientError\", err, socket, req);\n    } else {\n        abortHandshake(socket, code, message);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ws/lib/websocket-server.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ws/lib/websocket.js":
/*!******************************************!*\
  !*** ./node_modules/ws/lib/websocket.js ***!
  \******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/* eslint no-unused-vars: [\"error\", { \"varsIgnorePattern\": \"^Duplex|Readable$\", \"caughtErrors\": \"none\" }] */ \nconst EventEmitter = __webpack_require__(/*! events */ \"events\");\nconst https = __webpack_require__(/*! https */ \"https\");\nconst http = __webpack_require__(/*! http */ \"http\");\nconst net = __webpack_require__(/*! net */ \"net\");\nconst tls = __webpack_require__(/*! tls */ \"tls\");\nconst { randomBytes, createHash } = __webpack_require__(/*! crypto */ \"crypto\");\nconst { Duplex, Readable } = __webpack_require__(/*! stream */ \"stream\");\nconst { URL } = __webpack_require__(/*! url */ \"url\");\nconst PerMessageDeflate = __webpack_require__(/*! ./permessage-deflate */ \"(ssr)/./node_modules/ws/lib/permessage-deflate.js\");\nconst Receiver = __webpack_require__(/*! ./receiver */ \"(ssr)/./node_modules/ws/lib/receiver.js\");\nconst Sender = __webpack_require__(/*! ./sender */ \"(ssr)/./node_modules/ws/lib/sender.js\");\nconst { isBlob } = __webpack_require__(/*! ./validation */ \"(ssr)/./node_modules/ws/lib/validation.js\");\nconst { BINARY_TYPES, EMPTY_BUFFER, GUID, kForOnEventAttribute, kListener, kStatusCode, kWebSocket, NOOP } = __webpack_require__(/*! ./constants */ \"(ssr)/./node_modules/ws/lib/constants.js\");\nconst { EventTarget: { addEventListener, removeEventListener } } = __webpack_require__(/*! ./event-target */ \"(ssr)/./node_modules/ws/lib/event-target.js\");\nconst { format, parse } = __webpack_require__(/*! ./extension */ \"(ssr)/./node_modules/ws/lib/extension.js\");\nconst { toBuffer } = __webpack_require__(/*! ./buffer-util */ \"(ssr)/./node_modules/ws/lib/buffer-util.js\");\nconst closeTimeout = 30 * 1000;\nconst kAborted = Symbol(\"kAborted\");\nconst protocolVersions = [\n    8,\n    13\n];\nconst readyStates = [\n    \"CONNECTING\",\n    \"OPEN\",\n    \"CLOSING\",\n    \"CLOSED\"\n];\nconst subprotocolRegex = /^[!#$%&'*+\\-.0-9A-Z^_`|a-z~]+$/;\n/**\n * Class representing a WebSocket.\n *\n * @extends EventEmitter\n */ class WebSocket extends EventEmitter {\n    /**\n   * Create a new `WebSocket`.\n   *\n   * @param {(String|URL)} address The URL to which to connect\n   * @param {(String|String[])} [protocols] The subprotocols\n   * @param {Object} [options] Connection options\n   */ constructor(address, protocols, options){\n        super();\n        this._binaryType = BINARY_TYPES[0];\n        this._closeCode = 1006;\n        this._closeFrameReceived = false;\n        this._closeFrameSent = false;\n        this._closeMessage = EMPTY_BUFFER;\n        this._closeTimer = null;\n        this._errorEmitted = false;\n        this._extensions = {};\n        this._paused = false;\n        this._protocol = \"\";\n        this._readyState = WebSocket.CONNECTING;\n        this._receiver = null;\n        this._sender = null;\n        this._socket = null;\n        if (address !== null) {\n            this._bufferedAmount = 0;\n            this._isServer = false;\n            this._redirects = 0;\n            if (protocols === undefined) {\n                protocols = [];\n            } else if (!Array.isArray(protocols)) {\n                if (typeof protocols === \"object\" && protocols !== null) {\n                    options = protocols;\n                    protocols = [];\n                } else {\n                    protocols = [\n                        protocols\n                    ];\n                }\n            }\n            initAsClient(this, address, protocols, options);\n        } else {\n            this._autoPong = options.autoPong;\n            this._isServer = true;\n        }\n    }\n    /**\n   * For historical reasons, the custom \"nodebuffer\" type is used by the default\n   * instead of \"blob\".\n   *\n   * @type {String}\n   */ get binaryType() {\n        return this._binaryType;\n    }\n    set binaryType(type) {\n        if (!BINARY_TYPES.includes(type)) return;\n        this._binaryType = type;\n        //\n        // Allow to change `binaryType` on the fly.\n        //\n        if (this._receiver) this._receiver._binaryType = type;\n    }\n    /**\n   * @type {Number}\n   */ get bufferedAmount() {\n        if (!this._socket) return this._bufferedAmount;\n        return this._socket._writableState.length + this._sender._bufferedBytes;\n    }\n    /**\n   * @type {String}\n   */ get extensions() {\n        return Object.keys(this._extensions).join();\n    }\n    /**\n   * @type {Boolean}\n   */ get isPaused() {\n        return this._paused;\n    }\n    /**\n   * @type {Function}\n   */ /* istanbul ignore next */ get onclose() {\n        return null;\n    }\n    /**\n   * @type {Function}\n   */ /* istanbul ignore next */ get onerror() {\n        return null;\n    }\n    /**\n   * @type {Function}\n   */ /* istanbul ignore next */ get onopen() {\n        return null;\n    }\n    /**\n   * @type {Function}\n   */ /* istanbul ignore next */ get onmessage() {\n        return null;\n    }\n    /**\n   * @type {String}\n   */ get protocol() {\n        return this._protocol;\n    }\n    /**\n   * @type {Number}\n   */ get readyState() {\n        return this._readyState;\n    }\n    /**\n   * @type {String}\n   */ get url() {\n        return this._url;\n    }\n    /**\n   * Set up the socket and the internal resources.\n   *\n   * @param {Duplex} socket The network socket between the server and client\n   * @param {Buffer} head The first packet of the upgraded stream\n   * @param {Object} options Options object\n   * @param {Boolean} [options.allowSynchronousEvents=false] Specifies whether\n   *     any of the `'message'`, `'ping'`, and `'pong'` events can be emitted\n   *     multiple times in the same tick\n   * @param {Function} [options.generateMask] The function used to generate the\n   *     masking key\n   * @param {Number} [options.maxPayload=0] The maximum allowed message size\n   * @param {Boolean} [options.skipUTF8Validation=false] Specifies whether or\n   *     not to skip UTF-8 validation for text and close messages\n   * @private\n   */ setSocket(socket, head, options) {\n        const receiver = new Receiver({\n            allowSynchronousEvents: options.allowSynchronousEvents,\n            binaryType: this.binaryType,\n            extensions: this._extensions,\n            isServer: this._isServer,\n            maxPayload: options.maxPayload,\n            skipUTF8Validation: options.skipUTF8Validation\n        });\n        const sender = new Sender(socket, this._extensions, options.generateMask);\n        this._receiver = receiver;\n        this._sender = sender;\n        this._socket = socket;\n        receiver[kWebSocket] = this;\n        sender[kWebSocket] = this;\n        socket[kWebSocket] = this;\n        receiver.on(\"conclude\", receiverOnConclude);\n        receiver.on(\"drain\", receiverOnDrain);\n        receiver.on(\"error\", receiverOnError);\n        receiver.on(\"message\", receiverOnMessage);\n        receiver.on(\"ping\", receiverOnPing);\n        receiver.on(\"pong\", receiverOnPong);\n        sender.onerror = senderOnError;\n        //\n        // These methods may not be available if `socket` is just a `Duplex`.\n        //\n        if (socket.setTimeout) socket.setTimeout(0);\n        if (socket.setNoDelay) socket.setNoDelay();\n        if (head.length > 0) socket.unshift(head);\n        socket.on(\"close\", socketOnClose);\n        socket.on(\"data\", socketOnData);\n        socket.on(\"end\", socketOnEnd);\n        socket.on(\"error\", socketOnError);\n        this._readyState = WebSocket.OPEN;\n        this.emit(\"open\");\n    }\n    /**\n   * Emit the `'close'` event.\n   *\n   * @private\n   */ emitClose() {\n        if (!this._socket) {\n            this._readyState = WebSocket.CLOSED;\n            this.emit(\"close\", this._closeCode, this._closeMessage);\n            return;\n        }\n        if (this._extensions[PerMessageDeflate.extensionName]) {\n            this._extensions[PerMessageDeflate.extensionName].cleanup();\n        }\n        this._receiver.removeAllListeners();\n        this._readyState = WebSocket.CLOSED;\n        this.emit(\"close\", this._closeCode, this._closeMessage);\n    }\n    /**\n   * Start a closing handshake.\n   *\n   *          +----------+   +-----------+   +----------+\n   *     - - -|ws.close()|-->|close frame|-->|ws.close()|- - -\n   *    |     +----------+   +-----------+   +----------+     |\n   *          +----------+   +-----------+         |\n   * CLOSING  |ws.close()|<--|close frame|<--+-----+       CLOSING\n   *          +----------+   +-----------+   |\n   *    |           |                        |   +---+        |\n   *                +------------------------+-->|fin| - - - -\n   *    |         +---+                      |   +---+\n   *     - - - - -|fin|<---------------------+\n   *              +---+\n   *\n   * @param {Number} [code] Status code explaining why the connection is closing\n   * @param {(String|Buffer)} [data] The reason why the connection is\n   *     closing\n   * @public\n   */ close(code, data) {\n        if (this.readyState === WebSocket.CLOSED) return;\n        if (this.readyState === WebSocket.CONNECTING) {\n            const msg = \"WebSocket was closed before the connection was established\";\n            abortHandshake(this, this._req, msg);\n            return;\n        }\n        if (this.readyState === WebSocket.CLOSING) {\n            if (this._closeFrameSent && (this._closeFrameReceived || this._receiver._writableState.errorEmitted)) {\n                this._socket.end();\n            }\n            return;\n        }\n        this._readyState = WebSocket.CLOSING;\n        this._sender.close(code, data, !this._isServer, (err)=>{\n            //\n            // This error is handled by the `'error'` listener on the socket. We only\n            // want to know if the close frame has been sent here.\n            //\n            if (err) return;\n            this._closeFrameSent = true;\n            if (this._closeFrameReceived || this._receiver._writableState.errorEmitted) {\n                this._socket.end();\n            }\n        });\n        setCloseTimer(this);\n    }\n    /**\n   * Pause the socket.\n   *\n   * @public\n   */ pause() {\n        if (this.readyState === WebSocket.CONNECTING || this.readyState === WebSocket.CLOSED) {\n            return;\n        }\n        this._paused = true;\n        this._socket.pause();\n    }\n    /**\n   * Send a ping.\n   *\n   * @param {*} [data] The data to send\n   * @param {Boolean} [mask] Indicates whether or not to mask `data`\n   * @param {Function} [cb] Callback which is executed when the ping is sent\n   * @public\n   */ ping(data, mask, cb) {\n        if (this.readyState === WebSocket.CONNECTING) {\n            throw new Error(\"WebSocket is not open: readyState 0 (CONNECTING)\");\n        }\n        if (typeof data === \"function\") {\n            cb = data;\n            data = mask = undefined;\n        } else if (typeof mask === \"function\") {\n            cb = mask;\n            mask = undefined;\n        }\n        if (typeof data === \"number\") data = data.toString();\n        if (this.readyState !== WebSocket.OPEN) {\n            sendAfterClose(this, data, cb);\n            return;\n        }\n        if (mask === undefined) mask = !this._isServer;\n        this._sender.ping(data || EMPTY_BUFFER, mask, cb);\n    }\n    /**\n   * Send a pong.\n   *\n   * @param {*} [data] The data to send\n   * @param {Boolean} [mask] Indicates whether or not to mask `data`\n   * @param {Function} [cb] Callback which is executed when the pong is sent\n   * @public\n   */ pong(data, mask, cb) {\n        if (this.readyState === WebSocket.CONNECTING) {\n            throw new Error(\"WebSocket is not open: readyState 0 (CONNECTING)\");\n        }\n        if (typeof data === \"function\") {\n            cb = data;\n            data = mask = undefined;\n        } else if (typeof mask === \"function\") {\n            cb = mask;\n            mask = undefined;\n        }\n        if (typeof data === \"number\") data = data.toString();\n        if (this.readyState !== WebSocket.OPEN) {\n            sendAfterClose(this, data, cb);\n            return;\n        }\n        if (mask === undefined) mask = !this._isServer;\n        this._sender.pong(data || EMPTY_BUFFER, mask, cb);\n    }\n    /**\n   * Resume the socket.\n   *\n   * @public\n   */ resume() {\n        if (this.readyState === WebSocket.CONNECTING || this.readyState === WebSocket.CLOSED) {\n            return;\n        }\n        this._paused = false;\n        if (!this._receiver._writableState.needDrain) this._socket.resume();\n    }\n    /**\n   * Send a data message.\n   *\n   * @param {*} data The message to send\n   * @param {Object} [options] Options object\n   * @param {Boolean} [options.binary] Specifies whether `data` is binary or\n   *     text\n   * @param {Boolean} [options.compress] Specifies whether or not to compress\n   *     `data`\n   * @param {Boolean} [options.fin=true] Specifies whether the fragment is the\n   *     last one\n   * @param {Boolean} [options.mask] Specifies whether or not to mask `data`\n   * @param {Function} [cb] Callback which is executed when data is written out\n   * @public\n   */ send(data, options, cb) {\n        if (this.readyState === WebSocket.CONNECTING) {\n            throw new Error(\"WebSocket is not open: readyState 0 (CONNECTING)\");\n        }\n        if (typeof options === \"function\") {\n            cb = options;\n            options = {};\n        }\n        if (typeof data === \"number\") data = data.toString();\n        if (this.readyState !== WebSocket.OPEN) {\n            sendAfterClose(this, data, cb);\n            return;\n        }\n        const opts = {\n            binary: typeof data !== \"string\",\n            mask: !this._isServer,\n            compress: true,\n            fin: true,\n            ...options\n        };\n        if (!this._extensions[PerMessageDeflate.extensionName]) {\n            opts.compress = false;\n        }\n        this._sender.send(data || EMPTY_BUFFER, opts, cb);\n    }\n    /**\n   * Forcibly close the connection.\n   *\n   * @public\n   */ terminate() {\n        if (this.readyState === WebSocket.CLOSED) return;\n        if (this.readyState === WebSocket.CONNECTING) {\n            const msg = \"WebSocket was closed before the connection was established\";\n            abortHandshake(this, this._req, msg);\n            return;\n        }\n        if (this._socket) {\n            this._readyState = WebSocket.CLOSING;\n            this._socket.destroy();\n        }\n    }\n}\n/**\n * @constant {Number} CONNECTING\n * @memberof WebSocket\n */ Object.defineProperty(WebSocket, \"CONNECTING\", {\n    enumerable: true,\n    value: readyStates.indexOf(\"CONNECTING\")\n});\n/**\n * @constant {Number} CONNECTING\n * @memberof WebSocket.prototype\n */ Object.defineProperty(WebSocket.prototype, \"CONNECTING\", {\n    enumerable: true,\n    value: readyStates.indexOf(\"CONNECTING\")\n});\n/**\n * @constant {Number} OPEN\n * @memberof WebSocket\n */ Object.defineProperty(WebSocket, \"OPEN\", {\n    enumerable: true,\n    value: readyStates.indexOf(\"OPEN\")\n});\n/**\n * @constant {Number} OPEN\n * @memberof WebSocket.prototype\n */ Object.defineProperty(WebSocket.prototype, \"OPEN\", {\n    enumerable: true,\n    value: readyStates.indexOf(\"OPEN\")\n});\n/**\n * @constant {Number} CLOSING\n * @memberof WebSocket\n */ Object.defineProperty(WebSocket, \"CLOSING\", {\n    enumerable: true,\n    value: readyStates.indexOf(\"CLOSING\")\n});\n/**\n * @constant {Number} CLOSING\n * @memberof WebSocket.prototype\n */ Object.defineProperty(WebSocket.prototype, \"CLOSING\", {\n    enumerable: true,\n    value: readyStates.indexOf(\"CLOSING\")\n});\n/**\n * @constant {Number} CLOSED\n * @memberof WebSocket\n */ Object.defineProperty(WebSocket, \"CLOSED\", {\n    enumerable: true,\n    value: readyStates.indexOf(\"CLOSED\")\n});\n/**\n * @constant {Number} CLOSED\n * @memberof WebSocket.prototype\n */ Object.defineProperty(WebSocket.prototype, \"CLOSED\", {\n    enumerable: true,\n    value: readyStates.indexOf(\"CLOSED\")\n});\n[\n    \"binaryType\",\n    \"bufferedAmount\",\n    \"extensions\",\n    \"isPaused\",\n    \"protocol\",\n    \"readyState\",\n    \"url\"\n].forEach((property)=>{\n    Object.defineProperty(WebSocket.prototype, property, {\n        enumerable: true\n    });\n});\n//\n// Add the `onopen`, `onerror`, `onclose`, and `onmessage` attributes.\n// See https://html.spec.whatwg.org/multipage/comms.html#the-websocket-interface\n//\n[\n    \"open\",\n    \"error\",\n    \"close\",\n    \"message\"\n].forEach((method)=>{\n    Object.defineProperty(WebSocket.prototype, `on${method}`, {\n        enumerable: true,\n        get () {\n            for (const listener of this.listeners(method)){\n                if (listener[kForOnEventAttribute]) return listener[kListener];\n            }\n            return null;\n        },\n        set (handler) {\n            for (const listener of this.listeners(method)){\n                if (listener[kForOnEventAttribute]) {\n                    this.removeListener(method, listener);\n                    break;\n                }\n            }\n            if (typeof handler !== \"function\") return;\n            this.addEventListener(method, handler, {\n                [kForOnEventAttribute]: true\n            });\n        }\n    });\n});\nWebSocket.prototype.addEventListener = addEventListener;\nWebSocket.prototype.removeEventListener = removeEventListener;\nmodule.exports = WebSocket;\n/**\n * Initialize a WebSocket client.\n *\n * @param {WebSocket} websocket The client to initialize\n * @param {(String|URL)} address The URL to which to connect\n * @param {Array} protocols The subprotocols\n * @param {Object} [options] Connection options\n * @param {Boolean} [options.allowSynchronousEvents=true] Specifies whether any\n *     of the `'message'`, `'ping'`, and `'pong'` events can be emitted multiple\n *     times in the same tick\n * @param {Boolean} [options.autoPong=true] Specifies whether or not to\n *     automatically send a pong in response to a ping\n * @param {Function} [options.finishRequest] A function which can be used to\n *     customize the headers of each http request before it is sent\n * @param {Boolean} [options.followRedirects=false] Whether or not to follow\n *     redirects\n * @param {Function} [options.generateMask] The function used to generate the\n *     masking key\n * @param {Number} [options.handshakeTimeout] Timeout in milliseconds for the\n *     handshake request\n * @param {Number} [options.maxPayload=104857600] The maximum allowed message\n *     size\n * @param {Number} [options.maxRedirects=10] The maximum number of redirects\n *     allowed\n * @param {String} [options.origin] Value of the `Origin` or\n *     `Sec-WebSocket-Origin` header\n * @param {(Boolean|Object)} [options.perMessageDeflate=true] Enable/disable\n *     permessage-deflate\n * @param {Number} [options.protocolVersion=13] Value of the\n *     `Sec-WebSocket-Version` header\n * @param {Boolean} [options.skipUTF8Validation=false] Specifies whether or\n *     not to skip UTF-8 validation for text and close messages\n * @private\n */ function initAsClient(websocket, address, protocols, options) {\n    const opts = {\n        allowSynchronousEvents: true,\n        autoPong: true,\n        protocolVersion: protocolVersions[1],\n        maxPayload: 100 * 1024 * 1024,\n        skipUTF8Validation: false,\n        perMessageDeflate: true,\n        followRedirects: false,\n        maxRedirects: 10,\n        ...options,\n        socketPath: undefined,\n        hostname: undefined,\n        protocol: undefined,\n        timeout: undefined,\n        method: \"GET\",\n        host: undefined,\n        path: undefined,\n        port: undefined\n    };\n    websocket._autoPong = opts.autoPong;\n    if (!protocolVersions.includes(opts.protocolVersion)) {\n        throw new RangeError(`Unsupported protocol version: ${opts.protocolVersion} ` + `(supported versions: ${protocolVersions.join(\", \")})`);\n    }\n    let parsedUrl;\n    if (address instanceof URL) {\n        parsedUrl = address;\n    } else {\n        try {\n            parsedUrl = new URL(address);\n        } catch (e) {\n            throw new SyntaxError(`Invalid URL: ${address}`);\n        }\n    }\n    if (parsedUrl.protocol === \"http:\") {\n        parsedUrl.protocol = \"ws:\";\n    } else if (parsedUrl.protocol === \"https:\") {\n        parsedUrl.protocol = \"wss:\";\n    }\n    websocket._url = parsedUrl.href;\n    const isSecure = parsedUrl.protocol === \"wss:\";\n    const isIpcUrl = parsedUrl.protocol === \"ws+unix:\";\n    let invalidUrlMessage;\n    if (parsedUrl.protocol !== \"ws:\" && !isSecure && !isIpcUrl) {\n        invalidUrlMessage = 'The URL\\'s protocol must be one of \"ws:\", \"wss:\", ' + '\"http:\", \"https:\", or \"ws+unix:\"';\n    } else if (isIpcUrl && !parsedUrl.pathname) {\n        invalidUrlMessage = \"The URL's pathname is empty\";\n    } else if (parsedUrl.hash) {\n        invalidUrlMessage = \"The URL contains a fragment identifier\";\n    }\n    if (invalidUrlMessage) {\n        const err = new SyntaxError(invalidUrlMessage);\n        if (websocket._redirects === 0) {\n            throw err;\n        } else {\n            emitErrorAndClose(websocket, err);\n            return;\n        }\n    }\n    const defaultPort = isSecure ? 443 : 80;\n    const key = randomBytes(16).toString(\"base64\");\n    const request = isSecure ? https.request : http.request;\n    const protocolSet = new Set();\n    let perMessageDeflate;\n    opts.createConnection = opts.createConnection || (isSecure ? tlsConnect : netConnect);\n    opts.defaultPort = opts.defaultPort || defaultPort;\n    opts.port = parsedUrl.port || defaultPort;\n    opts.host = parsedUrl.hostname.startsWith(\"[\") ? parsedUrl.hostname.slice(1, -1) : parsedUrl.hostname;\n    opts.headers = {\n        ...opts.headers,\n        \"Sec-WebSocket-Version\": opts.protocolVersion,\n        \"Sec-WebSocket-Key\": key,\n        Connection: \"Upgrade\",\n        Upgrade: \"websocket\"\n    };\n    opts.path = parsedUrl.pathname + parsedUrl.search;\n    opts.timeout = opts.handshakeTimeout;\n    if (opts.perMessageDeflate) {\n        perMessageDeflate = new PerMessageDeflate(opts.perMessageDeflate !== true ? opts.perMessageDeflate : {}, false, opts.maxPayload);\n        opts.headers[\"Sec-WebSocket-Extensions\"] = format({\n            [PerMessageDeflate.extensionName]: perMessageDeflate.offer()\n        });\n    }\n    if (protocols.length) {\n        for (const protocol of protocols){\n            if (typeof protocol !== \"string\" || !subprotocolRegex.test(protocol) || protocolSet.has(protocol)) {\n                throw new SyntaxError(\"An invalid or duplicated subprotocol was specified\");\n            }\n            protocolSet.add(protocol);\n        }\n        opts.headers[\"Sec-WebSocket-Protocol\"] = protocols.join(\",\");\n    }\n    if (opts.origin) {\n        if (opts.protocolVersion < 13) {\n            opts.headers[\"Sec-WebSocket-Origin\"] = opts.origin;\n        } else {\n            opts.headers.Origin = opts.origin;\n        }\n    }\n    if (parsedUrl.username || parsedUrl.password) {\n        opts.auth = `${parsedUrl.username}:${parsedUrl.password}`;\n    }\n    if (isIpcUrl) {\n        const parts = opts.path.split(\":\");\n        opts.socketPath = parts[0];\n        opts.path = parts[1];\n    }\n    let req;\n    if (opts.followRedirects) {\n        if (websocket._redirects === 0) {\n            websocket._originalIpc = isIpcUrl;\n            websocket._originalSecure = isSecure;\n            websocket._originalHostOrSocketPath = isIpcUrl ? opts.socketPath : parsedUrl.host;\n            const headers = options && options.headers;\n            //\n            // Shallow copy the user provided options so that headers can be changed\n            // without mutating the original object.\n            //\n            options = {\n                ...options,\n                headers: {}\n            };\n            if (headers) {\n                for (const [key, value] of Object.entries(headers)){\n                    options.headers[key.toLowerCase()] = value;\n                }\n            }\n        } else if (websocket.listenerCount(\"redirect\") === 0) {\n            const isSameHost = isIpcUrl ? websocket._originalIpc ? opts.socketPath === websocket._originalHostOrSocketPath : false : websocket._originalIpc ? false : parsedUrl.host === websocket._originalHostOrSocketPath;\n            if (!isSameHost || websocket._originalSecure && !isSecure) {\n                //\n                // Match curl 7.77.0 behavior and drop the following headers. These\n                // headers are also dropped when following a redirect to a subdomain.\n                //\n                delete opts.headers.authorization;\n                delete opts.headers.cookie;\n                if (!isSameHost) delete opts.headers.host;\n                opts.auth = undefined;\n            }\n        }\n        //\n        // Match curl 7.77.0 behavior and make the first `Authorization` header win.\n        // If the `Authorization` header is set, then there is nothing to do as it\n        // will take precedence.\n        //\n        if (opts.auth && !options.headers.authorization) {\n            options.headers.authorization = \"Basic \" + Buffer.from(opts.auth).toString(\"base64\");\n        }\n        req = websocket._req = request(opts);\n        if (websocket._redirects) {\n            //\n            // Unlike what is done for the `'upgrade'` event, no early exit is\n            // triggered here if the user calls `websocket.close()` or\n            // `websocket.terminate()` from a listener of the `'redirect'` event. This\n            // is because the user can also call `request.destroy()` with an error\n            // before calling `websocket.close()` or `websocket.terminate()` and this\n            // would result in an error being emitted on the `request` object with no\n            // `'error'` event listeners attached.\n            //\n            websocket.emit(\"redirect\", websocket.url, req);\n        }\n    } else {\n        req = websocket._req = request(opts);\n    }\n    if (opts.timeout) {\n        req.on(\"timeout\", ()=>{\n            abortHandshake(websocket, req, \"Opening handshake has timed out\");\n        });\n    }\n    req.on(\"error\", (err)=>{\n        if (req === null || req[kAborted]) return;\n        req = websocket._req = null;\n        emitErrorAndClose(websocket, err);\n    });\n    req.on(\"response\", (res)=>{\n        const location = res.headers.location;\n        const statusCode = res.statusCode;\n        if (location && opts.followRedirects && statusCode >= 300 && statusCode < 400) {\n            if (++websocket._redirects > opts.maxRedirects) {\n                abortHandshake(websocket, req, \"Maximum redirects exceeded\");\n                return;\n            }\n            req.abort();\n            let addr;\n            try {\n                addr = new URL(location, address);\n            } catch (e) {\n                const err = new SyntaxError(`Invalid URL: ${location}`);\n                emitErrorAndClose(websocket, err);\n                return;\n            }\n            initAsClient(websocket, addr, protocols, options);\n        } else if (!websocket.emit(\"unexpected-response\", req, res)) {\n            abortHandshake(websocket, req, `Unexpected server response: ${res.statusCode}`);\n        }\n    });\n    req.on(\"upgrade\", (res, socket, head)=>{\n        websocket.emit(\"upgrade\", res);\n        //\n        // The user may have closed the connection from a listener of the\n        // `'upgrade'` event.\n        //\n        if (websocket.readyState !== WebSocket.CONNECTING) return;\n        req = websocket._req = null;\n        const upgrade = res.headers.upgrade;\n        if (upgrade === undefined || upgrade.toLowerCase() !== \"websocket\") {\n            abortHandshake(websocket, socket, \"Invalid Upgrade header\");\n            return;\n        }\n        const digest = createHash(\"sha1\").update(key + GUID).digest(\"base64\");\n        if (res.headers[\"sec-websocket-accept\"] !== digest) {\n            abortHandshake(websocket, socket, \"Invalid Sec-WebSocket-Accept header\");\n            return;\n        }\n        const serverProt = res.headers[\"sec-websocket-protocol\"];\n        let protError;\n        if (serverProt !== undefined) {\n            if (!protocolSet.size) {\n                protError = \"Server sent a subprotocol but none was requested\";\n            } else if (!protocolSet.has(serverProt)) {\n                protError = \"Server sent an invalid subprotocol\";\n            }\n        } else if (protocolSet.size) {\n            protError = \"Server sent no subprotocol\";\n        }\n        if (protError) {\n            abortHandshake(websocket, socket, protError);\n            return;\n        }\n        if (serverProt) websocket._protocol = serverProt;\n        const secWebSocketExtensions = res.headers[\"sec-websocket-extensions\"];\n        if (secWebSocketExtensions !== undefined) {\n            if (!perMessageDeflate) {\n                const message = \"Server sent a Sec-WebSocket-Extensions header but no extension \" + \"was requested\";\n                abortHandshake(websocket, socket, message);\n                return;\n            }\n            let extensions;\n            try {\n                extensions = parse(secWebSocketExtensions);\n            } catch (err) {\n                const message = \"Invalid Sec-WebSocket-Extensions header\";\n                abortHandshake(websocket, socket, message);\n                return;\n            }\n            const extensionNames = Object.keys(extensions);\n            if (extensionNames.length !== 1 || extensionNames[0] !== PerMessageDeflate.extensionName) {\n                const message = \"Server indicated an extension that was not requested\";\n                abortHandshake(websocket, socket, message);\n                return;\n            }\n            try {\n                perMessageDeflate.accept(extensions[PerMessageDeflate.extensionName]);\n            } catch (err) {\n                const message = \"Invalid Sec-WebSocket-Extensions header\";\n                abortHandshake(websocket, socket, message);\n                return;\n            }\n            websocket._extensions[PerMessageDeflate.extensionName] = perMessageDeflate;\n        }\n        websocket.setSocket(socket, head, {\n            allowSynchronousEvents: opts.allowSynchronousEvents,\n            generateMask: opts.generateMask,\n            maxPayload: opts.maxPayload,\n            skipUTF8Validation: opts.skipUTF8Validation\n        });\n    });\n    if (opts.finishRequest) {\n        opts.finishRequest(req, websocket);\n    } else {\n        req.end();\n    }\n}\n/**\n * Emit the `'error'` and `'close'` events.\n *\n * @param {WebSocket} websocket The WebSocket instance\n * @param {Error} The error to emit\n * @private\n */ function emitErrorAndClose(websocket, err) {\n    websocket._readyState = WebSocket.CLOSING;\n    //\n    // The following assignment is practically useless and is done only for\n    // consistency.\n    //\n    websocket._errorEmitted = true;\n    websocket.emit(\"error\", err);\n    websocket.emitClose();\n}\n/**\n * Create a `net.Socket` and initiate a connection.\n *\n * @param {Object} options Connection options\n * @return {net.Socket} The newly created socket used to start the connection\n * @private\n */ function netConnect(options) {\n    options.path = options.socketPath;\n    return net.connect(options);\n}\n/**\n * Create a `tls.TLSSocket` and initiate a connection.\n *\n * @param {Object} options Connection options\n * @return {tls.TLSSocket} The newly created socket used to start the connection\n * @private\n */ function tlsConnect(options) {\n    options.path = undefined;\n    if (!options.servername && options.servername !== \"\") {\n        options.servername = net.isIP(options.host) ? \"\" : options.host;\n    }\n    return tls.connect(options);\n}\n/**\n * Abort the handshake and emit an error.\n *\n * @param {WebSocket} websocket The WebSocket instance\n * @param {(http.ClientRequest|net.Socket|tls.Socket)} stream The request to\n *     abort or the socket to destroy\n * @param {String} message The error message\n * @private\n */ function abortHandshake(websocket, stream, message) {\n    websocket._readyState = WebSocket.CLOSING;\n    const err = new Error(message);\n    Error.captureStackTrace(err, abortHandshake);\n    if (stream.setHeader) {\n        stream[kAborted] = true;\n        stream.abort();\n        if (stream.socket && !stream.socket.destroyed) {\n            //\n            // On Node.js >= 14.3.0 `request.abort()` does not destroy the socket if\n            // called after the request completed. See\n            // https://github.com/websockets/ws/issues/1869.\n            //\n            stream.socket.destroy();\n        }\n        process.nextTick(emitErrorAndClose, websocket, err);\n    } else {\n        stream.destroy(err);\n        stream.once(\"error\", websocket.emit.bind(websocket, \"error\"));\n        stream.once(\"close\", websocket.emitClose.bind(websocket));\n    }\n}\n/**\n * Handle cases where the `ping()`, `pong()`, or `send()` methods are called\n * when the `readyState` attribute is `CLOSING` or `CLOSED`.\n *\n * @param {WebSocket} websocket The WebSocket instance\n * @param {*} [data] The data to send\n * @param {Function} [cb] Callback\n * @private\n */ function sendAfterClose(websocket, data, cb) {\n    if (data) {\n        const length = isBlob(data) ? data.size : toBuffer(data).length;\n        //\n        // The `_bufferedAmount` property is used only when the peer is a client and\n        // the opening handshake fails. Under these circumstances, in fact, the\n        // `setSocket()` method is not called, so the `_socket` and `_sender`\n        // properties are set to `null`.\n        //\n        if (websocket._socket) websocket._sender._bufferedBytes += length;\n        else websocket._bufferedAmount += length;\n    }\n    if (cb) {\n        const err = new Error(`WebSocket is not open: readyState ${websocket.readyState} ` + `(${readyStates[websocket.readyState]})`);\n        process.nextTick(cb, err);\n    }\n}\n/**\n * The listener of the `Receiver` `'conclude'` event.\n *\n * @param {Number} code The status code\n * @param {Buffer} reason The reason for closing\n * @private\n */ function receiverOnConclude(code, reason) {\n    const websocket = this[kWebSocket];\n    websocket._closeFrameReceived = true;\n    websocket._closeMessage = reason;\n    websocket._closeCode = code;\n    if (websocket._socket[kWebSocket] === undefined) return;\n    websocket._socket.removeListener(\"data\", socketOnData);\n    process.nextTick(resume, websocket._socket);\n    if (code === 1005) websocket.close();\n    else websocket.close(code, reason);\n}\n/**\n * The listener of the `Receiver` `'drain'` event.\n *\n * @private\n */ function receiverOnDrain() {\n    const websocket = this[kWebSocket];\n    if (!websocket.isPaused) websocket._socket.resume();\n}\n/**\n * The listener of the `Receiver` `'error'` event.\n *\n * @param {(RangeError|Error)} err The emitted error\n * @private\n */ function receiverOnError(err) {\n    const websocket = this[kWebSocket];\n    if (websocket._socket[kWebSocket] !== undefined) {\n        websocket._socket.removeListener(\"data\", socketOnData);\n        //\n        // On Node.js < 14.0.0 the `'error'` event is emitted synchronously. See\n        // https://github.com/websockets/ws/issues/1940.\n        //\n        process.nextTick(resume, websocket._socket);\n        websocket.close(err[kStatusCode]);\n    }\n    if (!websocket._errorEmitted) {\n        websocket._errorEmitted = true;\n        websocket.emit(\"error\", err);\n    }\n}\n/**\n * The listener of the `Receiver` `'finish'` event.\n *\n * @private\n */ function receiverOnFinish() {\n    this[kWebSocket].emitClose();\n}\n/**\n * The listener of the `Receiver` `'message'` event.\n *\n * @param {Buffer|ArrayBuffer|Buffer[])} data The message\n * @param {Boolean} isBinary Specifies whether the message is binary or not\n * @private\n */ function receiverOnMessage(data, isBinary) {\n    this[kWebSocket].emit(\"message\", data, isBinary);\n}\n/**\n * The listener of the `Receiver` `'ping'` event.\n *\n * @param {Buffer} data The data included in the ping frame\n * @private\n */ function receiverOnPing(data) {\n    const websocket = this[kWebSocket];\n    if (websocket._autoPong) websocket.pong(data, !this._isServer, NOOP);\n    websocket.emit(\"ping\", data);\n}\n/**\n * The listener of the `Receiver` `'pong'` event.\n *\n * @param {Buffer} data The data included in the pong frame\n * @private\n */ function receiverOnPong(data) {\n    this[kWebSocket].emit(\"pong\", data);\n}\n/**\n * Resume a readable stream\n *\n * @param {Readable} stream The readable stream\n * @private\n */ function resume(stream) {\n    stream.resume();\n}\n/**\n * The `Sender` error event handler.\n *\n * @param {Error} The error\n * @private\n */ function senderOnError(err) {\n    const websocket = this[kWebSocket];\n    if (websocket.readyState === WebSocket.CLOSED) return;\n    if (websocket.readyState === WebSocket.OPEN) {\n        websocket._readyState = WebSocket.CLOSING;\n        setCloseTimer(websocket);\n    }\n    //\n    // `socket.end()` is used instead of `socket.destroy()` to allow the other\n    // peer to finish sending queued data. There is no need to set a timer here\n    // because `CLOSING` means that it is already set or not needed.\n    //\n    this._socket.end();\n    if (!websocket._errorEmitted) {\n        websocket._errorEmitted = true;\n        websocket.emit(\"error\", err);\n    }\n}\n/**\n * Set a timer to destroy the underlying raw socket of a WebSocket.\n *\n * @param {WebSocket} websocket The WebSocket instance\n * @private\n */ function setCloseTimer(websocket) {\n    websocket._closeTimer = setTimeout(websocket._socket.destroy.bind(websocket._socket), closeTimeout);\n}\n/**\n * The listener of the socket `'close'` event.\n *\n * @private\n */ function socketOnClose() {\n    const websocket = this[kWebSocket];\n    this.removeListener(\"close\", socketOnClose);\n    this.removeListener(\"data\", socketOnData);\n    this.removeListener(\"end\", socketOnEnd);\n    websocket._readyState = WebSocket.CLOSING;\n    let chunk;\n    //\n    // The close frame might not have been received or the `'end'` event emitted,\n    // for example, if the socket was destroyed due to an error. Ensure that the\n    // `receiver` stream is closed after writing any remaining buffered data to\n    // it. If the readable side of the socket is in flowing mode then there is no\n    // buffered data as everything has been already written and `readable.read()`\n    // will return `null`. If instead, the socket is paused, any possible buffered\n    // data will be read as a single chunk.\n    //\n    if (!this._readableState.endEmitted && !websocket._closeFrameReceived && !websocket._receiver._writableState.errorEmitted && (chunk = websocket._socket.read()) !== null) {\n        websocket._receiver.write(chunk);\n    }\n    websocket._receiver.end();\n    this[kWebSocket] = undefined;\n    clearTimeout(websocket._closeTimer);\n    if (websocket._receiver._writableState.finished || websocket._receiver._writableState.errorEmitted) {\n        websocket.emitClose();\n    } else {\n        websocket._receiver.on(\"error\", receiverOnFinish);\n        websocket._receiver.on(\"finish\", receiverOnFinish);\n    }\n}\n/**\n * The listener of the socket `'data'` event.\n *\n * @param {Buffer} chunk A chunk of data\n * @private\n */ function socketOnData(chunk) {\n    if (!this[kWebSocket]._receiver.write(chunk)) {\n        this.pause();\n    }\n}\n/**\n * The listener of the socket `'end'` event.\n *\n * @private\n */ function socketOnEnd() {\n    const websocket = this[kWebSocket];\n    websocket._readyState = WebSocket.CLOSING;\n    websocket._receiver.end();\n    this.end();\n}\n/**\n * The listener of the socket `'error'` event.\n *\n * @private\n */ function socketOnError() {\n    const websocket = this[kWebSocket];\n    this.removeListener(\"error\", socketOnError);\n    this.on(\"error\", NOOP);\n    if (websocket) {\n        websocket._readyState = WebSocket.CLOSING;\n        this.destroy();\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ws/lib/websocket.js\n");

/***/ })

};
;