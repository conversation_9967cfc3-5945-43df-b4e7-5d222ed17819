<?php
session_start();

if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit();
}

include '../config/database.php';

$student_id = (int)($_GET['id'] ?? 0);
$error = '';
$success = '';

if (!$student_id) {
    header('Location: list.php?error=invalid_id');
    exit();
}

// Get student data
$sql = "SELECT * FROM students WHERE id = ?";
if ($_SESSION['user_role'] !== 'admin') {
    $sql .= " AND created_by = ?";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$student_id, $_SESSION['user_id']]);
} else {
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$student_id]);
}

$student = $stmt->fetch();

if (!$student) {
    header('Location: list.php?error=not_found');
    exit();
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $student_name = sanitizeInput($_POST['student_name']);
    $father_name = sanitizeInput($_POST['father_name']);
    $class = (int)$_POST['class'];
    $section = sanitizeInput($_POST['section']);
    $address = sanitizeInput($_POST['address']);
    $mobile_number = sanitizeInput($_POST['mobile_number']);
    $gender = sanitizeInput($_POST['gender']);
    $admission_date = $_POST['admission_date'];
    $status = sanitizeInput($_POST['status']);
    
    // Validation
    if (empty($student_name) || empty($father_name) || empty($class) || empty($section)) {
        $error = 'Please fill in all required fields.';
    } elseif ($class < 1 || $class > 12) {
        $error = 'Class must be between 1 and 12.';
    } elseif (!in_array($section, ['A', 'B', 'C'])) {
        $error = 'Section must be A, B, or C.';
    } elseif (!empty($mobile_number) && !preg_match('/^[0-9]{10}$/', $mobile_number)) {
        $error = 'Mobile number must be exactly 10 digits.';
    } else {
        try {
            $photo_path = $student['photo_path']; // Keep existing photo by default
            
            // Handle photo upload
            if (isset($_FILES['photo']) && $_FILES['photo']['error'] === UPLOAD_ERR_OK) {
                $allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
                $file_type = $_FILES['photo']['type'];
                $file_size = $_FILES['photo']['size'];
                
                if (!in_array($file_type, $allowed_types)) {
                    $error = 'Only JPG, PNG, and GIF images are allowed.';
                } elseif ($file_size > 5 * 1024 * 1024) {
                    $error = 'Image size must be less than 5MB.';
                } else {
                    // Delete old photo
                    if ($student['photo_path'] && file_exists('../uploads/students/' . $student['photo_path'])) {
                        unlink('../uploads/students/' . $student['photo_path']);
                    }
                    
                    $file_extension = pathinfo($_FILES['photo']['name'], PATHINFO_EXTENSION);
                    $photo_path = 'student_' . time() . '_' . rand(1000, 9999) . '.' . $file_extension;
                    $upload_path = '../uploads/students/' . $photo_path;
                    
                    if (!move_uploaded_file($_FILES['photo']['tmp_name'], $upload_path)) {
                        $error = 'Failed to upload photo.';
                    }
                }
            }
            
            if (!$error) {
                // Update student
                $stmt = $pdo->prepare("
                    UPDATE students 
                    SET student_name = ?, father_name = ?, class = ?, section = ?, address = ?, 
                        mobile_number = ?, gender = ?, photo_path = ?, admission_date = ?, 
                        status = ?, updated_at = NOW()
                    WHERE id = ?
                ");
                
                $stmt->execute([
                    $student_name, $father_name, $class, $section, $address, 
                    $mobile_number ?: null, $gender ?: null, $photo_path, 
                    $admission_date ?: null, $status, $student_id
                ]);
                
                // Log activity
                logActivity($pdo, $_SESSION['user_id'], 'STUDENT_UPDATED', 'STUDENT', $student_id, 
                           "Updated student: $student_name");
                
                $success = 'Student updated successfully!';
                
                // Refresh student data
                $student = $pdo->prepare("SELECT * FROM students WHERE id = ?")->execute([$student_id]);
                $student = $stmt->fetch();
            }
        } catch (PDOException $e) {
            $error = 'Database error: ' . $e->getMessage();
        }
    }
}

include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include '../includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">Edit Student</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <a href="view.php?id=<?php echo $student['id']; ?>" class="btn btn-sm btn-outline-info">
                            <i class="fas fa-eye"></i> View
                        </a>
                        <a href="list.php" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> Back to List
                        </a>
                    </div>
                </div>
            </div>

            <?php if ($error): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-triangle"></i> <?php echo $error; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle"></i> <?php echo $success; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <div class="row">
                <div class="col-lg-8">
                    <div class="card shadow">
                        <div class="card-header bg-warning text-dark">
                            <h5 class="mb-0">
                                <i class="fas fa-edit"></i> Edit Student Information
                            </h5>
                        </div>
                        <div class="card-body">
                            <form method="POST" enctype="multipart/form-data" id="editStudentForm">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="student_name" class="form-label">
                                            <i class="fas fa-user"></i> Student Name <span class="text-danger">*</span>
                                        </label>
                                        <input type="text" class="form-control" id="student_name" name="student_name" 
                                               value="<?php echo htmlspecialchars($student['student_name']); ?>" 
                                               required autocomplete="name">
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="father_name" class="form-label">
                                            <i class="fas fa-male"></i> Father's Name <span class="text-danger">*</span>
                                        </label>
                                        <input type="text" class="form-control" id="father_name" name="father_name" 
                                               value="<?php echo htmlspecialchars($student['father_name']); ?>" 
                                               required autocomplete="name">
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-4 mb-3">
                                        <label for="class" class="form-label">
                                            <i class="fas fa-graduation-cap"></i> Class <span class="text-danger">*</span>
                                        </label>
                                        <select class="form-select" id="class" name="class" required>
                                            <?php for ($i = 1; $i <= 12; $i++): ?>
                                                <option value="<?php echo $i; ?>" <?php echo $student['class'] == $i ? 'selected' : ''; ?>>
                                                    Class <?php echo $i; ?>
                                                </option>
                                            <?php endfor; ?>
                                        </select>
                                    </div>
                                    
                                    <div class="col-md-4 mb-3">
                                        <label for="section" class="form-label">
                                            <i class="fas fa-layer-group"></i> Section <span class="text-danger">*</span>
                                        </label>
                                        <select class="form-select" id="section" name="section" required>
                                            <option value="A" <?php echo $student['section'] == 'A' ? 'selected' : ''; ?>>Section A</option>
                                            <option value="B" <?php echo $student['section'] == 'B' ? 'selected' : ''; ?>>Section B</option>
                                            <option value="C" <?php echo $student['section'] == 'C' ? 'selected' : ''; ?>>Section C</option>
                                        </select>
                                    </div>
                                    
                                    <div class="col-md-4 mb-3">
                                        <label for="gender" class="form-label">
                                            <i class="fas fa-venus-mars"></i> Gender
                                        </label>
                                        <select class="form-select" id="gender" name="gender">
                                            <option value="">Select Gender</option>
                                            <option value="Male" <?php echo $student['gender'] == 'Male' ? 'selected' : ''; ?>>Male</option>
                                            <option value="Female" <?php echo $student['gender'] == 'Female' ? 'selected' : ''; ?>>Female</option>
                                            <option value="Other" <?php echo $student['gender'] == 'Other' ? 'selected' : ''; ?>>Other</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="mobile_number" class="form-label">
                                            <i class="fas fa-mobile-alt"></i> Mobile Number
                                        </label>
                                        <input type="tel" class="form-control" id="mobile_number" name="mobile_number" 
                                               value="<?php echo htmlspecialchars($student['mobile_number']); ?>" 
                                               pattern="[0-9]{10}" maxlength="10" 
                                               placeholder="Enter 10-digit mobile number"
                                               autocomplete="tel">
                                        <div class="form-text">
                                            <i class="fas fa-info-circle"></i> Enter exactly 10 digits
                                        </div>
                                        <div class="invalid-feedback">Mobile number must be exactly 10 digits.</div>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="admission_date" class="form-label">
                                            <i class="fas fa-calendar-alt"></i> Admission Date
                                        </label>
                                        <input type="date" class="form-control" id="admission_date" name="admission_date" 
                                               value="<?php echo $student['admission_date']; ?>">
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="status" class="form-label">
                                            <i class="fas fa-flag"></i> Status
                                        </label>
                                        <select class="form-select" id="status" name="status">
                                            <option value="Active" <?php echo $student['status'] == 'Active' ? 'selected' : ''; ?>>Active</option>
                                            <option value="Left" <?php echo $student['status'] == 'Left' ? 'selected' : ''; ?>>Left</option>
                                            <option value="Transferred" <?php echo $student['status'] == 'Transferred' ? 'selected' : ''; ?>>Transferred</option>
                                        </select>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="photo" class="form-label">
                                            <i class="fas fa-camera"></i> Update Photo
                                        </label>
                                        <input type="file" class="form-control" id="photo" name="photo" 
                                               accept="image/jpeg,image/jpg,image/png,image/gif">
                                        <div class="form-text">
                                            <i class="fas fa-info-circle"></i> Leave empty to keep current photo
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="address" class="form-label">
                                        <i class="fas fa-map-marker-alt"></i> Address
                                    </label>
                                    <textarea class="form-control" id="address" name="address" rows="3" 
                                              placeholder="Enter complete address"><?php echo htmlspecialchars($student['address']); ?></textarea>
                                </div>

                                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                    <a href="view.php?id=<?php echo $student['id']; ?>" class="btn btn-secondary me-md-2">
                                        <i class="fas fa-times"></i> Cancel
                                    </a>
                                    <button type="submit" class="btn btn-warning">
                                        <i class="fas fa-save"></i> Update Student
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-4">
                    <?php if ($student['photo_path']): ?>
                    <div class="card shadow mb-4">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-camera"></i> Current Photo
                            </h6>
                        </div>
                        <div class="card-body text-center">
                            <img src="../uploads/students/<?php echo htmlspecialchars($student['photo_path']); ?>" 
                                 alt="Current Photo" 
                                 class="img-fluid rounded shadow"
                                 style="max-height: 200px;">
                            <p class="text-muted mt-2 mb-0">Upload new photo to replace</p>
                        </div>
                    </div>
                    <?php endif; ?>
                    
                    <div class="card shadow">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-info-circle"></i> Edit Information
                            </h6>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled mb-0">
                                <li class="mb-2">
                                    <i class="fas fa-check text-success"></i> 
                                    Fields marked with <span class="text-danger">*</span> are required
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-mobile-alt text-info"></i> 
                                    Mobile number must be exactly 10 digits
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-camera text-warning"></i> 
                                    Leave photo field empty to keep current photo
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-history text-primary"></i> 
                                    Changes will be logged for audit
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
