<?php
session_start();

if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit();
}

include '../config/database.php';

// Get filter parameters
$class_filter = $_GET['class'] ?? '';
$section_filter = $_GET['section'] ?? '';
$status_filter = $_GET['status'] ?? '';
$format = $_GET['format'] ?? 'detailed'; // 'detailed' or 'summary'

// Build query based on user role
$where_conditions = [];
$params = [];

// If not admin, only export own students
if ($_SESSION['user_role'] !== 'admin') {
    $where_conditions[] = "s.created_by = ?";
    $params[] = $_SESSION['user_id'];
}

// Apply filters
if ($class_filter) {
    $where_conditions[] = "s.class = ?";
    $params[] = $class_filter;
}

if ($section_filter) {
    $where_conditions[] = "s.section = ?";
    $params[] = $section_filter;
}

if ($status_filter) {
    $where_conditions[] = "s.status = ?";
    $params[] = $status_filter;
}

$where_clause = $where_conditions ? "WHERE " . implode(" AND ", $where_conditions) : "";

// Get students with creator info
$sql = "SELECT s.*, u.full_name as created_by_name 
        FROM students s 
        LEFT JOIN users u ON s.created_by = u.id 
        $where_clause 
        ORDER BY s.class, s.section, s.student_name";

$stmt = $pdo->prepare($sql);
$stmt->execute($params);
$students = $stmt->fetchAll();

if (empty($students)) {
    header('Location: ../students/list.php?error=no_data_to_export');
    exit();
}

// Log export activity
$filter_details = [];
if ($class_filter) $filter_details[] = "Class: $class_filter";
if ($section_filter) $filter_details[] = "Section: $section_filter";
if ($status_filter) $filter_details[] = "Status: $status_filter";
$filter_text = $filter_details ? ' (' . implode(', ', $filter_details) . ')' : '';

logActivity($pdo, $_SESSION['user_id'], 'STUDENTS_EXPORTED', 'EXPORT', null, 
           "Exported " . count($students) . " students to Excel" . $filter_text);

// Generate filename
$timestamp = date('Y-m-d_H-i-s');
$filename = "students_export_$timestamp";
if ($class_filter && $section_filter) {
    $filename = "students_class_{$class_filter}_section_{$section_filter}_$timestamp";
} elseif ($class_filter) {
    $filename = "students_class_{$class_filter}_$timestamp";
}
$filename .= '.csv';

// Set headers for CSV download
header('Content-Type: text/csv; charset=utf-8');
header('Content-Disposition: attachment; filename="' . $filename . '"');
header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
header('Expires: 0');

// Create output stream
$output = fopen('php://output', 'w');

// Add BOM for proper UTF-8 encoding in Excel
fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));

// Add headers
if ($format === 'summary') {
    $headers = [
        'S.No',
        'Student Name',
        'Father Name',
        'Class',
        'Section',
        'Mobile Number',
        'Status',
        'Admission Date'
    ];
} else {
    $headers = [
        'S.No',
        'Student Name',
        'Father Name',
        'Class',
        'Section',
        'Address',
        'Mobile Number',
        'Gender',
        'Admission Date',
        'Status',
        'Created By',
        'Created Date',
        'Last Updated'
    ];
}

fputcsv($output, $headers);

// Add data rows
$serial = 1;
foreach ($students as $student) {
    if ($format === 'summary') {
        $row = [
            $serial++,
            $student['student_name'],
            $student['father_name'],
            $student['class'],
            $student['section'],
            $student['mobile_number'] ?: '',
            $student['status'],
            $student['admission_date'] ? date('d-m-Y', strtotime($student['admission_date'])) : ''
        ];
    } else {
        $row = [
            $serial++,
            $student['student_name'],
            $student['father_name'],
            $student['class'],
            $student['section'],
            $student['address'] ?: '',
            $student['mobile_number'] ?: '',
            $student['gender'] ?: '',
            $student['admission_date'] ? date('d-m-Y', strtotime($student['admission_date'])) : '',
            $student['status'],
            $student['created_by_name'] ?: '',
            date('d-m-Y', strtotime($student['created_at'])),
            date('d-m-Y', strtotime($student['updated_at']))
        ];
    }
    
    fputcsv($output, $row);
}

// Add summary footer
fputcsv($output, []);
fputcsv($output, ['Total Students:', count($students)]);
fputcsv($output, ['Export Date:', date('d-m-Y H:i:s')]);
fputcsv($output, ['Exported By:', $_SESSION['full_name']]);

if ($filter_details) {
    fputcsv($output, ['Filters Applied:', implode(', ', $filter_details)]);
}

fclose($output);
exit();
?>
