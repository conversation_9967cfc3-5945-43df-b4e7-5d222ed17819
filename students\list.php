<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit();
}

include '../config/database.php';
include '../includes/header.php';

// Get filter parameters
$class_filter = $_GET['class'] ?? '';
$section_filter = $_GET['section'] ?? '';
$status_filter = $_GET['status'] ?? '';
$search = $_GET['search'] ?? '';

// Build query based on user role
$where_conditions = [];
$params = [];

// If not admin, only show own students
if ($_SESSION['user_role'] !== 'admin') {
    $where_conditions[] = "s.created_by = ?";
    $params[] = $_SESSION['user_id'];
}

// Apply filters
if ($class_filter) {
    $where_conditions[] = "s.class = ?";
    $params[] = $class_filter;
}

if ($section_filter) {
    $where_conditions[] = "s.section = ?";
    $params[] = $section_filter;
}

if ($status_filter) {
    $where_conditions[] = "s.status = ?";
    $params[] = $status_filter;
}

if ($search) {
    $where_conditions[] = "(s.student_name LIKE ? OR s.father_name LIKE ? OR s.mobile_number LIKE ?)";
    $search_term = "%$search%";
    $params[] = $search_term;
    $params[] = $search_term;
    $params[] = $search_term;
}

$where_clause = $where_conditions ? "WHERE " . implode(" AND ", $where_conditions) : "";

// Get students with creator info
$sql = "SELECT s.*, u.full_name as created_by_name 
        FROM students s 
        LEFT JOIN users u ON s.created_by = u.id 
        $where_clause 
        ORDER BY s.created_at DESC";

$stmt = $pdo->prepare($sql);
$stmt->execute($params);
$students = $stmt->fetchAll();
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <?php include '../includes/sidebar.php'; ?>
        
        <!-- Main Content -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">Students Management</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <a href="add.php" class="btn btn-sm btn-primary">
                            <i class="fas fa-plus"></i> Add Student
                        </a>
                        <a href="../export/students.php" class="btn btn-sm btn-success">
                            <i class="fas fa-download"></i> Export
                        </a>
                    </div>
                </div>
            </div>

            <!-- Filters -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-2">
                            <label for="class" class="form-label">Class</label>
                            <select name="class" id="class" class="form-select">
                                <option value="">All Classes</option>
                                <?php for ($i = 1; $i <= 12; $i++): ?>
                                    <option value="<?php echo $i; ?>" <?php echo $class_filter == $i ? 'selected' : ''; ?>>
                                        Class <?php echo $i; ?>
                                    </option>
                                <?php endfor; ?>
                            </select>
                        </div>
                        
                        <div class="col-md-2">
                            <label for="section" class="form-label">Section</label>
                            <select name="section" id="section" class="form-select">
                                <option value="">All Sections</option>
                                <option value="A" <?php echo $section_filter == 'A' ? 'selected' : ''; ?>>Section A</option>
                                <option value="B" <?php echo $section_filter == 'B' ? 'selected' : ''; ?>>Section B</option>
                                <option value="C" <?php echo $section_filter == 'C' ? 'selected' : ''; ?>>Section C</option>
                            </select>
                        </div>
                        
                        <div class="col-md-2">
                            <label for="status" class="form-label">Status</label>
                            <select name="status" id="status" class="form-select">
                                <option value="">All Status</option>
                                <option value="Active" <?php echo $status_filter == 'Active' ? 'selected' : ''; ?>>Active</option>
                                <option value="Left" <?php echo $status_filter == 'Left' ? 'selected' : ''; ?>>Left</option>
                                <option value="Transferred" <?php echo $status_filter == 'Transferred' ? 'selected' : ''; ?>>Transferred</option>
                            </select>
                        </div>
                        
                        <div class="col-md-4">
                            <label for="search" class="form-label">Search</label>
                            <input type="text" name="search" id="search" class="form-control" 
                                   placeholder="Search by name, father's name, or mobile..." 
                                   value="<?php echo htmlspecialchars($search); ?>">
                        </div>
                        
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i> Filter
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Students Table -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-graduation-cap"></i> Students List 
                        <span class="badge bg-primary"><?php echo count($students); ?> students</span>
                    </h5>
                </div>
                <div class="card-body">
                    <?php if ($students): ?>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover data-table">
                                <thead class="table-dark">
                                    <tr>
                                        <th>ID</th>
                                        <th>Student Name</th>
                                        <th>Father's Name</th>
                                        <th>Class</th>
                                        <th>Section</th>
                                        <th>Mobile</th>
                                        <th>Status</th>
                                        <th>Created By</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($students as $student): ?>
                                    <tr>
                                        <td><?php echo $student['id']; ?></td>
                                        <td>
                                            <strong><?php echo htmlspecialchars($student['student_name']); ?></strong>
                                            <?php if ($student['gender']): ?>
                                                <br><small class="text-muted">
                                                    <i class="fas fa-<?php echo $student['gender'] == 'Male' ? 'mars' : ($student['gender'] == 'Female' ? 'venus' : 'genderless'); ?>"></i>
                                                    <?php echo $student['gender']; ?>
                                                </small>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo htmlspecialchars($student['father_name']); ?></td>
                                        <td><span class="badge bg-info">Class <?php echo $student['class']; ?></span></td>
                                        <td><span class="badge bg-secondary">Section <?php echo $student['section']; ?></span></td>
                                        <td>
                                            <?php if ($student['mobile_number']): ?>
                                                <a href="tel:<?php echo $student['mobile_number']; ?>" class="text-decoration-none">
                                                    <i class="fas fa-phone"></i> <?php echo $student['mobile_number']; ?>
                                                </a>
                                            <?php else: ?>
                                                <span class="text-muted">-</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <span class="badge bg-<?php echo $student['status'] == 'Active' ? 'success' : ($student['status'] == 'Left' ? 'danger' : 'warning'); ?>">
                                                <?php echo $student['status']; ?>
                                            </span>
                                        </td>
                                        <td>
                                            <small class="text-muted">
                                                <?php echo htmlspecialchars($student['created_by_name']); ?>
                                                <br><?php echo date('M j, Y', strtotime($student['created_at'])); ?>
                                            </small>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm" role="group">
                                                <a href="view.php?id=<?php echo $student['id']; ?>" 
                                                   class="btn btn-outline-info" title="View">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                
                                                <?php if ($_SESSION['user_role'] === 'admin' || $student['created_by'] == $_SESSION['user_id']): ?>
                                                <a href="edit.php?id=<?php echo $student['id']; ?>" 
                                                   class="btn btn-outline-warning" title="Edit">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a href="delete.php?id=<?php echo $student['id']; ?>" 
                                                   class="btn btn-outline-danger delete-btn" title="Delete">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-5">
                            <i class="fas fa-graduation-cap fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No students found</h5>
                            <p class="text-muted">Start by adding your first student.</p>
                            <a href="add.php" class="btn btn-primary">
                                <i class="fas fa-plus"></i> Add Student
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </main>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
