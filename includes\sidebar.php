<nav id="sidebarMenu" class="col-md-3 col-lg-2 d-md-block sidebar collapse">
    <div class="position-sticky pt-3">
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'index.php' ? 'active' : ''; ?>" 
                   href="<?php echo dirname($_SERVER['PHP_SELF']) == '/' ? '' : '../'; ?>index.php">
                    <i class="fas fa-tachometer-alt"></i> Dashboard
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link <?php echo strpos($_SERVER['PHP_SELF'], 'students') !== false ? 'active' : ''; ?>" 
                   href="<?php echo dirname($_SERVER['PHP_SELF']) == '/' ? '' : '../'; ?>students/list.php">
                    <i class="fas fa-graduation-cap"></i> Students
                </a>
            </li>
            
            <?php if (isset($_SESSION['user_role']) && $_SESSION['user_role'] === 'admin'): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo strpos($_SERVER['PHP_SELF'], 'users') !== false ? 'active' : ''; ?>" 
                   href="<?php echo dirname($_SERVER['PHP_SELF']) == '/' ? '' : '../'; ?>users/list.php">
                    <i class="fas fa-users"></i> Users
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link <?php echo strpos($_SERVER['PHP_SELF'], 'activity') !== false ? 'active' : ''; ?>" 
                   href="<?php echo dirname($_SERVER['PHP_SELF']) == '/' ? '' : '../'; ?>activity/logs.php">
                    <i class="fas fa-clipboard-list"></i> Activity Logs
                </a>
            </li>
            <?php endif; ?>
        </ul>

        <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
            <span>Quick Actions</span>
        </h6>
        
        <ul class="nav flex-column mb-2">
            <li class="nav-item">
                <a class="nav-link" href="<?php echo dirname($_SERVER['PHP_SELF']) == '/' ? '' : '../'; ?>students/add.php">
                    <i class="fas fa-plus"></i> Add Student
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link" href="<?php echo dirname($_SERVER['PHP_SELF']) == '/' ? '' : '../'; ?>export/students.php">
                    <i class="fas fa-download"></i> Export Data
                </a>
            </li>
            
            <?php if (isset($_SESSION['user_role']) && $_SESSION['user_role'] === 'admin'): ?>
            <li class="nav-item">
                <a class="nav-link" href="<?php echo dirname($_SERVER['PHP_SELF']) == '/' ? '' : '../'; ?>users/add.php">
                    <i class="fas fa-user-plus"></i> Add User
                </a>
            </li>
            <?php endif; ?>
        </ul>

        <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
            <span>Account</span>
        </h6>
        
        <ul class="nav flex-column mb-2">
            <li class="nav-item">
                <a class="nav-link" href="<?php echo dirname($_SERVER['PHP_SELF']) == '/' ? '' : '../'; ?>profile.php">
                    <i class="fas fa-user-circle"></i> My Profile
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link" href="<?php echo dirname($_SERVER['PHP_SELF']) == '/' ? '' : '../'; ?>logout.php">
                    <i class="fas fa-sign-out-alt"></i> Logout
                </a>
            </li>
        </ul>
        
        <!-- User Info -->
        <div class="px-3 mt-4">
            <div class="card bg-transparent border-light">
                <div class="card-body text-center py-2">
                    <div class="text-white">
                        <i class="fas fa-user-circle fa-2x mb-2"></i>
                        <h6 class="mb-1"><?php echo htmlspecialchars($_SESSION['full_name'] ?? 'User'); ?></h6>
                        <small class="text-light">
                            <i class="fas fa-shield-alt"></i> 
                            <?php echo ucfirst($_SESSION['user_role'] ?? 'user'); ?>
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</nav>
