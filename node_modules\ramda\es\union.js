import _concat from "./internal/_concat.js";
import _curry2 from "./internal/_curry2.js";
import compose from "./compose.js";
import uniq from "./uniq.js";
/**
 * Combines two lists into a set (i.e. no duplicates) composed of the elements
 * of each list.
 *
 * @func
 * @memberOf R
 * @since v0.1.0
 * @category Relation
 * @sig [*] -> [*] -> [*]
 * @param {Array} as The first list.
 * @param {Array} bs The second list.
 * @return {Array} The first and second lists concatenated, with
 *         duplicates removed.
 * @example
 *
 *      R.union([1, 2, 3], [2, 3, 4]); //=> [1, 2, 3, 4]
 */

var union =
/*#__PURE__*/
_curry2(
/*#__PURE__*/
compose(uniq, _concat));

export default union;