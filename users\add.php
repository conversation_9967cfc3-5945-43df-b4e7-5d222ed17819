<?php
session_start();

if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit();
}

// Check admin access
if ($_SESSION['user_role'] !== 'admin') {
    header('Location: ../index.php?error=access_denied');
    exit();
}

include '../config/database.php';

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = sanitizeInput($_POST['username']);
    $email = sanitizeInput($_POST['email']);
    $full_name = sanitizeInput($_POST['full_name']);
    $password = $_POST['password'];
    $confirm_password = $_POST['confirm_password'];
    $role = sanitizeInput($_POST['role']);
    $is_active = isset($_POST['is_active']) ? 1 : 0;
    
    // Validation
    if (empty($username) || empty($email) || empty($full_name) || empty($password) || empty($role)) {
        $error = 'Please fill in all required fields.';
    } elseif (!validateEmail($email)) {
        $error = 'Please enter a valid email address.';
    } elseif (strlen($password) < 6) {
        $error = 'Password must be at least 6 characters long.';
    } elseif ($password !== $confirm_password) {
        $error = 'Password and confirmation do not match.';
    } elseif (!in_array($role, ['admin', 'user'])) {
        $error = 'Invalid role selected.';
    } else {
        try {
            // Check if username or email already exists
            $stmt = $pdo->prepare("SELECT id FROM users WHERE username = ? OR email = ?");
            $stmt->execute([$username, $email]);
            
            if ($stmt->fetch()) {
                $error = 'Username or email already exists.';
            } else {
                // Create user
                $hashed_password = hashPassword($password);
                $stmt = $pdo->prepare("
                    INSERT INTO users (username, email, password, full_name, role, is_active) 
                    VALUES (?, ?, ?, ?, ?, ?)
                ");
                
                $stmt->execute([$username, $email, $hashed_password, $full_name, $role, $is_active]);
                $user_id = $pdo->lastInsertId();
                
                // Log activity
                logActivity($pdo, $_SESSION['user_id'], 'USER_CREATED', 'USER', $user_id, 
                           "Created user: $username ($role)");
                
                $success = 'User created successfully!';
                
                // Clear form data
                $_POST = [];
            }
        } catch (PDOException $e) {
            $error = 'Database error: ' . $e->getMessage();
        }
    }
}

include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include '../includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">Add New User</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <a href="list.php" class="btn btn-sm btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Users
                    </a>
                </div>
            </div>

            <?php if ($error): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-triangle"></i> <?php echo $error; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle"></i> <?php echo $success; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <div class="row">
                <div class="col-lg-8">
                    <div class="card shadow">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-user-plus"></i> User Information
                            </h5>
                        </div>
                        <div class="card-body">
                            <form method="POST" id="addUserForm">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="username" class="form-label">
                                            <i class="fas fa-at"></i> Username <span class="text-danger">*</span>
                                        </label>
                                        <input type="text" class="form-control" id="username" name="username" 
                                               value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>" 
                                               required autocomplete="username">
                                        <div class="form-text">Username must be unique and cannot be changed later</div>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="email" class="form-label">
                                            <i class="fas fa-envelope"></i> Email Address <span class="text-danger">*</span>
                                        </label>
                                        <input type="email" class="form-control" id="email" name="email" 
                                               value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>" 
                                               required autocomplete="email">
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="full_name" class="form-label">
                                        <i class="fas fa-user"></i> Full Name <span class="text-danger">*</span>
                                    </label>
                                    <input type="text" class="form-control" id="full_name" name="full_name" 
                                           value="<?php echo htmlspecialchars($_POST['full_name'] ?? ''); ?>" 
                                           required autocomplete="name">
                                </div>

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="password" class="form-label">
                                            <i class="fas fa-lock"></i> Password <span class="text-danger">*</span>
                                        </label>
                                        <input type="password" class="form-control" id="password" name="password" 
                                               required autocomplete="new-password" minlength="6">
                                        <div class="form-text">Minimum 6 characters</div>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="confirm_password" class="form-label">
                                            <i class="fas fa-lock"></i> Confirm Password <span class="text-danger">*</span>
                                        </label>
                                        <input type="password" class="form-control" id="confirm_password" name="confirm_password" 
                                               required autocomplete="new-password" minlength="6">
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="role" class="form-label">
                                            <i class="fas fa-shield-alt"></i> Role <span class="text-danger">*</span>
                                        </label>
                                        <select class="form-select" id="role" name="role" required>
                                            <option value="">Select Role</option>
                                            <option value="user" <?php echo (($_POST['role'] ?? '') == 'user') ? 'selected' : ''; ?>>
                                                User (Can manage own students)
                                            </option>
                                            <option value="admin" <?php echo (($_POST['role'] ?? '') == 'admin') ? 'selected' : ''; ?>>
                                                Admin (Full system access)
                                            </option>
                                        </select>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">
                                            <i class="fas fa-toggle-on"></i> Account Status
                                        </label>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                                   <?php echo (($_POST['is_active'] ?? true)) ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="is_active">
                                                Account is active (user can login)
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                    <a href="list.php" class="btn btn-secondary me-md-2">
                                        <i class="fas fa-times"></i> Cancel
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save"></i> Create User
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-4">
                    <div class="card shadow">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-info-circle"></i> User Roles
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <h6 class="text-primary">
                                    <i class="fas fa-user"></i> User Role
                                </h6>
                                <ul class="list-unstyled mb-0 small">
                                    <li><i class="fas fa-check text-success"></i> Add/edit/delete own students</li>
                                    <li><i class="fas fa-check text-success"></i> View own student data</li>
                                    <li><i class="fas fa-check text-success"></i> Export own data</li>
                                    <li><i class="fas fa-check text-success"></i> Update own profile</li>
                                </ul>
                            </div>
                            
                            <div class="mb-3">
                                <h6 class="text-danger">
                                    <i class="fas fa-shield-alt"></i> Admin Role
                                </h6>
                                <ul class="list-unstyled mb-0 small">
                                    <li><i class="fas fa-check text-success"></i> All user permissions</li>
                                    <li><i class="fas fa-check text-success"></i> View all student data</li>
                                    <li><i class="fas fa-check text-success"></i> Manage users</li>
                                    <li><i class="fas fa-check text-success"></i> View activity logs</li>
                                    <li><i class="fas fa-check text-success"></i> System administration</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card shadow mt-3">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-lightbulb"></i> Tips
                            </h6>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled mb-0 small">
                                <li class="mb-2">
                                    <i class="fas fa-key text-warning"></i> 
                                    Use strong passwords for security
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-envelope text-info"></i> 
                                    Email must be unique in the system
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-user-shield text-primary"></i> 
                                    Admin role should be given carefully
                                </li>
                                <li class="mb-0">
                                    <i class="fas fa-toggle-off text-muted"></i> 
                                    Inactive users cannot login
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Password confirmation validation
    const password = document.getElementById('password');
    const confirmPassword = document.getElementById('confirm_password');
    
    function validatePassword() {
        if (password.value !== confirmPassword.value) {
            confirmPassword.setCustomValidity('Passwords do not match');
        } else {
            confirmPassword.setCustomValidity('');
        }
    }
    
    password.addEventListener('input', validatePassword);
    confirmPassword.addEventListener('input', validatePassword);
});
</script>

<?php include '../includes/footer.php'; ?>
