<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>File List - Student Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; padding: 20px; }
        .file-card { background: rgba(255, 255, 255, 0.95); border-radius: 15px; box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1); }
        .file-header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 15px 15px 0 0; }
        .file-item { padding: 10px; border-bottom: 1px solid #eee; transition: background 0.3s; }
        .file-item:hover { background: #f8f9fa; }
        .file-item:last-child { border-bottom: none; }
        .folder-icon { color: #ffc107; }
        .file-icon { color: #007bff; }
    </style>
</head>
<body>
    <div class="container">
        <div class="file-card">
            <div class="file-header text-center py-4">
                <i class="fas fa-folder-open fa-3x mb-3"></i>
                <h2>Student Management System Files</h2>
                <p class="mb-1">🌐 Live Website: <strong>https://voicebirdtv.in/Student/</strong></p>
                <p class="mb-0">All files and folders are available</p>
            </div>
            
            <div class="p-4">
                <div class="row">
                    <div class="col-md-6">
                        <h5><i class="fas fa-file-code"></i> Main Files</h5>
                        <div class="file-item">
                            <i class="fas fa-file-code file-icon"></i>
                            <a href="index.php" class="text-decoration-none ms-2">index.php</a>
                            <small class="text-muted ms-2">- Dashboard</small>
                        </div>
                        <div class="file-item">
                            <i class="fas fa-file-code file-icon"></i>
                            <a href="login.php" class="text-decoration-none ms-2">login.php</a>
                            <small class="text-muted ms-2">- Login page</small>
                        </div>
                        <div class="file-item">
                            <i class="fas fa-file-code file-icon"></i>
                            <a href="profile.php" class="text-decoration-none ms-2">profile.php</a>
                            <small class="text-muted ms-2">- User profile</small>
                        </div>
                        <div class="file-item">
                            <i class="fas fa-file-code file-icon"></i>
                            <a href="mobile-demo.php" class="text-decoration-none ms-2">mobile-demo.php</a>
                            <small class="text-muted ms-2">- Mobile features demo</small>
                        </div>
                        <div class="file-item">
                            <i class="fas fa-file-code file-icon"></i>
                            <a href="mobile-test.php" class="text-decoration-none ms-2">mobile-test.php</a>
                            <small class="text-muted ms-2">- Mobile input test</small>
                        </div>
                        
                        <h5 class="mt-4"><i class="fas fa-folder folder-icon"></i> Students Module</h5>
                        <div class="file-item">
                            <i class="fas fa-file-code file-icon"></i>
                            <a href="students/list.php" class="text-decoration-none ms-2">students/list.php</a>
                            <small class="text-muted ms-2">- Student listing</small>
                        </div>
                        <div class="file-item">
                            <i class="fas fa-file-code file-icon"></i>
                            <a href="students/add.php" class="text-decoration-none ms-2">students/add.php</a>
                            <small class="text-muted ms-2">- Add student</small>
                        </div>
                        <div class="file-item">
                            <i class="fas fa-file-code file-icon"></i>
                            <span class="ms-2">students/edit.php</span>
                            <small class="text-muted ms-2">- Edit student</small>
                        </div>
                        <div class="file-item">
                            <i class="fas fa-file-code file-icon"></i>
                            <span class="ms-2">students/view.php</span>
                            <small class="text-muted ms-2">- View student</small>
                        </div>
                        <div class="file-item">
                            <i class="fas fa-file-code file-icon"></i>
                            <span class="ms-2">students/delete.php</span>
                            <small class="text-muted ms-2">- Delete student</small>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <h5><i class="fas fa-folder folder-icon"></i> Configuration</h5>
                        <div class="file-item">
                            <i class="fas fa-file-code file-icon"></i>
                            <span class="ms-2">config/database.php</span>
                            <small class="text-muted ms-2">- Database config</small>
                        </div>
                        
                        <h5 class="mt-4"><i class="fas fa-folder folder-icon"></i> Users Module (Admin)</h5>
                        <div class="file-item">
                            <i class="fas fa-file-code file-icon"></i>
                            <span class="ms-2">users/list.php</span>
                            <small class="text-muted ms-2">- User management</small>
                        </div>
                        <div class="file-item">
                            <i class="fas fa-file-code file-icon"></i>
                            <span class="ms-2">users/add.php</span>
                            <small class="text-muted ms-2">- Add new user</small>
                        </div>
                        
                        <h5 class="mt-4"><i class="fas fa-folder folder-icon"></i> Other Modules</h5>
                        <div class="file-item">
                            <i class="fas fa-file-code file-icon"></i>
                            <span class="ms-2">activity/logs.php</span>
                            <small class="text-muted ms-2">- Activity logs</small>
                        </div>
                        <div class="file-item">
                            <i class="fas fa-file-code file-icon"></i>
                            <span class="ms-2">export/students.php</span>
                            <small class="text-muted ms-2">- Excel export</small>
                        </div>
                        
                        <h5 class="mt-4"><i class="fas fa-folder folder-icon"></i> Setup & Utils</h5>
                        <div class="file-item">
                            <i class="fas fa-file-database file-icon"></i>
                            <span class="ms-2">setup/database.sql</span>
                            <small class="text-muted ms-2">- Database schema</small>
                        </div>
                        <div class="file-item">
                            <i class="fas fa-file-code file-icon"></i>
                            <a href="fix-passwords.php" class="text-decoration-none ms-2">fix-passwords.php</a>
                            <small class="text-muted ms-2">- Fix login passwords</small>
                        </div>
                        
                        <h5 class="mt-4"><i class="fas fa-folder folder-icon"></i> Includes</h5>
                        <div class="file-item">
                            <i class="fas fa-file-code file-icon"></i>
                            <span class="ms-2">includes/header.php</span>
                            <small class="text-muted ms-2">- Page header</small>
                        </div>
                        <div class="file-item">
                            <i class="fas fa-file-code file-icon"></i>
                            <span class="ms-2">includes/sidebar.php</span>
                            <small class="text-muted ms-2">- Navigation</small>
                        </div>
                        <div class="file-item">
                            <i class="fas fa-file-code file-icon"></i>
                            <span class="ms-2">includes/footer.php</span>
                            <small class="text-muted ms-2">- Page footer</small>
                        </div>
                    </div>
                </div>
                
                <div class="alert alert-info mt-4">
                    <h6><i class="fas fa-info-circle"></i> Setup Instructions:</h6>
                    <ol class="mb-0">
                        <li><strong>Import Database:</strong> Upload <code>setup/database.sql</code> to your database via phpMyAdmin</li>
                        <li><strong>Fix Passwords:</strong> Run <a href="fix-passwords.php">fix-passwords.php</a> to set correct login passwords</li>
                        <li><strong>Login:</strong> Use <code>admin/admin123</code> or <code>user1/user123</code></li>
                        <li><strong>Test Mobile:</strong> Try <a href="mobile-demo.php">mobile-demo.php</a> on your phone</li>
                    </ol>
                </div>
                
                <div class="alert alert-success mt-3">
                    <h6><i class="fas fa-check-circle"></i> Database Configuration:</h6>
                    <p class="mb-1"><strong>Database:</strong> u579681058_SMS</p>
                    <p class="mb-1"><strong>Username:</strong> u579681058_SMS</p>
                    <p class="mb-0"><strong>Status:</strong> ✅ Configured in config/database.php</p>
                </div>
                
                <div class="text-center mt-4">
                    <a href="login.php" class="btn btn-primary me-2">
                        <i class="fas fa-sign-in-alt"></i> Go to Login
                    </a>
                    <a href="fix-passwords.php" class="btn btn-warning me-2">
                        <i class="fas fa-key"></i> Fix Passwords
                    </a>
                    <a href="mobile-demo.php" class="btn btn-info">
                        <i class="fas fa-mobile-alt"></i> Mobile Demo
                    </a>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
