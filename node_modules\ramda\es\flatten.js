import _curry1 from "./internal/_curry1.js";
import _makeFlat from "./internal/_makeFlat.js";
/**
 * Returns a new list by pulling every item out of it (and all its sub-arrays)
 * and putting them in a new array, depth-first.
 *
 * @func
 * @memberOf R
 * @since v0.1.0
 * @category List
 * @sig [a] -> [b]
 * @param {Array} list The array to consider.
 * @return {Array} The flattened list.
 * @see <PERSON><PERSON>unnest
 * @example
 *
 *      <PERSON><PERSON>flatten([1, 2, [3, 4], 5, [6, [7, 8, [9, [10, 11], 12]]]]);
 *      //=> [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]
 */

var flatten =
/*#__PURE__*/
_curry1(
/*#__PURE__*/
_makeFlat(true));

export default flatten;