<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>Mobile Demo - Student Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 10px;
        }
        
        .demo-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            margin-bottom: 1rem;
        }
        
        .demo-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0;
            padding: 1rem;
        }
        
        .feature-demo {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        /* Mobile-optimized form styles */
        .form-control, .form-select {
            min-height: 50px;
            font-size: 16px;
            border-radius: 10px;
        }
        
        input[type="tel"] {
            font-size: 18px;
            letter-spacing: 1px;
            text-align: center;
            font-weight: 500;
        }
        
        .btn {
            min-height: 50px;
            font-size: 16px;
            border-radius: 10px;
            font-weight: 500;
        }
        
        .demo-section {
            border-left: 4px solid #667eea;
            padding-left: 1rem;
            margin: 1rem 0;
        }
        
        .status-indicator {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 0.5rem;
        }
        
        .status-success { background-color: #28a745; }
        .status-warning { background-color: #ffc107; }
        .status-danger { background-color: #dc3545; }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="demo-card">
            <div class="demo-header text-center">
                <i class="fas fa-mobile-alt fa-3x mb-3"></i>
                <h2>Mobile-Optimized Features</h2>
                <p class="mb-0">Complete Student Management System</p>
            </div>
            
            <div class="p-3">
                <!-- Mobile Number Input Demo -->
                <div class="demo-section">
                    <h5><i class="fas fa-phone text-primary"></i> Mobile Number Input</h5>
                    <div class="feature-demo">
                        <label for="demo_mobile" class="form-label">Mobile Number (10 digits)</label>
                        <input type="tel" 
                               class="form-control" 
                               id="demo_mobile" 
                               pattern="[0-9]{10}" 
                               maxlength="10" 
                               placeholder="Enter mobile number"
                               inputmode="numeric">
                        <div id="mobile_status" class="mt-2">
                            <span class="status-indicator status-warning"></span>
                            <small>Enter 10 digits to see validation</small>
                        </div>
                    </div>
                </div>
                
                <!-- Touch-Friendly Buttons -->
                <div class="demo-section">
                    <h5><i class="fas fa-hand-pointer text-success"></i> Touch-Friendly Interface</h5>
                    <div class="feature-demo">
                        <div class="row g-2">
                            <div class="col-6">
                                <button class="btn btn-primary w-100">
                                    <i class="fas fa-plus"></i> Add Student
                                </button>
                            </div>
                            <div class="col-6">
                                <button class="btn btn-success w-100">
                                    <i class="fas fa-download"></i> Export
                                </button>
                            </div>
                            <div class="col-6">
                                <button class="btn btn-warning w-100">
                                    <i class="fas fa-edit"></i> Edit
                                </button>
                            </div>
                            <div class="col-6">
                                <button class="btn btn-info w-100">
                                    <i class="fas fa-eye"></i> View
                                </button>
                            </div>
                        </div>
                        <p class="mt-2 mb-0 small text-muted">
                            <i class="fas fa-info-circle"></i> All buttons are minimum 50px height for easy tapping
                        </p>
                    </div>
                </div>
                
                <!-- Form Elements Demo -->
                <div class="demo-section">
                    <h5><i class="fas fa-wpforms text-warning"></i> Mobile-Optimized Forms</h5>
                    <div class="feature-demo">
                        <div class="mb-3">
                            <label for="demo_name" class="form-label">Student Name</label>
                            <input type="text" class="form-control" id="demo_name" placeholder="Enter student name">
                        </div>
                        
                        <div class="row">
                            <div class="col-6 mb-3">
                                <label for="demo_class" class="form-label">Class</label>
                                <select class="form-select" id="demo_class">
                                    <option>Select Class</option>
                                    <option>Class 1</option>
                                    <option>Class 2</option>
                                </select>
                            </div>
                            <div class="col-6 mb-3">
                                <label for="demo_section" class="form-label">Section</label>
                                <select class="form-select" id="demo_section">
                                    <option>Select Section</option>
                                    <option>Section A</option>
                                    <option>Section B</option>
                                </select>
                            </div>
                        </div>
                        
                        <p class="mb-0 small text-muted">
                            <i class="fas fa-info-circle"></i> 16px font size prevents zoom on iOS devices
                        </p>
                    </div>
                </div>
                
                <!-- Responsive Features -->
                <div class="demo-section">
                    <h5><i class="fas fa-expand-arrows-alt text-info"></i> Responsive Design</h5>
                    <div class="feature-demo">
                        <div class="row text-center">
                            <div class="col-4">
                                <div class="border rounded p-2 mb-2">
                                    <i class="fas fa-mobile-alt fa-2x text-primary"></i>
                                    <p class="small mb-0 mt-1">Mobile First</p>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="border rounded p-2 mb-2">
                                    <i class="fas fa-tablet-alt fa-2x text-success"></i>
                                    <p class="small mb-0 mt-1">Tablet Ready</p>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="border rounded p-2 mb-2">
                                    <i class="fas fa-desktop fa-2x text-warning"></i>
                                    <p class="small mb-0 mt-1">Desktop</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Key Features List -->
                <div class="demo-section">
                    <h5><i class="fas fa-check-circle text-success"></i> Mobile Features</h5>
                    <div class="feature-demo">
                        <ul class="list-unstyled mb-0">
                            <li class="mb-2">
                                <i class="fas fa-check text-success"></i> 
                                <strong>Numeric Keypad:</strong> Automatic for mobile numbers
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success"></i> 
                                <strong>10-Digit Validation:</strong> Real-time mobile number validation
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success"></i> 
                                <strong>Touch Targets:</strong> Minimum 44px for easy tapping
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success"></i> 
                                <strong>No Zoom:</strong> 16px font prevents unwanted zooming
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success"></i> 
                                <strong>Responsive Tables:</strong> Horizontal scroll on small screens
                            </li>
                            <li class="mb-0">
                                <i class="fas fa-check text-success"></i> 
                                <strong>Offline Ready:</strong> Works without internet after loading
                            </li>
                        </ul>
                    </div>
                </div>
                
                <!-- Navigation -->
                <div class="text-center mt-4">
                    <div class="row g-2">
                        <div class="col-6">
                            <a href="setup/" class="btn btn-primary w-100">
                                <i class="fas fa-database"></i> Setup Database
                            </a>
                        </div>
                        <div class="col-6">
                            <a href="login.php" class="btn btn-success w-100">
                                <i class="fas fa-sign-in-alt"></i> Login
                            </a>
                        </div>
                        <div class="col-6">
                            <a href="mobile-test.php" class="btn btn-info w-100">
                                <i class="fas fa-vial"></i> Test Mobile
                            </a>
                        </div>
                        <div class="col-6">
                            <a href="students/add.php" class="btn btn-warning w-100">
                                <i class="fas fa-user-plus"></i> Add Student
                            </a>
                        </div>
                    </div>
                </div>
                
                <!-- Device Info -->
                <div class="alert alert-info mt-4">
                    <h6><i class="fas fa-info-circle"></i> Testing Instructions:</h6>
                    <ol class="mb-0 small">
                        <li>Open this page on your mobile device</li>
                        <li>Tap the mobile number field - numeric keypad should appear</li>
                        <li>Try entering numbers and see real-time validation</li>
                        <li>Test all buttons for touch responsiveness</li>
                        <li>Rotate device to test responsive design</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        $(document).ready(function() {
            const mobileInput = $('#demo_mobile');
            const statusDiv = $('#mobile_status');
            
            mobileInput.on('input', function() {
                let value = $(this).val().replace(/\D/g, '');
                $(this).val(value);
                
                if (value.length === 0) {
                    statusDiv.html('<span class="status-indicator status-warning"></span><small>Enter 10 digits to see validation</small>');
                } else if (value.length < 10) {
                    statusDiv.html(`<span class="status-indicator status-danger"></span><small>Need ${10 - value.length} more digits (${value.length}/10)</small>`);
                } else if (value.length === 10) {
                    statusDiv.html('<span class="status-indicator status-success"></span><small>✓ Valid mobile number!</small>');
                    $(this).val(value.substring(0, 10));
                }
            });
            
            // Add click feedback to buttons
            $('.btn').on('click', function(e) {
                if ($(this).attr('href') && $(this).attr('href').startsWith('#')) {
                    e.preventDefault();
                }
                
                $(this).addClass('btn-clicked');
                setTimeout(() => {
                    $(this).removeClass('btn-clicked');
                }, 200);
            });
        });
    </script>
    
    <style>
        .btn-clicked {
            transform: scale(0.95);
            transition: transform 0.1s ease;
        }
    </style>
</body>
</html>
