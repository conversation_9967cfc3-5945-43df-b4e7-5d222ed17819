-- Student Management System Database Setup
-- Run this script in your MySQL database

CREATE DATABASE IF NOT EXISTS student_management;
USE student_management;

-- Users table
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    role ENUM('admin', 'user') DEFAULT 'user',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Students table
CREATE TABLE students (
    id INT AUTO_INCREMENT PRIMARY KEY,
    student_name VARCHAR(100) NOT NULL,
    father_name VA<PERSON>HA<PERSON>(100) NOT NULL,
    class INT NOT NULL CHECK (class >= 1 AND class <= 12),
    section ENUM('A', 'B', 'C') NOT NULL,
    address TEXT,
    mobile_number VARCHAR(10) CHECK (mobile_number IS NULL OR (LENGTH(mobile_number) = 10 AND mobile_number REGEXP '^[0-9]{10}$')),
    gender ENUM('Male', 'Female', 'Other'),
    photo_path VARCHAR(255),
    admission_date DATE DEFAULT CURRENT_DATE,
    status ENUM('Active', 'Left', 'Transferred') DEFAULT 'Active',
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE
);

-- Activity logs table
CREATE TABLE activity_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    action VARCHAR(50) NOT NULL,
    entity_type VARCHAR(50) NOT NULL,
    entity_id INT,
    details TEXT,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Create indexes for better performance
CREATE INDEX idx_students_class_section ON students(class, section);
CREATE INDEX idx_students_created_by ON students(created_by);
CREATE INDEX idx_students_status ON students(status);
CREATE INDEX idx_activity_logs_user_id ON activity_logs(user_id);
CREATE INDEX idx_activity_logs_created_at ON activity_logs(created_at);

-- Insert default admin user
-- Password: admin123
INSERT INTO users (username, email, password, full_name, role) VALUES 
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'System Administrator', 'admin');

-- Insert sample user
-- Password: user123
INSERT INTO users (username, email, password, full_name, role) VALUES 
('user1', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'John Doe', 'user');

-- Insert sample students
INSERT INTO students (student_name, father_name, class, section, address, mobile_number, gender, admission_date, created_by) VALUES 
('Alice Johnson', 'Robert Johnson', 10, 'A', '123 Main St, City', '1234567890', 'Female', '2024-01-15', 2),
('Bob Smith', 'Michael Smith', 9, 'B', '456 Oak Ave, Town', '0987654321', 'Male', '2024-01-16', 2),
('Carol Davis', 'David Davis', 11, 'A', '789 Pine Rd, Village', '1122334455', 'Female', '2024-01-17', 2);

-- Insert sample activity logs
INSERT INTO activity_logs (user_id, action, entity_type, entity_id, details) VALUES 
(1, 'USER_LOGIN', 'AUTH', NULL, 'Admin user logged in'),
(2, 'STUDENT_CREATED', 'STUDENT', 1, 'Created student: Alice Johnson'),
(2, 'STUDENT_CREATED', 'STUDENT', 2, 'Created student: Bob Smith'),
(2, 'STUDENT_CREATED', 'STUDENT', 3, 'Created student: Carol Davis');
