"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-hook-form";
exports.ids = ["vendor-chunks/react-hook-form"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-hook-form/dist/index.esm.mjs":
/*!*********************************************************!*\
  !*** ./node_modules/react-hook-form/dist/index.esm.mjs ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Controller: () => (/* binding */ Controller),\n/* harmony export */   Form: () => (/* binding */ Form),\n/* harmony export */   FormProvider: () => (/* binding */ FormProvider),\n/* harmony export */   appendErrors: () => (/* binding */ appendErrors),\n/* harmony export */   createFormControl: () => (/* binding */ createFormControl),\n/* harmony export */   get: () => (/* binding */ get),\n/* harmony export */   set: () => (/* binding */ set),\n/* harmony export */   useController: () => (/* binding */ useController),\n/* harmony export */   useFieldArray: () => (/* binding */ useFieldArray),\n/* harmony export */   useForm: () => (/* binding */ useForm),\n/* harmony export */   useFormContext: () => (/* binding */ useFormContext),\n/* harmony export */   useFormState: () => (/* binding */ useFormState),\n/* harmony export */   useWatch: () => (/* binding */ useWatch)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n\n\nvar isCheckBoxInput = (element)=>element.type === \"checkbox\";\nvar isDateObject = (value1)=>value1 instanceof Date;\nvar isNullOrUndefined = (value1)=>value1 == null;\nconst isObjectType = (value1)=>typeof value1 === \"object\";\nvar isObject = (value1)=>!isNullOrUndefined(value1) && !Array.isArray(value1) && isObjectType(value1) && !isDateObject(value1);\nvar getEventValue = (event)=>isObject(event) && event.target ? isCheckBoxInput(event.target) ? event.target.checked : event.target.value : event;\nvar getNodeParentName = (name)=>name.substring(0, name.search(/\\.\\d+(\\.|$)/)) || name;\nvar isNameInFieldArray = (names, name)=>names.has(getNodeParentName(name));\nvar isPlainObject = (tempObject)=>{\n    const prototypeCopy = tempObject.constructor && tempObject.constructor.prototype;\n    return isObject(prototypeCopy) && prototypeCopy.hasOwnProperty(\"isPrototypeOf\");\n};\nvar isWeb =  false && 0;\nfunction cloneObject(data) {\n    let copy;\n    const isArray = Array.isArray(data);\n    const isFileListInstance = typeof FileList !== \"undefined\" ? data instanceof FileList : false;\n    if (data instanceof Date) {\n        copy = new Date(data);\n    } else if (data instanceof Set) {\n        copy = new Set(data);\n    } else if (!(isWeb && (data instanceof Blob || isFileListInstance)) && (isArray || isObject(data))) {\n        copy = isArray ? [] : {};\n        if (!isArray && !isPlainObject(data)) {\n            copy = data;\n        } else {\n            for(const key in data){\n                if (data.hasOwnProperty(key)) {\n                    copy[key] = cloneObject(data[key]);\n                }\n            }\n        }\n    } else {\n        return data;\n    }\n    return copy;\n}\nvar compact = (value1)=>Array.isArray(value1) ? value1.filter(Boolean) : [];\nvar isUndefined = (val)=>val === undefined;\nvar get = (object, path, defaultValue)=>{\n    if (!path || !isObject(object)) {\n        return defaultValue;\n    }\n    const result = compact(path.split(/[,[\\].]+?/)).reduce((result, key)=>isNullOrUndefined(result) ? result : result[key], object);\n    return isUndefined(result) || result === object ? isUndefined(object[path]) ? defaultValue : object[path] : result;\n};\nvar isBoolean = (value1)=>typeof value1 === \"boolean\";\nvar isKey = (value1)=>/^\\w*$/.test(value1);\nvar stringToPath = (input)=>compact(input.replace(/[\"|']|\\]/g, \"\").split(/\\.|\\[/));\nvar set = (object, path, value1)=>{\n    let index = -1;\n    const tempPath = isKey(path) ? [\n        path\n    ] : stringToPath(path);\n    const length = tempPath.length;\n    const lastIndex = length - 1;\n    while(++index < length){\n        const key = tempPath[index];\n        let newValue = value1;\n        if (index !== lastIndex) {\n            const objValue = object[key];\n            newValue = isObject(objValue) || Array.isArray(objValue) ? objValue : !isNaN(+tempPath[index + 1]) ? [] : {};\n        }\n        if (key === \"__proto__\" || key === \"constructor\" || key === \"prototype\") {\n            return;\n        }\n        object[key] = newValue;\n        object = object[key];\n    }\n};\nconst EVENTS = {\n    BLUR: \"blur\",\n    FOCUS_OUT: \"focusout\",\n    CHANGE: \"change\"\n};\nconst VALIDATION_MODE = {\n    onBlur: \"onBlur\",\n    onChange: \"onChange\",\n    onSubmit: \"onSubmit\",\n    onTouched: \"onTouched\",\n    all: \"all\"\n};\nconst INPUT_VALIDATION_RULES = {\n    max: \"max\",\n    min: \"min\",\n    maxLength: \"maxLength\",\n    minLength: \"minLength\",\n    pattern: \"pattern\",\n    required: \"required\",\n    validate: \"validate\"\n};\nconst HookFormContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);\n/**\n * This custom hook allows you to access the form context. useFormContext is intended to be used in deeply nested structures, where it would become inconvenient to pass the context as a prop. To be used with {@link FormProvider}.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformcontext) • [Demo](https://codesandbox.io/s/react-hook-form-v7-form-context-ytudi)\n *\n * @returns return all useForm methods\n *\n * @example\n * ```tsx\n * function App() {\n *   const methods = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   return (\n *     <FormProvider {...methods} >\n *       <form onSubmit={methods.handleSubmit(onSubmit)}>\n *         <NestedInput />\n *         <input type=\"submit\" />\n *       </form>\n *     </FormProvider>\n *   );\n * }\n *\n *  function NestedInput() {\n *   const { register } = useFormContext(); // retrieve all hook methods\n *   return <input {...register(\"test\")} />;\n * }\n * ```\n */ const useFormContext = ()=>react__WEBPACK_IMPORTED_MODULE_0__.useContext(HookFormContext);\n/**\n * A provider component that propagates the `useForm` methods to all children components via [React Context](https://reactjs.org/docs/context.html) API. To be used with {@link useFormContext}.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformcontext) • [Demo](https://codesandbox.io/s/react-hook-form-v7-form-context-ytudi)\n *\n * @param props - all useForm methods\n *\n * @example\n * ```tsx\n * function App() {\n *   const methods = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   return (\n *     <FormProvider {...methods} >\n *       <form onSubmit={methods.handleSubmit(onSubmit)}>\n *         <NestedInput />\n *         <input type=\"submit\" />\n *       </form>\n *     </FormProvider>\n *   );\n * }\n *\n *  function NestedInput() {\n *   const { register } = useFormContext(); // retrieve all hook methods\n *   return <input {...register(\"test\")} />;\n * }\n * ```\n */ const FormProvider = (props)=>{\n    const { children, ...data } = props;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(HookFormContext.Provider, {\n        value: data\n    }, children);\n};\nvar getProxyFormState = (formState, control, localProxyFormState, isRoot = true)=>{\n    const result = {\n        defaultValues: control._defaultValues\n    };\n    for(const key in formState){\n        Object.defineProperty(result, key, {\n            get: ()=>{\n                const _key = key;\n                if (control._proxyFormState[_key] !== VALIDATION_MODE.all) {\n                    control._proxyFormState[_key] = !isRoot || VALIDATION_MODE.all;\n                }\n                localProxyFormState && (localProxyFormState[_key] = true);\n                return formState[_key];\n            }\n        });\n    }\n    return result;\n};\nconst useIsomorphicLayoutEffect =  false ? 0 : react__WEBPACK_IMPORTED_MODULE_0__.useEffect;\n/**\n * This custom hook allows you to subscribe to each form state, and isolate the re-render at the custom hook level. It has its scope in terms of form state subscription, so it would not affect other useFormState and useForm. Using this hook can reduce the re-render impact on large and complex form application.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformstate) • [Demo](https://codesandbox.io/s/useformstate-75xly)\n *\n * @param props - include options on specify fields to subscribe. {@link UseFormStateReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, handleSubmit, control } = useForm({\n *     defaultValues: {\n *     firstName: \"firstName\"\n *   }});\n *   const { dirtyFields } = useFormState({\n *     control\n *   });\n *   const onSubmit = (data) => console.log(data);\n *\n *   return (\n *     <form onSubmit={handleSubmit(onSubmit)}>\n *       <input {...register(\"firstName\")} placeholder=\"First Name\" />\n *       {dirtyFields.firstName && <p>Field is dirty.</p>}\n *       <input type=\"submit\" />\n *     </form>\n *   );\n * }\n * ```\n */ function useFormState(props) {\n    const methods = useFormContext();\n    const { control = methods.control, disabled, name, exact } = props || {};\n    const [formState, updateFormState] = react__WEBPACK_IMPORTED_MODULE_0__.useState(control._formState);\n    const _localProxyFormState = react__WEBPACK_IMPORTED_MODULE_0__.useRef({\n        isDirty: false,\n        isLoading: false,\n        dirtyFields: false,\n        touchedFields: false,\n        validatingFields: false,\n        isValidating: false,\n        isValid: false,\n        errors: false\n    });\n    useIsomorphicLayoutEffect(()=>control._subscribe({\n            name,\n            formState: _localProxyFormState.current,\n            exact,\n            callback: (formState)=>{\n                !disabled && updateFormState({\n                    ...control._formState,\n                    ...formState\n                });\n            }\n        }), [\n        name,\n        disabled,\n        exact\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        _localProxyFormState.current.isValid && control._setValid(true);\n    }, [\n        control\n    ]);\n    return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>getProxyFormState(formState, control, _localProxyFormState.current, false), [\n        formState,\n        control\n    ]);\n}\nvar isString = (value1)=>typeof value1 === \"string\";\nvar generateWatchOutput = (names, _names, formValues, isGlobal, defaultValue)=>{\n    if (isString(names)) {\n        isGlobal && _names.watch.add(names);\n        return get(formValues, names, defaultValue);\n    }\n    if (Array.isArray(names)) {\n        return names.map((fieldName)=>(isGlobal && _names.watch.add(fieldName), get(formValues, fieldName)));\n    }\n    isGlobal && (_names.watchAll = true);\n    return formValues;\n};\n/**\n * Custom hook to subscribe to field change and isolate re-rendering at the component level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @example\n * ```tsx\n * const { control } = useForm();\n * const values = useWatch({\n *   name: \"fieldName\"\n *   control,\n * })\n * ```\n */ function useWatch(props) {\n    const methods = useFormContext();\n    const { control = methods.control, name, defaultValue, disabled, exact } = props || {};\n    const _defaultValue = react__WEBPACK_IMPORTED_MODULE_0__.useRef(defaultValue);\n    const [value1, updateValue] = react__WEBPACK_IMPORTED_MODULE_0__.useState(control._getWatch(name, _defaultValue.current));\n    useIsomorphicLayoutEffect(()=>control._subscribe({\n            name,\n            formState: {\n                values: true\n            },\n            exact,\n            callback: (formState)=>!disabled && updateValue(generateWatchOutput(name, control._names, formState.values || control._formValues, false, _defaultValue.current))\n        }), [\n        name,\n        control,\n        disabled,\n        exact\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>control._removeUnmounted());\n    return value1;\n}\n/**\n * Custom hook to work with controlled component, this function provide you with both form and field level state. Re-render is isolated at the hook level.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usecontroller) • [Demo](https://codesandbox.io/s/usecontroller-0o8px)\n *\n * @param props - the path name to the form field value, and validation rules.\n *\n * @returns field properties, field and form state. {@link UseControllerReturn}\n *\n * @example\n * ```tsx\n * function Input(props) {\n *   const { field, fieldState, formState } = useController(props);\n *   return (\n *     <div>\n *       <input {...field} placeholder={props.name} />\n *       <p>{fieldState.isTouched && \"Touched\"}</p>\n *       <p>{formState.isSubmitted ? \"submitted\" : \"\"}</p>\n *     </div>\n *   );\n * }\n * ```\n */ function useController(props) {\n    const methods = useFormContext();\n    const { name, disabled, control = methods.control, shouldUnregister } = props;\n    const isArrayField = isNameInFieldArray(control._names.array, name);\n    const value1 = useWatch({\n        control,\n        name,\n        defaultValue: get(control._formValues, name, get(control._defaultValues, name, props.defaultValue)),\n        exact: true\n    });\n    const formState = useFormState({\n        control,\n        name,\n        exact: true\n    });\n    const _props = react__WEBPACK_IMPORTED_MODULE_0__.useRef(props);\n    const _registerProps = react__WEBPACK_IMPORTED_MODULE_0__.useRef(control.register(name, {\n        ...props.rules,\n        value: value1,\n        ...isBoolean(props.disabled) ? {\n            disabled: props.disabled\n        } : {}\n    }));\n    const fieldState = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>Object.defineProperties({}, {\n            invalid: {\n                enumerable: true,\n                get: ()=>!!get(formState.errors, name)\n            },\n            isDirty: {\n                enumerable: true,\n                get: ()=>!!get(formState.dirtyFields, name)\n            },\n            isTouched: {\n                enumerable: true,\n                get: ()=>!!get(formState.touchedFields, name)\n            },\n            isValidating: {\n                enumerable: true,\n                get: ()=>!!get(formState.validatingFields, name)\n            },\n            error: {\n                enumerable: true,\n                get: ()=>get(formState.errors, name)\n            }\n        }), [\n        formState,\n        name\n    ]);\n    const onChange = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((event)=>_registerProps.current.onChange({\n            target: {\n                value: getEventValue(event),\n                name: name\n            },\n            type: EVENTS.CHANGE\n        }), [\n        name\n    ]);\n    const onBlur = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>_registerProps.current.onBlur({\n            target: {\n                value: get(control._formValues, name),\n                name: name\n            },\n            type: EVENTS.BLUR\n        }), [\n        name,\n        control._formValues\n    ]);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((elm)=>{\n        const field = get(control._fields, name);\n        if (field && elm) {\n            field._f.ref = {\n                focus: ()=>elm.focus && elm.focus(),\n                select: ()=>elm.select && elm.select(),\n                setCustomValidity: (message)=>elm.setCustomValidity(message),\n                reportValidity: ()=>elm.reportValidity()\n            };\n        }\n    }, [\n        control._fields,\n        name\n    ]);\n    const field = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>({\n            name,\n            value: value1,\n            ...isBoolean(disabled) || formState.disabled ? {\n                disabled: formState.disabled || disabled\n            } : {},\n            onChange,\n            onBlur,\n            ref\n        }), [\n        name,\n        disabled,\n        formState.disabled,\n        onChange,\n        onBlur,\n        ref,\n        value1\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const _shouldUnregisterField = control._options.shouldUnregister || shouldUnregister;\n        control.register(name, {\n            ..._props.current.rules,\n            ...isBoolean(_props.current.disabled) ? {\n                disabled: _props.current.disabled\n            } : {}\n        });\n        const updateMounted = (name, value1)=>{\n            const field = get(control._fields, name);\n            if (field && field._f) {\n                field._f.mount = value1;\n            }\n        };\n        updateMounted(name, true);\n        if (_shouldUnregisterField) {\n            const value1 = cloneObject(get(control._options.defaultValues, name));\n            set(control._defaultValues, name, value1);\n            if (isUndefined(get(control._formValues, name))) {\n                set(control._formValues, name, value1);\n            }\n        }\n        !isArrayField && control.register(name);\n        return ()=>{\n            (isArrayField ? _shouldUnregisterField && !control._state.action : _shouldUnregisterField) ? control.unregister(name) : updateMounted(name, false);\n        };\n    }, [\n        name,\n        control,\n        isArrayField,\n        shouldUnregister\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        control._setDisabledField({\n            disabled,\n            name\n        });\n    }, [\n        disabled,\n        name,\n        control\n    ]);\n    return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>({\n            field,\n            formState,\n            fieldState\n        }), [\n        field,\n        formState,\n        fieldState\n    ]);\n}\n/**\n * Component based on `useController` hook to work with controlled component.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usecontroller/controller) • [Demo](https://codesandbox.io/s/react-hook-form-v6-controller-ts-jwyzw) • [Video](https://www.youtube.com/watch?v=N2UNk_UCVyA)\n *\n * @param props - the path name to the form field value, and validation rules.\n *\n * @returns provide field handler functions, field and form state.\n *\n * @example\n * ```tsx\n * function App() {\n *   const { control } = useForm<FormValues>({\n *     defaultValues: {\n *       test: \"\"\n *     }\n *   });\n *\n *   return (\n *     <form>\n *       <Controller\n *         control={control}\n *         name=\"test\"\n *         render={({ field: { onChange, onBlur, value, ref }, formState, fieldState }) => (\n *           <>\n *             <input\n *               onChange={onChange} // send value to hook form\n *               onBlur={onBlur} // notify when input is touched\n *               value={value} // return updated value\n *               ref={ref} // set ref for focus management\n *             />\n *             <p>{formState.isSubmitted ? \"submitted\" : \"\"}</p>\n *             <p>{fieldState.isTouched ? \"touched\" : \"\"}</p>\n *           </>\n *         )}\n *       />\n *     </form>\n *   );\n * }\n * ```\n */ const Controller = (props)=>props.render(useController(props));\nconst flatten = (obj)=>{\n    const output = {};\n    for (const key of Object.keys(obj)){\n        if (isObjectType(obj[key]) && obj[key] !== null) {\n            const nested = flatten(obj[key]);\n            for (const nestedKey of Object.keys(nested)){\n                output[`${key}.${nestedKey}`] = nested[nestedKey];\n            }\n        } else {\n            output[key] = obj[key];\n        }\n    }\n    return output;\n};\nconst POST_REQUEST = \"post\";\n/**\n * Form component to manage submission.\n *\n * @param props - to setup submission detail. {@link FormProps}\n *\n * @returns form component or headless render prop.\n *\n * @example\n * ```tsx\n * function App() {\n *   const { control, formState: { errors } } = useForm();\n *\n *   return (\n *     <Form action=\"/api\" control={control}>\n *       <input {...register(\"name\")} />\n *       <p>{errors?.root?.server && 'Server error'}</p>\n *       <button>Submit</button>\n *     </Form>\n *   );\n * }\n * ```\n */ function Form(props) {\n    const methods = useFormContext();\n    const [mounted, setMounted] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const { control = methods.control, onSubmit, children, action, method = POST_REQUEST, headers, encType, onError, render, onSuccess, validateStatus, ...rest } = props;\n    const submit = async (event)=>{\n        let hasError = false;\n        let type = \"\";\n        await control.handleSubmit(async (data)=>{\n            const formData = new FormData();\n            let formDataJson = \"\";\n            try {\n                formDataJson = JSON.stringify(data);\n            } catch (_a) {}\n            const flattenFormValues = flatten(control._formValues);\n            for(const key in flattenFormValues){\n                formData.append(key, flattenFormValues[key]);\n            }\n            if (onSubmit) {\n                await onSubmit({\n                    data,\n                    event,\n                    method,\n                    formData,\n                    formDataJson\n                });\n            }\n            if (action) {\n                try {\n                    const shouldStringifySubmissionData = [\n                        headers && headers[\"Content-Type\"],\n                        encType\n                    ].some((value1)=>value1 && value1.includes(\"json\"));\n                    const response = await fetch(String(action), {\n                        method,\n                        headers: {\n                            ...headers,\n                            ...encType ? {\n                                \"Content-Type\": encType\n                            } : {}\n                        },\n                        body: shouldStringifySubmissionData ? formDataJson : formData\n                    });\n                    if (response && (validateStatus ? !validateStatus(response.status) : response.status < 200 || response.status >= 300)) {\n                        hasError = true;\n                        onError && onError({\n                            response\n                        });\n                        type = String(response.status);\n                    } else {\n                        onSuccess && onSuccess({\n                            response\n                        });\n                    }\n                } catch (error) {\n                    hasError = true;\n                    onError && onError({\n                        error\n                    });\n                }\n            }\n        })(event);\n        if (hasError && props.control) {\n            props.control._subjects.state.next({\n                isSubmitSuccessful: false\n            });\n            props.control.setError(\"root.server\", {\n                type\n            });\n        }\n    };\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        setMounted(true);\n    }, []);\n    return render ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, render({\n        submit\n    })) : /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"form\", {\n        noValidate: mounted,\n        action: action,\n        method: method,\n        encType: encType,\n        onSubmit: submit,\n        ...rest\n    }, children);\n}\nvar appendErrors = (name, validateAllFieldCriteria, errors, type, message)=>validateAllFieldCriteria ? {\n        ...errors[name],\n        types: {\n            ...errors[name] && errors[name].types ? errors[name].types : {},\n            [type]: message || true\n        }\n    } : {};\nvar convertToArrayPayload = (value1)=>Array.isArray(value1) ? value1 : [\n        value1\n    ];\nvar createSubject = ()=>{\n    let _observers = [];\n    const next = (value1)=>{\n        for (const observer of _observers){\n            observer.next && observer.next(value1);\n        }\n    };\n    const subscribe = (observer)=>{\n        _observers.push(observer);\n        return {\n            unsubscribe: ()=>{\n                _observers = _observers.filter((o)=>o !== observer);\n            }\n        };\n    };\n    const unsubscribe = ()=>{\n        _observers = [];\n    };\n    return {\n        get observers () {\n            return _observers;\n        },\n        next,\n        subscribe,\n        unsubscribe\n    };\n};\nvar isPrimitive = (value1)=>isNullOrUndefined(value1) || !isObjectType(value1);\nfunction deepEqual(object1, object2) {\n    if (isPrimitive(object1) || isPrimitive(object2)) {\n        return object1 === object2;\n    }\n    if (isDateObject(object1) && isDateObject(object2)) {\n        return object1.getTime() === object2.getTime();\n    }\n    const keys1 = Object.keys(object1);\n    const keys2 = Object.keys(object2);\n    if (keys1.length !== keys2.length) {\n        return false;\n    }\n    for (const key of keys1){\n        const val1 = object1[key];\n        if (!keys2.includes(key)) {\n            return false;\n        }\n        if (key !== \"ref\") {\n            const val2 = object2[key];\n            if (isDateObject(val1) && isDateObject(val2) || isObject(val1) && isObject(val2) || Array.isArray(val1) && Array.isArray(val2) ? !deepEqual(val1, val2) : val1 !== val2) {\n                return false;\n            }\n        }\n    }\n    return true;\n}\nvar isEmptyObject = (value1)=>isObject(value1) && !Object.keys(value1).length;\nvar isFileInput = (element)=>element.type === \"file\";\nvar isFunction = (value1)=>typeof value1 === \"function\";\nvar isHTMLElement = (value1)=>{\n    if (!isWeb) {\n        return false;\n    }\n    const owner = value1 ? value1.ownerDocument : 0;\n    return value1 instanceof (owner && owner.defaultView ? owner.defaultView.HTMLElement : HTMLElement);\n};\nvar isMultipleSelect = (element)=>element.type === `select-multiple`;\nvar isRadioInput = (element)=>element.type === \"radio\";\nvar isRadioOrCheckbox = (ref)=>isRadioInput(ref) || isCheckBoxInput(ref);\nvar live = (ref)=>isHTMLElement(ref) && ref.isConnected;\nfunction baseGet(object, updatePath) {\n    const length = updatePath.slice(0, -1).length;\n    let index = 0;\n    while(index < length){\n        object = isUndefined(object) ? index++ : object[updatePath[index++]];\n    }\n    return object;\n}\nfunction isEmptyArray(obj) {\n    for(const key in obj){\n        if (obj.hasOwnProperty(key) && !isUndefined(obj[key])) {\n            return false;\n        }\n    }\n    return true;\n}\nfunction unset(object, path) {\n    const paths = Array.isArray(path) ? path : isKey(path) ? [\n        path\n    ] : stringToPath(path);\n    const childObject = paths.length === 1 ? object : baseGet(object, paths);\n    const index = paths.length - 1;\n    const key = paths[index];\n    if (childObject) {\n        delete childObject[key];\n    }\n    if (index !== 0 && (isObject(childObject) && isEmptyObject(childObject) || Array.isArray(childObject) && isEmptyArray(childObject))) {\n        unset(object, paths.slice(0, -1));\n    }\n    return object;\n}\nvar objectHasFunction = (data)=>{\n    for(const key in data){\n        if (isFunction(data[key])) {\n            return true;\n        }\n    }\n    return false;\n};\nfunction markFieldsDirty(data, fields = {}) {\n    const isParentNodeArray = Array.isArray(data);\n    if (isObject(data) || isParentNodeArray) {\n        for(const key in data){\n            if (Array.isArray(data[key]) || isObject(data[key]) && !objectHasFunction(data[key])) {\n                fields[key] = Array.isArray(data[key]) ? [] : {};\n                markFieldsDirty(data[key], fields[key]);\n            } else if (!isNullOrUndefined(data[key])) {\n                fields[key] = true;\n            }\n        }\n    }\n    return fields;\n}\nfunction getDirtyFieldsFromDefaultValues(data, formValues, dirtyFieldsFromValues) {\n    const isParentNodeArray = Array.isArray(data);\n    if (isObject(data) || isParentNodeArray) {\n        for(const key in data){\n            if (Array.isArray(data[key]) || isObject(data[key]) && !objectHasFunction(data[key])) {\n                if (isUndefined(formValues) || isPrimitive(dirtyFieldsFromValues[key])) {\n                    dirtyFieldsFromValues[key] = Array.isArray(data[key]) ? markFieldsDirty(data[key], []) : {\n                        ...markFieldsDirty(data[key])\n                    };\n                } else {\n                    getDirtyFieldsFromDefaultValues(data[key], isNullOrUndefined(formValues) ? {} : formValues[key], dirtyFieldsFromValues[key]);\n                }\n            } else {\n                dirtyFieldsFromValues[key] = !deepEqual(data[key], formValues[key]);\n            }\n        }\n    }\n    return dirtyFieldsFromValues;\n}\nvar getDirtyFields = (defaultValues, formValues)=>getDirtyFieldsFromDefaultValues(defaultValues, formValues, markFieldsDirty(formValues));\nconst defaultResult = {\n    value: false,\n    isValid: false\n};\nconst validResult = {\n    value: true,\n    isValid: true\n};\nvar getCheckboxValue = (options)=>{\n    if (Array.isArray(options)) {\n        if (options.length > 1) {\n            const values = options.filter((option)=>option && option.checked && !option.disabled).map((option)=>option.value);\n            return {\n                value: values,\n                isValid: !!values.length\n            };\n        }\n        return options[0].checked && !options[0].disabled ? options[0].attributes && !isUndefined(options[0].attributes.value) ? isUndefined(options[0].value) || options[0].value === \"\" ? validResult : {\n            value: options[0].value,\n            isValid: true\n        } : validResult : defaultResult;\n    }\n    return defaultResult;\n};\nvar getFieldValueAs = (value1, { valueAsNumber, valueAsDate, setValueAs })=>isUndefined(value1) ? value1 : valueAsNumber ? value1 === \"\" ? NaN : value1 ? +value1 : value1 : valueAsDate && isString(value1) ? new Date(value1) : setValueAs ? setValueAs(value1) : value1;\nconst defaultReturn = {\n    isValid: false,\n    value: null\n};\nvar getRadioValue = (options)=>Array.isArray(options) ? options.reduce((previous, option)=>option && option.checked && !option.disabled ? {\n            isValid: true,\n            value: option.value\n        } : previous, defaultReturn) : defaultReturn;\nfunction getFieldValue(_f) {\n    const ref = _f.ref;\n    if (isFileInput(ref)) {\n        return ref.files;\n    }\n    if (isRadioInput(ref)) {\n        return getRadioValue(_f.refs).value;\n    }\n    if (isMultipleSelect(ref)) {\n        return [\n            ...ref.selectedOptions\n        ].map(({ value: value1 })=>value1);\n    }\n    if (isCheckBoxInput(ref)) {\n        return getCheckboxValue(_f.refs).value;\n    }\n    return getFieldValueAs(isUndefined(ref.value) ? _f.ref.value : ref.value, _f);\n}\nvar getResolverOptions = (fieldsNames, _fields, criteriaMode, shouldUseNativeValidation)=>{\n    const fields = {};\n    for (const name of fieldsNames){\n        const field = get(_fields, name);\n        field && set(fields, name, field._f);\n    }\n    return {\n        criteriaMode,\n        names: [\n            ...fieldsNames\n        ],\n        fields,\n        shouldUseNativeValidation\n    };\n};\nvar isRegex = (value1)=>value1 instanceof RegExp;\nvar getRuleValue = (rule)=>isUndefined(rule) ? rule : isRegex(rule) ? rule.source : isObject(rule) ? isRegex(rule.value) ? rule.value.source : rule.value : rule;\nvar getValidationModes = (mode)=>({\n        isOnSubmit: !mode || mode === VALIDATION_MODE.onSubmit,\n        isOnBlur: mode === VALIDATION_MODE.onBlur,\n        isOnChange: mode === VALIDATION_MODE.onChange,\n        isOnAll: mode === VALIDATION_MODE.all,\n        isOnTouch: mode === VALIDATION_MODE.onTouched\n    });\nconst ASYNC_FUNCTION = \"AsyncFunction\";\nvar hasPromiseValidation = (fieldReference)=>!!fieldReference && !!fieldReference.validate && !!(isFunction(fieldReference.validate) && fieldReference.validate.constructor.name === ASYNC_FUNCTION || isObject(fieldReference.validate) && Object.values(fieldReference.validate).find((validateFunction)=>validateFunction.constructor.name === ASYNC_FUNCTION));\nvar hasValidation = (options)=>options.mount && (options.required || options.min || options.max || options.maxLength || options.minLength || options.pattern || options.validate);\nvar isWatched = (name, _names, isBlurEvent)=>!isBlurEvent && (_names.watchAll || _names.watch.has(name) || [\n        ..._names.watch\n    ].some((watchName)=>name.startsWith(watchName) && /^\\.\\w+/.test(name.slice(watchName.length))));\nconst iterateFieldsByAction = (fields, action, fieldsNames, abortEarly)=>{\n    for (const key of fieldsNames || Object.keys(fields)){\n        const field = get(fields, key);\n        if (field) {\n            const { _f, ...currentField } = field;\n            if (_f) {\n                if (_f.refs && _f.refs[0] && action(_f.refs[0], key) && !abortEarly) {\n                    return true;\n                } else if (_f.ref && action(_f.ref, _f.name) && !abortEarly) {\n                    return true;\n                } else {\n                    if (iterateFieldsByAction(currentField, action)) {\n                        break;\n                    }\n                }\n            } else if (isObject(currentField)) {\n                if (iterateFieldsByAction(currentField, action)) {\n                    break;\n                }\n            }\n        }\n    }\n    return;\n};\nfunction schemaErrorLookup(errors, _fields, name) {\n    const error = get(errors, name);\n    if (error || isKey(name)) {\n        return {\n            error,\n            name\n        };\n    }\n    const names = name.split(\".\");\n    while(names.length){\n        const fieldName = names.join(\".\");\n        const field = get(_fields, fieldName);\n        const foundError = get(errors, fieldName);\n        if (field && !Array.isArray(field) && name !== fieldName) {\n            return {\n                name\n            };\n        }\n        if (foundError && foundError.type) {\n            return {\n                name: fieldName,\n                error: foundError\n            };\n        }\n        if (foundError && foundError.root && foundError.root.type) {\n            return {\n                name: `${fieldName}.root`,\n                error: foundError.root\n            };\n        }\n        names.pop();\n    }\n    return {\n        name\n    };\n}\nvar shouldRenderFormState = (formStateData, _proxyFormState, updateFormState, isRoot)=>{\n    updateFormState(formStateData);\n    const { name, ...formState } = formStateData;\n    return isEmptyObject(formState) || Object.keys(formState).length >= Object.keys(_proxyFormState).length || Object.keys(formState).find((key)=>_proxyFormState[key] === (!isRoot || VALIDATION_MODE.all));\n};\nvar shouldSubscribeByName = (name, signalName, exact)=>!name || !signalName || name === signalName || convertToArrayPayload(name).some((currentName)=>currentName && (exact ? currentName === signalName : currentName.startsWith(signalName) || signalName.startsWith(currentName)));\nvar skipValidation = (isBlurEvent, isTouched, isSubmitted, reValidateMode, mode)=>{\n    if (mode.isOnAll) {\n        return false;\n    } else if (!isSubmitted && mode.isOnTouch) {\n        return !(isTouched || isBlurEvent);\n    } else if (isSubmitted ? reValidateMode.isOnBlur : mode.isOnBlur) {\n        return !isBlurEvent;\n    } else if (isSubmitted ? reValidateMode.isOnChange : mode.isOnChange) {\n        return isBlurEvent;\n    }\n    return true;\n};\nvar unsetEmptyArray = (ref, name)=>!compact(get(ref, name)).length && unset(ref, name);\nvar updateFieldArrayRootError = (errors, error, name)=>{\n    const fieldArrayErrors = convertToArrayPayload(get(errors, name));\n    set(fieldArrayErrors, \"root\", error[name]);\n    set(errors, name, fieldArrayErrors);\n    return errors;\n};\nvar isMessage = (value1)=>isString(value1);\nfunction getValidateError(result, ref, type = \"validate\") {\n    if (isMessage(result) || Array.isArray(result) && result.every(isMessage) || isBoolean(result) && !result) {\n        return {\n            type,\n            message: isMessage(result) ? result : \"\",\n            ref\n        };\n    }\n}\nvar getValueAndMessage = (validationData)=>isObject(validationData) && !isRegex(validationData) ? validationData : {\n        value: validationData,\n        message: \"\"\n    };\nvar validateField = async (field, disabledFieldNames, formValues, validateAllFieldCriteria, shouldUseNativeValidation, isFieldArray)=>{\n    const { ref, refs, required, maxLength, minLength, min, max, pattern, validate, name, valueAsNumber, mount } = field._f;\n    const inputValue = get(formValues, name);\n    if (!mount || disabledFieldNames.has(name)) {\n        return {};\n    }\n    const inputRef = refs ? refs[0] : ref;\n    const setCustomValidity = (message)=>{\n        if (shouldUseNativeValidation && inputRef.reportValidity) {\n            inputRef.setCustomValidity(isBoolean(message) ? \"\" : message || \"\");\n            inputRef.reportValidity();\n        }\n    };\n    const error = {};\n    const isRadio = isRadioInput(ref);\n    const isCheckBox = isCheckBoxInput(ref);\n    const isRadioOrCheckbox = isRadio || isCheckBox;\n    const isEmpty = (valueAsNumber || isFileInput(ref)) && isUndefined(ref.value) && isUndefined(inputValue) || isHTMLElement(ref) && ref.value === \"\" || inputValue === \"\" || Array.isArray(inputValue) && !inputValue.length;\n    const appendErrorsCurry = appendErrors.bind(null, name, validateAllFieldCriteria, error);\n    const getMinMaxMessage = (exceedMax, maxLengthMessage, minLengthMessage, maxType = INPUT_VALIDATION_RULES.maxLength, minType = INPUT_VALIDATION_RULES.minLength)=>{\n        const message = exceedMax ? maxLengthMessage : minLengthMessage;\n        error[name] = {\n            type: exceedMax ? maxType : minType,\n            message,\n            ref,\n            ...appendErrorsCurry(exceedMax ? maxType : minType, message)\n        };\n    };\n    if (isFieldArray ? !Array.isArray(inputValue) || !inputValue.length : required && (!isRadioOrCheckbox && (isEmpty || isNullOrUndefined(inputValue)) || isBoolean(inputValue) && !inputValue || isCheckBox && !getCheckboxValue(refs).isValid || isRadio && !getRadioValue(refs).isValid)) {\n        const { value: value1, message } = isMessage(required) ? {\n            value: !!required,\n            message: required\n        } : getValueAndMessage(required);\n        if (value1) {\n            error[name] = {\n                type: INPUT_VALIDATION_RULES.required,\n                message,\n                ref: inputRef,\n                ...appendErrorsCurry(INPUT_VALIDATION_RULES.required, message)\n            };\n            if (!validateAllFieldCriteria) {\n                setCustomValidity(message);\n                return error;\n            }\n        }\n    }\n    if (!isEmpty && (!isNullOrUndefined(min) || !isNullOrUndefined(max))) {\n        let exceedMax;\n        let exceedMin;\n        const maxOutput = getValueAndMessage(max);\n        const minOutput = getValueAndMessage(min);\n        if (!isNullOrUndefined(inputValue) && !isNaN(inputValue)) {\n            const valueNumber = ref.valueAsNumber || (inputValue ? +inputValue : inputValue);\n            if (!isNullOrUndefined(maxOutput.value)) {\n                exceedMax = valueNumber > maxOutput.value;\n            }\n            if (!isNullOrUndefined(minOutput.value)) {\n                exceedMin = valueNumber < minOutput.value;\n            }\n        } else {\n            const valueDate = ref.valueAsDate || new Date(inputValue);\n            const convertTimeToDate = (time)=>new Date(new Date().toDateString() + \" \" + time);\n            const isTime = ref.type == \"time\";\n            const isWeek = ref.type == \"week\";\n            if (isString(maxOutput.value) && inputValue) {\n                exceedMax = isTime ? convertTimeToDate(inputValue) > convertTimeToDate(maxOutput.value) : isWeek ? inputValue > maxOutput.value : valueDate > new Date(maxOutput.value);\n            }\n            if (isString(minOutput.value) && inputValue) {\n                exceedMin = isTime ? convertTimeToDate(inputValue) < convertTimeToDate(minOutput.value) : isWeek ? inputValue < minOutput.value : valueDate < new Date(minOutput.value);\n            }\n        }\n        if (exceedMax || exceedMin) {\n            getMinMaxMessage(!!exceedMax, maxOutput.message, minOutput.message, INPUT_VALIDATION_RULES.max, INPUT_VALIDATION_RULES.min);\n            if (!validateAllFieldCriteria) {\n                setCustomValidity(error[name].message);\n                return error;\n            }\n        }\n    }\n    if ((maxLength || minLength) && !isEmpty && (isString(inputValue) || isFieldArray && Array.isArray(inputValue))) {\n        const maxLengthOutput = getValueAndMessage(maxLength);\n        const minLengthOutput = getValueAndMessage(minLength);\n        const exceedMax = !isNullOrUndefined(maxLengthOutput.value) && inputValue.length > +maxLengthOutput.value;\n        const exceedMin = !isNullOrUndefined(minLengthOutput.value) && inputValue.length < +minLengthOutput.value;\n        if (exceedMax || exceedMin) {\n            getMinMaxMessage(exceedMax, maxLengthOutput.message, minLengthOutput.message);\n            if (!validateAllFieldCriteria) {\n                setCustomValidity(error[name].message);\n                return error;\n            }\n        }\n    }\n    if (pattern && !isEmpty && isString(inputValue)) {\n        const { value: patternValue, message } = getValueAndMessage(pattern);\n        if (isRegex(patternValue) && !inputValue.match(patternValue)) {\n            error[name] = {\n                type: INPUT_VALIDATION_RULES.pattern,\n                message,\n                ref,\n                ...appendErrorsCurry(INPUT_VALIDATION_RULES.pattern, message)\n            };\n            if (!validateAllFieldCriteria) {\n                setCustomValidity(message);\n                return error;\n            }\n        }\n    }\n    if (validate) {\n        if (isFunction(validate)) {\n            const result = await validate(inputValue, formValues);\n            const validateError = getValidateError(result, inputRef);\n            if (validateError) {\n                error[name] = {\n                    ...validateError,\n                    ...appendErrorsCurry(INPUT_VALIDATION_RULES.validate, validateError.message)\n                };\n                if (!validateAllFieldCriteria) {\n                    setCustomValidity(validateError.message);\n                    return error;\n                }\n            }\n        } else if (isObject(validate)) {\n            let validationResult = {};\n            for(const key in validate){\n                if (!isEmptyObject(validationResult) && !validateAllFieldCriteria) {\n                    break;\n                }\n                const validateError = getValidateError(await validate[key](inputValue, formValues), inputRef, key);\n                if (validateError) {\n                    validationResult = {\n                        ...validateError,\n                        ...appendErrorsCurry(key, validateError.message)\n                    };\n                    setCustomValidity(validateError.message);\n                    if (validateAllFieldCriteria) {\n                        error[name] = validationResult;\n                    }\n                }\n            }\n            if (!isEmptyObject(validationResult)) {\n                error[name] = {\n                    ref: inputRef,\n                    ...validationResult\n                };\n                if (!validateAllFieldCriteria) {\n                    return error;\n                }\n            }\n        }\n    }\n    setCustomValidity(true);\n    return error;\n};\nconst defaultOptions = {\n    mode: VALIDATION_MODE.onSubmit,\n    reValidateMode: VALIDATION_MODE.onChange,\n    shouldFocusError: true\n};\nfunction createFormControl(props = {}) {\n    let _options = {\n        ...defaultOptions,\n        ...props\n    };\n    let _formState = {\n        submitCount: 0,\n        isDirty: false,\n        isReady: false,\n        isLoading: isFunction(_options.defaultValues),\n        isValidating: false,\n        isSubmitted: false,\n        isSubmitting: false,\n        isSubmitSuccessful: false,\n        isValid: false,\n        touchedFields: {},\n        dirtyFields: {},\n        validatingFields: {},\n        errors: _options.errors || {},\n        disabled: _options.disabled || false\n    };\n    const _fields = {};\n    let _defaultValues = isObject(_options.defaultValues) || isObject(_options.values) ? cloneObject(_options.defaultValues || _options.values) || {} : {};\n    let _formValues = _options.shouldUnregister ? {} : cloneObject(_defaultValues);\n    let _state = {\n        action: false,\n        mount: false,\n        watch: false\n    };\n    let _names = {\n        mount: new Set(),\n        disabled: new Set(),\n        unMount: new Set(),\n        array: new Set(),\n        watch: new Set()\n    };\n    let delayErrorCallback;\n    let timer = 0;\n    const _proxyFormState = {\n        isDirty: false,\n        dirtyFields: false,\n        validatingFields: false,\n        touchedFields: false,\n        isValidating: false,\n        isValid: false,\n        errors: false\n    };\n    let _proxySubscribeFormState = {\n        ..._proxyFormState\n    };\n    const _subjects = {\n        array: createSubject(),\n        state: createSubject()\n    };\n    const shouldDisplayAllAssociatedErrors = _options.criteriaMode === VALIDATION_MODE.all;\n    const debounce = (callback)=>(wait)=>{\n            clearTimeout(timer);\n            timer = setTimeout(callback, wait);\n        };\n    const _setValid = async (shouldUpdateValid)=>{\n        if (!_options.disabled && (_proxyFormState.isValid || _proxySubscribeFormState.isValid || shouldUpdateValid)) {\n            const isValid = _options.resolver ? isEmptyObject((await _runSchema()).errors) : await executeBuiltInValidation(_fields, true);\n            if (isValid !== _formState.isValid) {\n                _subjects.state.next({\n                    isValid\n                });\n            }\n        }\n    };\n    const _updateIsValidating = (names, isValidating)=>{\n        if (!_options.disabled && (_proxyFormState.isValidating || _proxyFormState.validatingFields || _proxySubscribeFormState.isValidating || _proxySubscribeFormState.validatingFields)) {\n            (names || Array.from(_names.mount)).forEach((name)=>{\n                if (name) {\n                    isValidating ? set(_formState.validatingFields, name, isValidating) : unset(_formState.validatingFields, name);\n                }\n            });\n            _subjects.state.next({\n                validatingFields: _formState.validatingFields,\n                isValidating: !isEmptyObject(_formState.validatingFields)\n            });\n        }\n    };\n    const _setFieldArray = (name, values = [], method, args, shouldSetValues = true, shouldUpdateFieldsAndState = true)=>{\n        if (args && method && !_options.disabled) {\n            _state.action = true;\n            if (shouldUpdateFieldsAndState && Array.isArray(get(_fields, name))) {\n                const fieldValues = method(get(_fields, name), args.argA, args.argB);\n                shouldSetValues && set(_fields, name, fieldValues);\n            }\n            if (shouldUpdateFieldsAndState && Array.isArray(get(_formState.errors, name))) {\n                const errors = method(get(_formState.errors, name), args.argA, args.argB);\n                shouldSetValues && set(_formState.errors, name, errors);\n                unsetEmptyArray(_formState.errors, name);\n            }\n            if ((_proxyFormState.touchedFields || _proxySubscribeFormState.touchedFields) && shouldUpdateFieldsAndState && Array.isArray(get(_formState.touchedFields, name))) {\n                const touchedFields = method(get(_formState.touchedFields, name), args.argA, args.argB);\n                shouldSetValues && set(_formState.touchedFields, name, touchedFields);\n            }\n            if (_proxyFormState.dirtyFields || _proxySubscribeFormState.dirtyFields) {\n                _formState.dirtyFields = getDirtyFields(_defaultValues, _formValues);\n            }\n            _subjects.state.next({\n                name,\n                isDirty: _getDirty(name, values),\n                dirtyFields: _formState.dirtyFields,\n                errors: _formState.errors,\n                isValid: _formState.isValid\n            });\n        } else {\n            set(_formValues, name, values);\n        }\n    };\n    const updateErrors = (name, error)=>{\n        set(_formState.errors, name, error);\n        _subjects.state.next({\n            errors: _formState.errors\n        });\n    };\n    const _setErrors = (errors)=>{\n        _formState.errors = errors;\n        _subjects.state.next({\n            errors: _formState.errors,\n            isValid: false\n        });\n    };\n    const updateValidAndValue = (name, shouldSkipSetValueAs, value1, ref)=>{\n        const field = get(_fields, name);\n        if (field) {\n            const defaultValue = get(_formValues, name, isUndefined(value1) ? get(_defaultValues, name) : value1);\n            isUndefined(defaultValue) || ref && ref.defaultChecked || shouldSkipSetValueAs ? set(_formValues, name, shouldSkipSetValueAs ? defaultValue : getFieldValue(field._f)) : setFieldValue(name, defaultValue);\n            _state.mount && _setValid();\n        }\n    };\n    const updateTouchAndDirty = (name, fieldValue, isBlurEvent, shouldDirty, shouldRender)=>{\n        let shouldUpdateField = false;\n        let isPreviousDirty = false;\n        const output = {\n            name\n        };\n        if (!_options.disabled) {\n            if (!isBlurEvent || shouldDirty) {\n                if (_proxyFormState.isDirty || _proxySubscribeFormState.isDirty) {\n                    isPreviousDirty = _formState.isDirty;\n                    _formState.isDirty = output.isDirty = _getDirty();\n                    shouldUpdateField = isPreviousDirty !== output.isDirty;\n                }\n                const isCurrentFieldPristine = deepEqual(get(_defaultValues, name), fieldValue);\n                isPreviousDirty = !!get(_formState.dirtyFields, name);\n                isCurrentFieldPristine ? unset(_formState.dirtyFields, name) : set(_formState.dirtyFields, name, true);\n                output.dirtyFields = _formState.dirtyFields;\n                shouldUpdateField = shouldUpdateField || (_proxyFormState.dirtyFields || _proxySubscribeFormState.dirtyFields) && isPreviousDirty !== !isCurrentFieldPristine;\n            }\n            if (isBlurEvent) {\n                const isPreviousFieldTouched = get(_formState.touchedFields, name);\n                if (!isPreviousFieldTouched) {\n                    set(_formState.touchedFields, name, isBlurEvent);\n                    output.touchedFields = _formState.touchedFields;\n                    shouldUpdateField = shouldUpdateField || (_proxyFormState.touchedFields || _proxySubscribeFormState.touchedFields) && isPreviousFieldTouched !== isBlurEvent;\n                }\n            }\n            shouldUpdateField && shouldRender && _subjects.state.next(output);\n        }\n        return shouldUpdateField ? output : {};\n    };\n    const shouldRenderByError = (name, isValid, error, fieldState)=>{\n        const previousFieldError = get(_formState.errors, name);\n        const shouldUpdateValid = (_proxyFormState.isValid || _proxySubscribeFormState.isValid) && isBoolean(isValid) && _formState.isValid !== isValid;\n        if (_options.delayError && error) {\n            delayErrorCallback = debounce(()=>updateErrors(name, error));\n            delayErrorCallback(_options.delayError);\n        } else {\n            clearTimeout(timer);\n            delayErrorCallback = null;\n            error ? set(_formState.errors, name, error) : unset(_formState.errors, name);\n        }\n        if ((error ? !deepEqual(previousFieldError, error) : previousFieldError) || !isEmptyObject(fieldState) || shouldUpdateValid) {\n            const updatedFormState = {\n                ...fieldState,\n                ...shouldUpdateValid && isBoolean(isValid) ? {\n                    isValid\n                } : {},\n                errors: _formState.errors,\n                name\n            };\n            _formState = {\n                ..._formState,\n                ...updatedFormState\n            };\n            _subjects.state.next(updatedFormState);\n        }\n    };\n    const _runSchema = async (name)=>{\n        _updateIsValidating(name, true);\n        const result = await _options.resolver(_formValues, _options.context, getResolverOptions(name || _names.mount, _fields, _options.criteriaMode, _options.shouldUseNativeValidation));\n        _updateIsValidating(name);\n        return result;\n    };\n    const executeSchemaAndUpdateState = async (names)=>{\n        const { errors } = await _runSchema(names);\n        if (names) {\n            for (const name of names){\n                const error = get(errors, name);\n                error ? set(_formState.errors, name, error) : unset(_formState.errors, name);\n            }\n        } else {\n            _formState.errors = errors;\n        }\n        return errors;\n    };\n    const executeBuiltInValidation = async (fields, shouldOnlyCheckValid, context = {\n        valid: true\n    })=>{\n        for(const name in fields){\n            const field = fields[name];\n            if (field) {\n                const { _f, ...fieldValue } = field;\n                if (_f) {\n                    const isFieldArrayRoot = _names.array.has(_f.name);\n                    const isPromiseFunction = field._f && hasPromiseValidation(field._f);\n                    if (isPromiseFunction && _proxyFormState.validatingFields) {\n                        _updateIsValidating([\n                            name\n                        ], true);\n                    }\n                    const fieldError = await validateField(field, _names.disabled, _formValues, shouldDisplayAllAssociatedErrors, _options.shouldUseNativeValidation && !shouldOnlyCheckValid, isFieldArrayRoot);\n                    if (isPromiseFunction && _proxyFormState.validatingFields) {\n                        _updateIsValidating([\n                            name\n                        ]);\n                    }\n                    if (fieldError[_f.name]) {\n                        context.valid = false;\n                        if (shouldOnlyCheckValid) {\n                            break;\n                        }\n                    }\n                    !shouldOnlyCheckValid && (get(fieldError, _f.name) ? isFieldArrayRoot ? updateFieldArrayRootError(_formState.errors, fieldError, _f.name) : set(_formState.errors, _f.name, fieldError[_f.name]) : unset(_formState.errors, _f.name));\n                }\n                !isEmptyObject(fieldValue) && await executeBuiltInValidation(fieldValue, shouldOnlyCheckValid, context);\n            }\n        }\n        return context.valid;\n    };\n    const _removeUnmounted = ()=>{\n        for (const name of _names.unMount){\n            const field = get(_fields, name);\n            field && (field._f.refs ? field._f.refs.every((ref)=>!live(ref)) : !live(field._f.ref)) && unregister(name);\n        }\n        _names.unMount = new Set();\n    };\n    const _getDirty = (name, data)=>!_options.disabled && (name && data && set(_formValues, name, data), !deepEqual(getValues(), _defaultValues));\n    const _getWatch = (names, defaultValue, isGlobal)=>generateWatchOutput(names, _names, {\n            ..._state.mount ? _formValues : isUndefined(defaultValue) ? _defaultValues : isString(names) ? {\n                [names]: defaultValue\n            } : defaultValue\n        }, isGlobal, defaultValue);\n    const _getFieldArray = (name)=>compact(get(_state.mount ? _formValues : _defaultValues, name, _options.shouldUnregister ? get(_defaultValues, name, []) : []));\n    const setFieldValue = (name, value1, options = {})=>{\n        const field = get(_fields, name);\n        let fieldValue = value1;\n        if (field) {\n            const fieldReference = field._f;\n            if (fieldReference) {\n                !fieldReference.disabled && set(_formValues, name, getFieldValueAs(value1, fieldReference));\n                fieldValue = isHTMLElement(fieldReference.ref) && isNullOrUndefined(value1) ? \"\" : value1;\n                if (isMultipleSelect(fieldReference.ref)) {\n                    [\n                        ...fieldReference.ref.options\n                    ].forEach((optionRef)=>optionRef.selected = fieldValue.includes(optionRef.value));\n                } else if (fieldReference.refs) {\n                    if (isCheckBoxInput(fieldReference.ref)) {\n                        fieldReference.refs.forEach((checkboxRef)=>{\n                            if (!checkboxRef.defaultChecked || !checkboxRef.disabled) {\n                                if (Array.isArray(fieldValue)) {\n                                    checkboxRef.checked = !!fieldValue.find((data)=>data === checkboxRef.value);\n                                } else {\n                                    checkboxRef.checked = fieldValue === checkboxRef.value || !!fieldValue;\n                                }\n                            }\n                        });\n                    } else {\n                        fieldReference.refs.forEach((radioRef)=>radioRef.checked = radioRef.value === fieldValue);\n                    }\n                } else if (isFileInput(fieldReference.ref)) {\n                    fieldReference.ref.value = \"\";\n                } else {\n                    fieldReference.ref.value = fieldValue;\n                    if (!fieldReference.ref.type) {\n                        _subjects.state.next({\n                            name,\n                            values: cloneObject(_formValues)\n                        });\n                    }\n                }\n            }\n        }\n        (options.shouldDirty || options.shouldTouch) && updateTouchAndDirty(name, fieldValue, options.shouldTouch, options.shouldDirty, true);\n        options.shouldValidate && trigger(name);\n    };\n    const setValues = (name, value1, options)=>{\n        for(const fieldKey in value1){\n            if (!value1.hasOwnProperty(fieldKey)) {\n                return;\n            }\n            const fieldValue = value1[fieldKey];\n            const fieldName = name + \".\" + fieldKey;\n            const field = get(_fields, fieldName);\n            (_names.array.has(name) || isObject(fieldValue) || field && !field._f) && !isDateObject(fieldValue) ? setValues(fieldName, fieldValue, options) : setFieldValue(fieldName, fieldValue, options);\n        }\n    };\n    const setValue = (name, value1, options = {})=>{\n        const field = get(_fields, name);\n        const isFieldArray = _names.array.has(name);\n        const cloneValue = cloneObject(value1);\n        set(_formValues, name, cloneValue);\n        if (isFieldArray) {\n            _subjects.array.next({\n                name,\n                values: cloneObject(_formValues)\n            });\n            if ((_proxyFormState.isDirty || _proxyFormState.dirtyFields || _proxySubscribeFormState.isDirty || _proxySubscribeFormState.dirtyFields) && options.shouldDirty) {\n                _subjects.state.next({\n                    name,\n                    dirtyFields: getDirtyFields(_defaultValues, _formValues),\n                    isDirty: _getDirty(name, cloneValue)\n                });\n            }\n        } else {\n            field && !field._f && !isNullOrUndefined(cloneValue) ? setValues(name, cloneValue, options) : setFieldValue(name, cloneValue, options);\n        }\n        isWatched(name, _names) && _subjects.state.next({\n            ..._formState\n        });\n        _subjects.state.next({\n            name: _state.mount ? name : undefined,\n            values: cloneObject(_formValues)\n        });\n    };\n    const onChange = async (event)=>{\n        _state.mount = true;\n        const target = event.target;\n        let name = target.name;\n        let isFieldValueUpdated = true;\n        const field = get(_fields, name);\n        const _updateIsFieldValueUpdated = (fieldValue)=>{\n            isFieldValueUpdated = Number.isNaN(fieldValue) || isDateObject(fieldValue) && isNaN(fieldValue.getTime()) || deepEqual(fieldValue, get(_formValues, name, fieldValue));\n        };\n        const validationModeBeforeSubmit = getValidationModes(_options.mode);\n        const validationModeAfterSubmit = getValidationModes(_options.reValidateMode);\n        if (field) {\n            let error;\n            let isValid;\n            const fieldValue = target.type ? getFieldValue(field._f) : getEventValue(event);\n            const isBlurEvent = event.type === EVENTS.BLUR || event.type === EVENTS.FOCUS_OUT;\n            const shouldSkipValidation = !hasValidation(field._f) && !_options.resolver && !get(_formState.errors, name) && !field._f.deps || skipValidation(isBlurEvent, get(_formState.touchedFields, name), _formState.isSubmitted, validationModeAfterSubmit, validationModeBeforeSubmit);\n            const watched = isWatched(name, _names, isBlurEvent);\n            set(_formValues, name, fieldValue);\n            if (isBlurEvent) {\n                field._f.onBlur && field._f.onBlur(event);\n                delayErrorCallback && delayErrorCallback(0);\n            } else if (field._f.onChange) {\n                field._f.onChange(event);\n            }\n            const fieldState = updateTouchAndDirty(name, fieldValue, isBlurEvent);\n            const shouldRender = !isEmptyObject(fieldState) || watched;\n            !isBlurEvent && _subjects.state.next({\n                name,\n                type: event.type,\n                values: cloneObject(_formValues)\n            });\n            if (shouldSkipValidation) {\n                if (_proxyFormState.isValid || _proxySubscribeFormState.isValid) {\n                    if (_options.mode === \"onBlur\") {\n                        if (isBlurEvent) {\n                            _setValid();\n                        }\n                    } else if (!isBlurEvent) {\n                        _setValid();\n                    }\n                }\n                return shouldRender && _subjects.state.next({\n                    name,\n                    ...watched ? {} : fieldState\n                });\n            }\n            !isBlurEvent && watched && _subjects.state.next({\n                ..._formState\n            });\n            if (_options.resolver) {\n                const { errors } = await _runSchema([\n                    name\n                ]);\n                _updateIsFieldValueUpdated(fieldValue);\n                if (isFieldValueUpdated) {\n                    const previousErrorLookupResult = schemaErrorLookup(_formState.errors, _fields, name);\n                    const errorLookupResult = schemaErrorLookup(errors, _fields, previousErrorLookupResult.name || name);\n                    error = errorLookupResult.error;\n                    name = errorLookupResult.name;\n                    isValid = isEmptyObject(errors);\n                }\n            } else {\n                _updateIsValidating([\n                    name\n                ], true);\n                error = (await validateField(field, _names.disabled, _formValues, shouldDisplayAllAssociatedErrors, _options.shouldUseNativeValidation))[name];\n                _updateIsValidating([\n                    name\n                ]);\n                _updateIsFieldValueUpdated(fieldValue);\n                if (isFieldValueUpdated) {\n                    if (error) {\n                        isValid = false;\n                    } else if (_proxyFormState.isValid || _proxySubscribeFormState.isValid) {\n                        isValid = await executeBuiltInValidation(_fields, true);\n                    }\n                }\n            }\n            if (isFieldValueUpdated) {\n                field._f.deps && trigger(field._f.deps);\n                shouldRenderByError(name, isValid, error, fieldState);\n            }\n        }\n    };\n    const _focusInput = (ref, key)=>{\n        if (get(_formState.errors, key) && ref.focus) {\n            ref.focus();\n            return 1;\n        }\n        return;\n    };\n    const trigger = async (name, options = {})=>{\n        let isValid;\n        let validationResult;\n        const fieldNames = convertToArrayPayload(name);\n        if (_options.resolver) {\n            const errors = await executeSchemaAndUpdateState(isUndefined(name) ? name : fieldNames);\n            isValid = isEmptyObject(errors);\n            validationResult = name ? !fieldNames.some((name)=>get(errors, name)) : isValid;\n        } else if (name) {\n            validationResult = (await Promise.all(fieldNames.map(async (fieldName)=>{\n                const field = get(_fields, fieldName);\n                return await executeBuiltInValidation(field && field._f ? {\n                    [fieldName]: field\n                } : field);\n            }))).every(Boolean);\n            !(!validationResult && !_formState.isValid) && _setValid();\n        } else {\n            validationResult = isValid = await executeBuiltInValidation(_fields);\n        }\n        _subjects.state.next({\n            ...!isString(name) || (_proxyFormState.isValid || _proxySubscribeFormState.isValid) && isValid !== _formState.isValid ? {} : {\n                name\n            },\n            ..._options.resolver || !name ? {\n                isValid\n            } : {},\n            errors: _formState.errors\n        });\n        options.shouldFocus && !validationResult && iterateFieldsByAction(_fields, _focusInput, name ? fieldNames : _names.mount);\n        return validationResult;\n    };\n    const getValues = (fieldNames)=>{\n        const values = {\n            ..._state.mount ? _formValues : _defaultValues\n        };\n        return isUndefined(fieldNames) ? values : isString(fieldNames) ? get(values, fieldNames) : fieldNames.map((name)=>get(values, name));\n    };\n    const getFieldState = (name, formState)=>({\n            invalid: !!get((formState || _formState).errors, name),\n            isDirty: !!get((formState || _formState).dirtyFields, name),\n            error: get((formState || _formState).errors, name),\n            isValidating: !!get(_formState.validatingFields, name),\n            isTouched: !!get((formState || _formState).touchedFields, name)\n        });\n    const clearErrors = (name)=>{\n        name && convertToArrayPayload(name).forEach((inputName)=>unset(_formState.errors, inputName));\n        _subjects.state.next({\n            errors: name ? _formState.errors : {}\n        });\n    };\n    const setError = (name, error, options)=>{\n        const ref = (get(_fields, name, {\n            _f: {}\n        })._f || {}).ref;\n        const currentError = get(_formState.errors, name) || {};\n        // Don't override existing error messages elsewhere in the object tree.\n        const { ref: currentRef, message, type, ...restOfErrorTree } = currentError;\n        set(_formState.errors, name, {\n            ...restOfErrorTree,\n            ...error,\n            ref\n        });\n        _subjects.state.next({\n            name,\n            errors: _formState.errors,\n            isValid: false\n        });\n        options && options.shouldFocus && ref && ref.focus && ref.focus();\n    };\n    const watch = (name, defaultValue)=>isFunction(name) ? _subjects.state.subscribe({\n            next: (payload)=>name(_getWatch(undefined, defaultValue), payload)\n        }) : _getWatch(name, defaultValue, true);\n    const _subscribe = (props)=>_subjects.state.subscribe({\n            next: (formState)=>{\n                if (shouldSubscribeByName(props.name, formState.name, props.exact) && shouldRenderFormState(formState, props.formState || _proxyFormState, _setFormState, props.reRenderRoot)) {\n                    props.callback({\n                        values: {\n                            ..._formValues\n                        },\n                        ..._formState,\n                        ...formState\n                    });\n                }\n            }\n        }).unsubscribe;\n    const subscribe = (props)=>{\n        _state.mount = true;\n        _proxySubscribeFormState = {\n            ..._proxySubscribeFormState,\n            ...props.formState\n        };\n        return _subscribe({\n            ...props,\n            formState: _proxySubscribeFormState\n        });\n    };\n    const unregister = (name, options = {})=>{\n        for (const fieldName of name ? convertToArrayPayload(name) : _names.mount){\n            _names.mount.delete(fieldName);\n            _names.array.delete(fieldName);\n            if (!options.keepValue) {\n                unset(_fields, fieldName);\n                unset(_formValues, fieldName);\n            }\n            !options.keepError && unset(_formState.errors, fieldName);\n            !options.keepDirty && unset(_formState.dirtyFields, fieldName);\n            !options.keepTouched && unset(_formState.touchedFields, fieldName);\n            !options.keepIsValidating && unset(_formState.validatingFields, fieldName);\n            !_options.shouldUnregister && !options.keepDefaultValue && unset(_defaultValues, fieldName);\n        }\n        _subjects.state.next({\n            values: cloneObject(_formValues)\n        });\n        _subjects.state.next({\n            ..._formState,\n            ...!options.keepDirty ? {} : {\n                isDirty: _getDirty()\n            }\n        });\n        !options.keepIsValid && _setValid();\n    };\n    const _setDisabledField = ({ disabled, name })=>{\n        if (isBoolean(disabled) && _state.mount || !!disabled || _names.disabled.has(name)) {\n            disabled ? _names.disabled.add(name) : _names.disabled.delete(name);\n        }\n    };\n    const register = (name, options = {})=>{\n        let field = get(_fields, name);\n        const disabledIsDefined = isBoolean(options.disabled) || isBoolean(_options.disabled);\n        set(_fields, name, {\n            ...field || {},\n            _f: {\n                ...field && field._f ? field._f : {\n                    ref: {\n                        name\n                    }\n                },\n                name,\n                mount: true,\n                ...options\n            }\n        });\n        _names.mount.add(name);\n        if (field) {\n            _setDisabledField({\n                disabled: isBoolean(options.disabled) ? options.disabled : _options.disabled,\n                name\n            });\n        } else {\n            updateValidAndValue(name, true, options.value);\n        }\n        return {\n            ...disabledIsDefined ? {\n                disabled: options.disabled || _options.disabled\n            } : {},\n            ..._options.progressive ? {\n                required: !!options.required,\n                min: getRuleValue(options.min),\n                max: getRuleValue(options.max),\n                minLength: getRuleValue(options.minLength),\n                maxLength: getRuleValue(options.maxLength),\n                pattern: getRuleValue(options.pattern)\n            } : {},\n            name,\n            onChange,\n            onBlur: onChange,\n            ref: (ref)=>{\n                if (ref) {\n                    register(name, options);\n                    field = get(_fields, name);\n                    const fieldRef = isUndefined(ref.value) ? ref.querySelectorAll ? ref.querySelectorAll(\"input,select,textarea\")[0] || ref : ref : ref;\n                    const radioOrCheckbox = isRadioOrCheckbox(fieldRef);\n                    const refs = field._f.refs || [];\n                    if (radioOrCheckbox ? refs.find((option)=>option === fieldRef) : fieldRef === field._f.ref) {\n                        return;\n                    }\n                    set(_fields, name, {\n                        _f: {\n                            ...field._f,\n                            ...radioOrCheckbox ? {\n                                refs: [\n                                    ...refs.filter(live),\n                                    fieldRef,\n                                    ...Array.isArray(get(_defaultValues, name)) ? [\n                                        {}\n                                    ] : []\n                                ],\n                                ref: {\n                                    type: fieldRef.type,\n                                    name\n                                }\n                            } : {\n                                ref: fieldRef\n                            }\n                        }\n                    });\n                    updateValidAndValue(name, false, undefined, fieldRef);\n                } else {\n                    field = get(_fields, name, {});\n                    if (field._f) {\n                        field._f.mount = false;\n                    }\n                    (_options.shouldUnregister || options.shouldUnregister) && !(isNameInFieldArray(_names.array, name) && _state.action) && _names.unMount.add(name);\n                }\n            }\n        };\n    };\n    const _focusError = ()=>_options.shouldFocusError && iterateFieldsByAction(_fields, _focusInput, _names.mount);\n    const _disableForm = (disabled)=>{\n        if (isBoolean(disabled)) {\n            _subjects.state.next({\n                disabled\n            });\n            iterateFieldsByAction(_fields, (ref, name)=>{\n                const currentField = get(_fields, name);\n                if (currentField) {\n                    ref.disabled = currentField._f.disabled || disabled;\n                    if (Array.isArray(currentField._f.refs)) {\n                        currentField._f.refs.forEach((inputRef)=>{\n                            inputRef.disabled = currentField._f.disabled || disabled;\n                        });\n                    }\n                }\n            }, 0, false);\n        }\n    };\n    const handleSubmit = (onValid, onInvalid)=>async (e)=>{\n            let onValidError = undefined;\n            if (e) {\n                e.preventDefault && e.preventDefault();\n                e.persist && e.persist();\n            }\n            let fieldValues = cloneObject(_formValues);\n            _subjects.state.next({\n                isSubmitting: true\n            });\n            if (_options.resolver) {\n                const { errors, values } = await _runSchema();\n                _formState.errors = errors;\n                fieldValues = values;\n            } else {\n                await executeBuiltInValidation(_fields);\n            }\n            if (_names.disabled.size) {\n                for (const name of _names.disabled){\n                    set(fieldValues, name, undefined);\n                }\n            }\n            unset(_formState.errors, \"root\");\n            if (isEmptyObject(_formState.errors)) {\n                _subjects.state.next({\n                    errors: {}\n                });\n                try {\n                    await onValid(fieldValues, e);\n                } catch (error) {\n                    onValidError = error;\n                }\n            } else {\n                if (onInvalid) {\n                    await onInvalid({\n                        ..._formState.errors\n                    }, e);\n                }\n                _focusError();\n                setTimeout(_focusError);\n            }\n            _subjects.state.next({\n                isSubmitted: true,\n                isSubmitting: false,\n                isSubmitSuccessful: isEmptyObject(_formState.errors) && !onValidError,\n                submitCount: _formState.submitCount + 1,\n                errors: _formState.errors\n            });\n            if (onValidError) {\n                throw onValidError;\n            }\n        };\n    const resetField = (name, options = {})=>{\n        if (get(_fields, name)) {\n            if (isUndefined(options.defaultValue)) {\n                setValue(name, cloneObject(get(_defaultValues, name)));\n            } else {\n                setValue(name, options.defaultValue);\n                set(_defaultValues, name, cloneObject(options.defaultValue));\n            }\n            if (!options.keepTouched) {\n                unset(_formState.touchedFields, name);\n            }\n            if (!options.keepDirty) {\n                unset(_formState.dirtyFields, name);\n                _formState.isDirty = options.defaultValue ? _getDirty(name, cloneObject(get(_defaultValues, name))) : _getDirty();\n            }\n            if (!options.keepError) {\n                unset(_formState.errors, name);\n                _proxyFormState.isValid && _setValid();\n            }\n            _subjects.state.next({\n                ..._formState\n            });\n        }\n    };\n    const _reset = (formValues, keepStateOptions = {})=>{\n        const updatedValues = formValues ? cloneObject(formValues) : _defaultValues;\n        const cloneUpdatedValues = cloneObject(updatedValues);\n        const isEmptyResetValues = isEmptyObject(formValues);\n        const values = isEmptyResetValues ? _defaultValues : cloneUpdatedValues;\n        if (!keepStateOptions.keepDefaultValues) {\n            _defaultValues = updatedValues;\n        }\n        if (!keepStateOptions.keepValues) {\n            if (keepStateOptions.keepDirtyValues) {\n                const fieldsToCheck = new Set([\n                    ..._names.mount,\n                    ...Object.keys(getDirtyFields(_defaultValues, _formValues))\n                ]);\n                for (const fieldName of Array.from(fieldsToCheck)){\n                    get(_formState.dirtyFields, fieldName) ? set(values, fieldName, get(_formValues, fieldName)) : setValue(fieldName, get(values, fieldName));\n                }\n            } else {\n                if (isWeb && isUndefined(formValues)) {\n                    for (const name of _names.mount){\n                        const field = get(_fields, name);\n                        if (field && field._f) {\n                            const fieldReference = Array.isArray(field._f.refs) ? field._f.refs[0] : field._f.ref;\n                            if (isHTMLElement(fieldReference)) {\n                                const form = fieldReference.closest(\"form\");\n                                if (form) {\n                                    form.reset();\n                                    break;\n                                }\n                            }\n                        }\n                    }\n                }\n                for (const fieldName of _names.mount){\n                    setValue(fieldName, get(values, fieldName));\n                }\n            }\n            _formValues = cloneObject(values);\n            _subjects.array.next({\n                values: {\n                    ...values\n                }\n            });\n            _subjects.state.next({\n                values: {\n                    ...values\n                }\n            });\n        }\n        _names = {\n            mount: keepStateOptions.keepDirtyValues ? _names.mount : new Set(),\n            unMount: new Set(),\n            array: new Set(),\n            disabled: new Set(),\n            watch: new Set(),\n            watchAll: false,\n            focus: \"\"\n        };\n        _state.mount = !_proxyFormState.isValid || !!keepStateOptions.keepIsValid || !!keepStateOptions.keepDirtyValues;\n        _state.watch = !!_options.shouldUnregister;\n        _subjects.state.next({\n            submitCount: keepStateOptions.keepSubmitCount ? _formState.submitCount : 0,\n            isDirty: isEmptyResetValues ? false : keepStateOptions.keepDirty ? _formState.isDirty : !!(keepStateOptions.keepDefaultValues && !deepEqual(formValues, _defaultValues)),\n            isSubmitted: keepStateOptions.keepIsSubmitted ? _formState.isSubmitted : false,\n            dirtyFields: isEmptyResetValues ? {} : keepStateOptions.keepDirtyValues ? keepStateOptions.keepDefaultValues && _formValues ? getDirtyFields(_defaultValues, _formValues) : _formState.dirtyFields : keepStateOptions.keepDefaultValues && formValues ? getDirtyFields(_defaultValues, formValues) : keepStateOptions.keepDirty ? _formState.dirtyFields : {},\n            touchedFields: keepStateOptions.keepTouched ? _formState.touchedFields : {},\n            errors: keepStateOptions.keepErrors ? _formState.errors : {},\n            isSubmitSuccessful: keepStateOptions.keepIsSubmitSuccessful ? _formState.isSubmitSuccessful : false,\n            isSubmitting: false\n        });\n    };\n    const reset = (formValues, keepStateOptions)=>_reset(isFunction(formValues) ? formValues(_formValues) : formValues, keepStateOptions);\n    const setFocus = (name, options = {})=>{\n        const field = get(_fields, name);\n        const fieldReference = field && field._f;\n        if (fieldReference) {\n            const fieldRef = fieldReference.refs ? fieldReference.refs[0] : fieldReference.ref;\n            if (fieldRef.focus) {\n                fieldRef.focus();\n                options.shouldSelect && isFunction(fieldRef.select) && fieldRef.select();\n            }\n        }\n    };\n    const _setFormState = (updatedFormState)=>{\n        _formState = {\n            ..._formState,\n            ...updatedFormState\n        };\n    };\n    const _resetDefaultValues = ()=>isFunction(_options.defaultValues) && _options.defaultValues().then((values)=>{\n            reset(values, _options.resetOptions);\n            _subjects.state.next({\n                isLoading: false\n            });\n        });\n    const methods = {\n        control: {\n            register,\n            unregister,\n            getFieldState,\n            handleSubmit,\n            setError,\n            _subscribe,\n            _runSchema,\n            _focusError,\n            _getWatch,\n            _getDirty,\n            _setValid,\n            _setFieldArray,\n            _setDisabledField,\n            _setErrors,\n            _getFieldArray,\n            _reset,\n            _resetDefaultValues,\n            _removeUnmounted,\n            _disableForm,\n            _subjects,\n            _proxyFormState,\n            get _fields () {\n                return _fields;\n            },\n            get _formValues () {\n                return _formValues;\n            },\n            get _state () {\n                return _state;\n            },\n            set _state (value){\n                _state = value;\n            },\n            get _defaultValues () {\n                return _defaultValues;\n            },\n            get _names () {\n                return _names;\n            },\n            set _names (value){\n                _names = value;\n            },\n            get _formState () {\n                return _formState;\n            },\n            get _options () {\n                return _options;\n            },\n            set _options (value){\n                _options = {\n                    ..._options,\n                    ...value\n                };\n            }\n        },\n        subscribe,\n        trigger,\n        register,\n        handleSubmit,\n        watch,\n        setValue,\n        getValues,\n        reset,\n        resetField,\n        clearErrors,\n        unregister,\n        setError,\n        setFocus,\n        getFieldState\n    };\n    return {\n        ...methods,\n        formControl: methods\n    };\n}\nvar generateId = ()=>{\n    const d = typeof performance === \"undefined\" ? Date.now() : performance.now() * 1000;\n    return \"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx\".replace(/[xy]/g, (c)=>{\n        const r = (Math.random() * 16 + d) % 16 | 0;\n        return (c == \"x\" ? r : r & 0x3 | 0x8).toString(16);\n    });\n};\nvar getFocusFieldName = (name, index, options = {})=>options.shouldFocus || isUndefined(options.shouldFocus) ? options.focusName || `${name}.${isUndefined(options.focusIndex) ? index : options.focusIndex}.` : \"\";\nvar appendAt = (data, value1)=>[\n        ...data,\n        ...convertToArrayPayload(value1)\n    ];\nvar fillEmptyArray = (value1)=>Array.isArray(value1) ? value1.map(()=>undefined) : undefined;\nfunction insert(data, index, value1) {\n    return [\n        ...data.slice(0, index),\n        ...convertToArrayPayload(value1),\n        ...data.slice(index)\n    ];\n}\nvar moveArrayAt = (data, from, to)=>{\n    if (!Array.isArray(data)) {\n        return [];\n    }\n    if (isUndefined(data[to])) {\n        data[to] = undefined;\n    }\n    data.splice(to, 0, data.splice(from, 1)[0]);\n    return data;\n};\nvar prependAt = (data, value1)=>[\n        ...convertToArrayPayload(value1),\n        ...convertToArrayPayload(data)\n    ];\nfunction removeAtIndexes(data, indexes) {\n    let i = 0;\n    const temp = [\n        ...data\n    ];\n    for (const index of indexes){\n        temp.splice(index - i, 1);\n        i++;\n    }\n    return compact(temp).length ? temp : [];\n}\nvar removeArrayAt = (data, index)=>isUndefined(index) ? [] : removeAtIndexes(data, convertToArrayPayload(index).sort((a, b)=>a - b));\nvar swapArrayAt = (data, indexA, indexB)=>{\n    [data[indexA], data[indexB]] = [\n        data[indexB],\n        data[indexA]\n    ];\n};\nvar updateAt = (fieldValues, index, value1)=>{\n    fieldValues[index] = value1;\n    return fieldValues;\n};\n/**\n * A custom hook that exposes convenient methods to perform operations with a list of dynamic inputs that need to be appended, updated, removed etc. • [Demo](https://codesandbox.io/s/react-hook-form-usefieldarray-ssugn) • [Video](https://youtu.be/4MrbfGSFY2A)\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usefieldarray) • [Demo](https://codesandbox.io/s/react-hook-form-usefieldarray-ssugn)\n *\n * @param props - useFieldArray props\n *\n * @returns methods - functions to manipulate with the Field Arrays (dynamic inputs) {@link UseFieldArrayReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, control, handleSubmit, reset, trigger, setError } = useForm({\n *     defaultValues: {\n *       test: []\n *     }\n *   });\n *   const { fields, append } = useFieldArray({\n *     control,\n *     name: \"test\"\n *   });\n *\n *   return (\n *     <form onSubmit={handleSubmit(data => console.log(data))}>\n *       {fields.map((item, index) => (\n *          <input key={item.id} {...register(`test.${index}.firstName`)}  />\n *       ))}\n *       <button type=\"button\" onClick={() => append({ firstName: \"bill\" })}>\n *         append\n *       </button>\n *       <input type=\"submit\" />\n *     </form>\n *   );\n * }\n * ```\n */ function useFieldArray(props) {\n    const methods = useFormContext();\n    const { control = methods.control, name, keyName = \"id\", shouldUnregister, rules } = props;\n    const [fields, setFields] = react__WEBPACK_IMPORTED_MODULE_0__.useState(control._getFieldArray(name));\n    const ids = react__WEBPACK_IMPORTED_MODULE_0__.useRef(control._getFieldArray(name).map(generateId));\n    const _fieldIds = react__WEBPACK_IMPORTED_MODULE_0__.useRef(fields);\n    const _name = react__WEBPACK_IMPORTED_MODULE_0__.useRef(name);\n    const _actioned = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    _name.current = name;\n    _fieldIds.current = fields;\n    control._names.array.add(name);\n    rules && control.register(name, rules);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>control._subjects.array.subscribe({\n            next: ({ values, name: fieldArrayName })=>{\n                if (fieldArrayName === _name.current || !fieldArrayName) {\n                    const fieldValues = get(values, _name.current);\n                    if (Array.isArray(fieldValues)) {\n                        setFields(fieldValues);\n                        ids.current = fieldValues.map(generateId);\n                    }\n                }\n            }\n        }).unsubscribe, [\n        control\n    ]);\n    const updateValues = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((updatedFieldArrayValues)=>{\n        _actioned.current = true;\n        control._setFieldArray(name, updatedFieldArrayValues);\n    }, [\n        control,\n        name\n    ]);\n    const append = (value1, options)=>{\n        const appendValue = convertToArrayPayload(cloneObject(value1));\n        const updatedFieldArrayValues = appendAt(control._getFieldArray(name), appendValue);\n        control._names.focus = getFocusFieldName(name, updatedFieldArrayValues.length - 1, options);\n        ids.current = appendAt(ids.current, appendValue.map(generateId));\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._setFieldArray(name, updatedFieldArrayValues, appendAt, {\n            argA: fillEmptyArray(value1)\n        });\n    };\n    const prepend = (value1, options)=>{\n        const prependValue = convertToArrayPayload(cloneObject(value1));\n        const updatedFieldArrayValues = prependAt(control._getFieldArray(name), prependValue);\n        control._names.focus = getFocusFieldName(name, 0, options);\n        ids.current = prependAt(ids.current, prependValue.map(generateId));\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._setFieldArray(name, updatedFieldArrayValues, prependAt, {\n            argA: fillEmptyArray(value1)\n        });\n    };\n    const remove = (index)=>{\n        const updatedFieldArrayValues = removeArrayAt(control._getFieldArray(name), index);\n        ids.current = removeArrayAt(ids.current, index);\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        !Array.isArray(get(control._fields, name)) && set(control._fields, name, undefined);\n        control._setFieldArray(name, updatedFieldArrayValues, removeArrayAt, {\n            argA: index\n        });\n    };\n    const insert$1 = (index, value1, options)=>{\n        const insertValue = convertToArrayPayload(cloneObject(value1));\n        const updatedFieldArrayValues = insert(control._getFieldArray(name), index, insertValue);\n        control._names.focus = getFocusFieldName(name, index, options);\n        ids.current = insert(ids.current, index, insertValue.map(generateId));\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._setFieldArray(name, updatedFieldArrayValues, insert, {\n            argA: index,\n            argB: fillEmptyArray(value1)\n        });\n    };\n    const swap = (indexA, indexB)=>{\n        const updatedFieldArrayValues = control._getFieldArray(name);\n        swapArrayAt(updatedFieldArrayValues, indexA, indexB);\n        swapArrayAt(ids.current, indexA, indexB);\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._setFieldArray(name, updatedFieldArrayValues, swapArrayAt, {\n            argA: indexA,\n            argB: indexB\n        }, false);\n    };\n    const move = (from, to)=>{\n        const updatedFieldArrayValues = control._getFieldArray(name);\n        moveArrayAt(updatedFieldArrayValues, from, to);\n        moveArrayAt(ids.current, from, to);\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._setFieldArray(name, updatedFieldArrayValues, moveArrayAt, {\n            argA: from,\n            argB: to\n        }, false);\n    };\n    const update = (index, value1)=>{\n        const updateValue = cloneObject(value1);\n        const updatedFieldArrayValues = updateAt(control._getFieldArray(name), index, updateValue);\n        ids.current = [\n            ...updatedFieldArrayValues\n        ].map((item, i)=>!item || i === index ? generateId() : ids.current[i]);\n        updateValues(updatedFieldArrayValues);\n        setFields([\n            ...updatedFieldArrayValues\n        ]);\n        control._setFieldArray(name, updatedFieldArrayValues, updateAt, {\n            argA: index,\n            argB: updateValue\n        }, true, false);\n    };\n    const replace = (value1)=>{\n        const updatedFieldArrayValues = convertToArrayPayload(cloneObject(value1));\n        ids.current = updatedFieldArrayValues.map(generateId);\n        updateValues([\n            ...updatedFieldArrayValues\n        ]);\n        setFields([\n            ...updatedFieldArrayValues\n        ]);\n        control._setFieldArray(name, [\n            ...updatedFieldArrayValues\n        ], (data)=>data, {}, true, false);\n    };\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        control._state.action = false;\n        isWatched(name, control._names) && control._subjects.state.next({\n            ...control._formState\n        });\n        if (_actioned.current && (!getValidationModes(control._options.mode).isOnSubmit || control._formState.isSubmitted) && !getValidationModes(control._options.reValidateMode).isOnSubmit) {\n            if (control._options.resolver) {\n                control._runSchema([\n                    name\n                ]).then((result)=>{\n                    const error = get(result.errors, name);\n                    const existingError = get(control._formState.errors, name);\n                    if (existingError ? !error && existingError.type || error && (existingError.type !== error.type || existingError.message !== error.message) : error && error.type) {\n                        error ? set(control._formState.errors, name, error) : unset(control._formState.errors, name);\n                        control._subjects.state.next({\n                            errors: control._formState.errors\n                        });\n                    }\n                });\n            } else {\n                const field = get(control._fields, name);\n                if (field && field._f && !(getValidationModes(control._options.reValidateMode).isOnSubmit && getValidationModes(control._options.mode).isOnSubmit)) {\n                    validateField(field, control._names.disabled, control._formValues, control._options.criteriaMode === VALIDATION_MODE.all, control._options.shouldUseNativeValidation, true).then((error)=>!isEmptyObject(error) && control._subjects.state.next({\n                            errors: updateFieldArrayRootError(control._formState.errors, error, name)\n                        }));\n                }\n            }\n        }\n        control._subjects.state.next({\n            name,\n            values: cloneObject(control._formValues)\n        });\n        control._names.focus && iterateFieldsByAction(control._fields, (ref, key)=>{\n            if (control._names.focus && key.startsWith(control._names.focus) && ref.focus) {\n                ref.focus();\n                return 1;\n            }\n            return;\n        });\n        control._names.focus = \"\";\n        control._setValid();\n        _actioned.current = false;\n    }, [\n        fields,\n        name,\n        control\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        !get(control._formValues, name) && control._setFieldArray(name);\n        return ()=>{\n            const updateMounted = (name, value1)=>{\n                const field = get(control._fields, name);\n                if (field && field._f) {\n                    field._f.mount = value1;\n                }\n            };\n            control._options.shouldUnregister || shouldUnregister ? control.unregister(name) : updateMounted(name, false);\n        };\n    }, [\n        name,\n        control,\n        keyName,\n        shouldUnregister\n    ]);\n    return {\n        swap: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(swap, [\n            updateValues,\n            name,\n            control\n        ]),\n        move: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(move, [\n            updateValues,\n            name,\n            control\n        ]),\n        prepend: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(prepend, [\n            updateValues,\n            name,\n            control\n        ]),\n        append: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(append, [\n            updateValues,\n            name,\n            control\n        ]),\n        remove: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(remove, [\n            updateValues,\n            name,\n            control\n        ]),\n        insert: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(insert$1, [\n            updateValues,\n            name,\n            control\n        ]),\n        update: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(update, [\n            updateValues,\n            name,\n            control\n        ]),\n        replace: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(replace, [\n            updateValues,\n            name,\n            control\n        ]),\n        fields: react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>fields.map((field, index)=>({\n                    ...field,\n                    [keyName]: ids.current[index] || generateId()\n                })), [\n            fields,\n            keyName\n        ])\n    };\n}\n/**\n * Custom hook to manage the entire form.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useform) • [Demo](https://codesandbox.io/s/react-hook-form-get-started-ts-5ksmm) • [Video](https://www.youtube.com/watch?v=RkXv4AXXC_4)\n *\n * @param props - form configuration and validation parameters.\n *\n * @returns methods - individual functions to manage the form state. {@link UseFormReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, handleSubmit, watch, formState: { errors } } = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   console.log(watch(\"example\"));\n *\n *   return (\n *     <form onSubmit={handleSubmit(onSubmit)}>\n *       <input defaultValue=\"test\" {...register(\"example\")} />\n *       <input {...register(\"exampleRequired\", { required: true })} />\n *       {errors.exampleRequired && <span>This field is required</span>}\n *       <button>Submit</button>\n *     </form>\n *   );\n * }\n * ```\n */ function useForm(props = {}) {\n    const _formControl = react__WEBPACK_IMPORTED_MODULE_0__.useRef(undefined);\n    const _values = react__WEBPACK_IMPORTED_MODULE_0__.useRef(undefined);\n    const [formState, updateFormState] = react__WEBPACK_IMPORTED_MODULE_0__.useState({\n        isDirty: false,\n        isValidating: false,\n        isLoading: isFunction(props.defaultValues),\n        isSubmitted: false,\n        isSubmitting: false,\n        isSubmitSuccessful: false,\n        isValid: false,\n        submitCount: 0,\n        dirtyFields: {},\n        touchedFields: {},\n        validatingFields: {},\n        errors: props.errors || {},\n        disabled: props.disabled || false,\n        isReady: false,\n        defaultValues: isFunction(props.defaultValues) ? undefined : props.defaultValues\n    });\n    if (!_formControl.current) {\n        _formControl.current = {\n            ...props.formControl ? props.formControl : createFormControl(props),\n            formState\n        };\n        if (props.formControl && props.defaultValues && !isFunction(props.defaultValues)) {\n            props.formControl.reset(props.defaultValues, props.resetOptions);\n        }\n    }\n    const control = _formControl.current.control;\n    control._options = props;\n    useIsomorphicLayoutEffect(()=>{\n        const sub = control._subscribe({\n            formState: control._proxyFormState,\n            callback: ()=>updateFormState({\n                    ...control._formState\n                }),\n            reRenderRoot: true\n        });\n        updateFormState((data)=>({\n                ...data,\n                isReady: true\n            }));\n        control._formState.isReady = true;\n        return sub;\n    }, [\n        control\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>control._disableForm(props.disabled), [\n        control,\n        props.disabled\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (props.mode) {\n            control._options.mode = props.mode;\n        }\n        if (props.reValidateMode) {\n            control._options.reValidateMode = props.reValidateMode;\n        }\n    }, [\n        control,\n        props.mode,\n        props.reValidateMode\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (props.errors) {\n            control._setErrors(props.errors);\n            control._focusError();\n        }\n    }, [\n        control,\n        props.errors\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        props.shouldUnregister && control._subjects.state.next({\n            values: control._getWatch()\n        });\n    }, [\n        control,\n        props.shouldUnregister\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (control._proxyFormState.isDirty) {\n            const isDirty = control._getDirty();\n            if (isDirty !== formState.isDirty) {\n                control._subjects.state.next({\n                    isDirty\n                });\n            }\n        }\n    }, [\n        control,\n        formState.isDirty\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (props.values && !deepEqual(props.values, _values.current)) {\n            control._reset(props.values, control._options.resetOptions);\n            _values.current = props.values;\n            updateFormState((state)=>({\n                    ...state\n                }));\n        } else {\n            control._resetDefaultValues();\n        }\n    }, [\n        control,\n        props.values\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (!control._state.mount) {\n            control._setValid();\n            control._state.mount = true;\n        }\n        if (control._state.watch) {\n            control._state.watch = false;\n            control._subjects.state.next({\n                ...control._formState\n            });\n        }\n        control._removeUnmounted();\n    });\n    _formControl.current.formState = getProxyFormState(formState, control);\n    return _formControl.current;\n}\n //# sourceMappingURL=index.esm.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-hook-form/dist/index.esm.mjs\n");

/***/ })

};
;